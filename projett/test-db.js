// Test database connection directly
const mysql = require('mysql2/promise');
const fs = require('fs');
const path = require('path');

// Read the .env file manually
const envPath = path.join(__dirname, 'backend', '.env');
const envContent = fs.readFileSync(envPath, 'utf8');

// Parse the .env file
const envVars = {};
envContent.split('\n').forEach(line => {
  const parts = line.split('=');
  if (parts.length === 2) {
    envVars[parts[0].trim()] = parts[1].trim();
  }
});

// Log the database configuration
console.log('Database configuration:');
console.log('Host:', envVars.DB_HOST);
console.log('Port:', envVars.DB_PORT);
console.log('Database:', envVars.DB_DATABASE);
console.log('Username:', envVars.DB_USERNAME);
console.log('Password:', envVars.DB_PASSWORD ? '******' : 'empty');

async function testConnection() {
  try {
    // Create a connection
    const connection = await mysql.createConnection({
      host: envVars.DB_HOST,
      port: envVars.DB_PORT,
      user: envVars.DB_USERNAME,
      password: envVars.DB_PASSWORD,
      database: envVars.DB_DATABASE
    });

    console.log('Connected to the database!');

    // Test a simple query
    const [rows] = await connection.execute('SELECT 1 as test');
    console.log('Query result:', rows);

    // Test a query on the likes table
    try {
      const [likes] = await connection.execute('SELECT * FROM likes LIMIT 3');
      console.log(`Found ${likes.length} likes:`, likes);
    } catch (error) {
      console.error('Error querying likes table:', error.message);
    }

    // Close the connection
    await connection.end();
    console.log('Connection closed');
  } catch (error) {
    console.error('Error connecting to the database:', error.message);
  }
}

testConnection();
