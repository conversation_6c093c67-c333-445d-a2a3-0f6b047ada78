#!/usr/bin/env python3
import os
import sys
import pandas as pd
from sqlalchemy import create_engine, text

# Configuration de la base de données
DB_HOST = os.environ.get('DB_HOST', 'localhost')
DB_PORT = os.environ.get('DB_PORT', '3306')
DB_USER = os.environ.get('DB_USER', 'root')
DB_PASSWORD = os.environ.get('DB_PASSWORD', '')
DB_NAME = os.environ.get('DB_NAME', 'abajimdb')

# Création de la connexion à la base de données
connection_string = f"mysql+pymysql://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}"
engine = create_engine(connection_string)

def check_materials():
    try:
        # Mapping entre level_id et material_ids
        manual_level_mapping = {
            6: [1, 1, 2],
            7: [3, 4, 4],
            8: [5, 5, 6, 7, 7, 8],
            9: [9, 9, 10, 11, 11, 12, 12],
            10: [13, 14, 14, 15, 16, 16, 17, 17],
            11: [18, 18, 19, 20, 21, 21, 22, 22, 23]
        }
        
        # Vérifier les matériaux pour le niveau 9
        material_ids = manual_level_mapping[9]
        print(f"Material IDs pour le niveau 9: {material_ids}")
        
        # Récupérer les informations sur ces matériaux
        materials_query = f"""
        SELECT id, name, section_id
        FROM materials
        WHERE id IN ({','.join(map(str, material_ids))})
        """
        materials_df = pd.read_sql(materials_query, engine)
        print("\n=== MATÉRIAUX POUR NIVEAU 9 ===")
        print(materials_df)
        
        # Récupérer les manuels pour ces matériaux
        manuels_query = f"""
        SELECT m.id, m.name, m.material_id, mat.name as material_name
        FROM manuels m
        JOIN materials mat ON m.material_id = mat.id
        WHERE m.material_id IN ({','.join(map(str, material_ids))})
        """
        manuels_df = pd.read_sql(manuels_query, engine)
        print("\n=== MANUELS POUR NIVEAU 9 ===")
        print(manuels_df)
        
        # Vérifier les matériaux correspondant à "رياضيات" (mathématiques)
        math_materials_query = """
        SELECT id, name, section_id
        FROM materials
        WHERE name LIKE '%رياض%'
        """
        math_materials_df = pd.read_sql(math_materials_query, engine)
        print("\n=== MATÉRIAUX POUR 'رياضيات' ===")
        print(math_materials_df)
        
        # Trouver les matériaux de mathématiques pour le niveau 9
        math_level9_materials = []
        for material_id in material_ids:
            material_query = f"""
            SELECT id, name, section_id
            FROM materials
            WHERE id = {material_id} AND name LIKE '%رياض%'
            """
            material_df = pd.read_sql(material_query, engine)
            if not material_df.empty:
                math_level9_materials.append(material_df.iloc[0])
        
        print("\n=== MATÉRIAUX DE MATHÉMATIQUES POUR NIVEAU 9 ===")
        if math_level9_materials:
            math_level9_df = pd.DataFrame(math_level9_materials)
            print(math_level9_df)
        else:
            print("Aucun matériau de mathématiques trouvé pour le niveau 9")
        
        # Vérifier les vidéos disponibles pour les manuels de mathématiques du niveau 9
        if math_level9_materials:
            math_material_ids = [m['id'] for m in math_level9_materials]
            manuel_ids_query = f"""
            SELECT id
            FROM manuels
            WHERE material_id IN ({','.join(map(str, math_material_ids))})
            """
            manuel_ids_df = pd.read_sql(manuel_ids_query, engine)
            
            if not manuel_ids_df.empty:
                manuel_ids = manuel_ids_df['id'].tolist()
                videos_query = f"""
                SELECT id, titre, manuel_id, user_id, page, status, vues, likes
                FROM videos
                WHERE manuel_id IN ({','.join(map(str, manuel_ids))})
                AND status = 'APPROVED'
                LIMIT 10
                """
                videos_df = pd.read_sql(videos_query, engine)
                print("\n=== VIDÉOS POUR MANUELS DE MATHÉMATIQUES DU NIVEAU 9 ===")
                print(videos_df)
            else:
                print("\nAucun manuel trouvé pour les matériaux de mathématiques du niveau 9")
        
    except Exception as e:
        print(f"Erreur lors de la vérification des matériaux: {e}")

if __name__ == "__main__":
    check_materials()
