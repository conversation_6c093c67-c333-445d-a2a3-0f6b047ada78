#!/usr/bin/env python3
import os
import sys
import pandas as pd
from sqlalchemy import create_engine, text
from tabulate import tabulate

# Configuration de la base de données
DB_HOST = os.environ.get('DB_HOST', 'localhost')
DB_PORT = os.environ.get('DB_PORT', '3306')
DB_USER = os.environ.get('DB_USER', 'root')
DB_PASSWORD = os.environ.get('DB_PASSWORD', '')
DB_NAME = os.environ.get('DB_NAME', 'abajimdb')

# Création de la connexion à la base de données
connection_string = f"mysql+pymysql://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}"
engine = create_engine(connection_string)

def print_table(df, title):
    """Affiche un DataFrame avec un titre"""
    print(f"\n{'=' * 80}")
    print(f"{title}")
    print(f"{'=' * 80}")
    print(tabulate(df, headers='keys', tablefmt='psql', showindex=False))
    print(f"Total rows: {len(df)}")

def explore_tables():
    try:
        # 1. Afficher la structure de la table materials
        print("\n\n📊 STRUCTURE DE LA TABLE MATERIALS")
        materials_structure = pd.read_sql("DESCRIBE materials", engine)
        print(tabulate(materials_structure, headers='keys', tablefmt='psql', showindex=False))

        # 2. Afficher la structure de la table manuels
        print("\n\n📊 STRUCTURE DE LA TABLE MANUELS")
        manuels_structure = pd.read_sql("DESCRIBE manuels", engine)
        print(tabulate(manuels_structure, headers='keys', tablefmt='psql', showindex=False))

        # 3. Afficher la structure de la table videos
        print("\n\n📊 STRUCTURE DE LA TABLE VIDEOS")
        videos_structure = pd.read_sql("DESCRIBE videos", engine)
        print(tabulate(videos_structure, headers='keys', tablefmt='psql', showindex=False))

        # 4. Afficher le contenu de la table materials
        materials_query = """
        SELECT id, name, section_id, created_at, updated_at
        FROM materials
        ORDER BY id
        """
        materials_df = pd.read_sql(materials_query, engine)
        print_table(materials_df, "📚 CONTENU DE LA TABLE MATERIALS")

        # 5. Afficher le contenu de la table manuels
        manuels_query = """
        SELECT id, name, material_id, logo, created_at, updated_at
        FROM manuels
        ORDER BY id
        """
        manuels_df = pd.read_sql(manuels_query, engine)
        print_table(manuels_df, "📚 CONTENU DE LA TABLE MANUELS")

        # 6. Afficher un échantillon de la table videos
        videos_query = """
        SELECT id, titre, description, manuel_id, user_id, page, status, vues, likes, created_at
        FROM videos
        ORDER BY id
        LIMIT 20
        """
        videos_df = pd.read_sql(videos_query, engine)
        print_table(videos_df, "🎬 ÉCHANTILLON DE LA TABLE VIDEOS (20 premières lignes)")

        # 7. Afficher les relations entre les tables
        print("\n\n🔄 RELATIONS ENTRE LES TABLES")
        
        # 7.1 Relation entre materials et manuels
        materials_manuels_query = """
        SELECT m.id as manuel_id, m.name as manuel_name, 
               mat.id as material_id, mat.name as material_name,
               mat.section_id as level_id
        FROM manuels m
        JOIN materials mat ON m.material_id = mat.id
        ORDER BY mat.section_id, mat.id, m.id
        """
        materials_manuels_df = pd.read_sql(materials_manuels_query, engine)
        print_table(materials_manuels_df, "🔄 RELATION ENTRE MATERIALS ET MANUELS")

        # 7.2 Relation entre manuels et videos
        manuels_videos_query = """
        SELECT v.id as video_id, v.titre as video_title, v.page,
               m.id as manuel_id, m.name as manuel_name,
               mat.id as material_id, mat.name as material_name,
               mat.section_id as level_id
        FROM videos v
        JOIN manuels m ON v.manuel_id = m.id
        JOIN materials mat ON m.material_id = mat.id
        ORDER BY mat.section_id, mat.id, m.id, v.id
        LIMIT 20
        """
        manuels_videos_df = pd.read_sql(manuels_videos_query, engine)
        print_table(manuels_videos_df, "🔄 RELATION ENTRE MANUELS ET VIDEOS (20 premières lignes)")

        # 8. Statistiques sur les vidéos par manuel
        videos_per_manuel_query = """
        SELECT m.id as manuel_id, m.name as manuel_name, 
               mat.id as material_id, mat.name as material_name,
               mat.section_id as level_id,
               COUNT(v.id) as video_count
        FROM manuels m
        JOIN materials mat ON m.material_id = mat.id
        LEFT JOIN videos v ON m.id = v.manuel_id
        GROUP BY m.id, m.name, mat.id, mat.name, mat.section_id
        ORDER BY mat.section_id, mat.id, m.id
        """
        videos_per_manuel_df = pd.read_sql(videos_per_manuel_query, engine)
        print_table(videos_per_manuel_df, "📊 NOMBRE DE VIDÉOS PAR MANUEL")

        # 9. Statistiques sur les vidéos par matière et niveau
        videos_per_material_level_query = """
        SELECT mat.id as material_id, mat.name as material_name,
               mat.section_id as level_id,
               COUNT(v.id) as video_count
        FROM materials mat
        JOIN manuels m ON mat.id = m.material_id
        LEFT JOIN videos v ON m.id = v.manuel_id
        GROUP BY mat.id, mat.name, mat.section_id
        ORDER BY mat.section_id, mat.id
        """
        videos_per_material_level_df = pd.read_sql(videos_per_material_level_query, engine)
        print_table(videos_per_material_level_df, "📊 NOMBRE DE VIDÉOS PAR MATIÈRE ET NIVEAU")

        # 10. Vérifier les manuels pour le niveau 9
        level9_manuels_query = """
        SELECT m.id as manuel_id, m.name as manuel_name, 
               mat.id as material_id, mat.name as material_name
        FROM manuels m
        JOIN materials mat ON m.material_id = mat.id
        WHERE mat.id IN (9, 10, 11, 12)
        ORDER BY mat.id, m.id
        """
        level9_manuels_df = pd.read_sql(level9_manuels_query, engine)
        print_table(level9_manuels_df, "📚 MANUELS POUR LE NIVEAU 9 (material_ids: 9, 10, 11, 12)")

    except Exception as e:
        print(f"Erreur lors de l'exploration des tables: {e}")

if __name__ == "__main__":
    explore_tables()
