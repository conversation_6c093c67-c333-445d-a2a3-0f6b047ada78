#!/usr/bin/env python3
import os
import sys
import pandas as pd
from sqlalchemy import create_engine, text

# Configuration de la base de données
DB_HOST = os.environ.get('DB_HOST', 'localhost')
DB_PORT = os.environ.get('DB_PORT', '3306')
DB_USER = os.environ.get('DB_USER', 'root')
DB_PASSWORD = os.environ.get('DB_PASSWORD', '')
DB_NAME = os.environ.get('DB_NAME', 'abajimdb')

# Création de la connexion à la base de données
connection_string = f"mysql+pymysql://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}"
engine = create_engine(connection_string)

def query_database():
    try:
        # Requête pour obtenir les manuels
        manuels_query = """
        SELECT id, name, material_id
        FROM manuels
        """
        manuels_df = pd.read_sql(manuels_query, engine)
        print("=== MANUELS ===")
        print(manuels_df)
        print("\n")

        # Requête pour obtenir les matières
        materials_query = """
        SELECT id, name, section_id
        FROM materials
        """
        materials_df = pd.read_sql(materials_query, engine)
        print("=== MATIÈRES ===")
        print(materials_df)
        print("\n")

        # Requête pour obtenir les manuels associés au niveau 9 et à la matière "رياضيات"
        level_matiere_query = """
        SELECT m.id as manuel_id, m.name as manuel_name,
               mat.id as material_id, mat.name as material_name,
               mat.section_id as level_id
        FROM manuels m
        JOIN materials mat ON m.material_id = mat.id
        WHERE mat.section_id = 9 AND mat.id = 9
        """
        level_matiere_df = pd.read_sql(level_matiere_query, engine)
        print("=== MANUELS POUR NIVEAU 9 ET MATIÈRE 'رياضيات' ===")
        print(level_matiere_df)
        print("\n")

        # Requête pour obtenir les vidéos associées aux manuels du niveau 9 et matière "رياضيات"
        videos_query = """
        SELECT v.id, v.titre, v.manuel_id, m.name as manuel_name,
               mat.id as material_id, mat.name as material_name,
               mat.section_id as level_id
        FROM videos v
        JOIN manuels m ON v.manuel_id = m.id
        JOIN materials mat ON m.material_id = mat.id
        WHERE mat.section_id = 9 AND mat.id = 9
        LIMIT 10
        """
        videos_df = pd.read_sql(videos_query, engine)
        print("=== VIDÉOS POUR NIVEAU 9 ET MATIÈRE 'رياضيات' ===")
        print(videos_df)
        print("\n")

        # Vérifier le mapping manuel-niveau dans le code
        manual_level_mapping = {
            6: [1, 1, 2],
            7: [3, 4, 4],
            8: [5, 5, 6, 7, 7, 8],
            9: [9, 9, 10, 11, 11, 12, 12],
            10: [13, 14, 14, 15, 16, 16, 17, 17],
            11: [18, 18, 19, 20, 21, 21, 22, 22, 23]
        }

        print("=== MAPPING MANUEL-NIVEAU DANS LE CODE ===")
        print(f"Niveau 9 -> Material IDs: {manual_level_mapping[9]}")

        # Vérifier les matériaux pour le niveau 9
        materials_level_query = """
        SELECT id, name
        FROM materials
        WHERE section_id = 9
        """
        materials_level_df = pd.read_sql(materials_level_query, engine)
        print("\n=== MATÉRIAUX POUR NIVEAU 9 ===")
        print(materials_level_df)

        # Vérifier les manuels pour les matériaux du niveau 9
        manuels_level_query = """
        SELECT m.id, m.name, m.material_id, mat.name as material_name
        FROM manuels m
        JOIN materials mat ON m.material_id = mat.id
        WHERE mat.section_id = 9
        """
        manuels_level_df = pd.read_sql(manuels_level_query, engine)
        print("\n=== MANUELS POUR NIVEAU 9 ===")
        print(manuels_level_df)

        # Vérifier spécifiquement les manuels 13 et 14
        manuels_specific_query = """
        SELECT m.id, m.name, m.material_id, mat.name as material_name, mat.section_id as level_id
        FROM manuels m
        JOIN materials mat ON m.material_id = mat.id
        WHERE m.id IN (13, 14, 19)
        """
        manuels_specific_df = pd.read_sql(manuels_specific_query, engine)
        print("\n=== MANUELS SPÉCIFIQUES (13, 14, 19) ===")
        print(manuels_specific_df)

    except Exception as e:
        print(f"Erreur lors de l'interrogation de la base de données: {e}")

if __name__ == "__main__":
    query_database()
