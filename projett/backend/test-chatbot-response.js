#!/usr/bin/env node

/**
 * Test de la réponse complète du chatbot
 */

const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '.env') });

const ChatbotMobileService = require('./public/src/services/ChatbotMobileService');

async function testChatbotResponse() {
    console.log('🤖 TEST DE LA RÉPONSE COMPLÈTE DU CHATBOT');
    console.log('='.repeat(60));
    
    try {
        // Test avec le message exact qui pose problème
        console.log('\n📋 Test: "هل يوجد درس اسمه الضرب في رقمين؟"');
        console.log('📊 Niveau: 9 (4ème année)');
        console.log('📚 Matière: رياضيات');
        
        const result = await ChatbotMobileService.voir_cours(
            9, 
            "رياضيات", 
            "هل يوجد درس اسمه الضرب في رقمين؟"
        );
        
        console.log('\n✅ RÉSULTAT COMPLET:');
        console.log('='.repeat(40));
        console.log(`Success: ${result.success}`);
        console.log(`Message: ${result.message || result.messageAr || 'Aucun message'}`);
        
        if (result.data) {
            console.log('\n📊 DONNÉES:');
            if (result.data.courses) {
                console.log(`📚 Cours trouvés: ${result.data.courses.length}`);
                result.data.courses.forEach((course, index) => {
                    console.log(`  ${index + 1}. ${course.title} (ID: ${course.id})`);
                });
            }
            if (result.data.course) {
                console.log(`📚 Cours unique: ${result.data.course.title} (ID: ${result.data.course.id})`);
            }
        }
        
        if (result.navigation) {
            console.log('\n🧭 NAVIGATION:');
            console.log(`Screen: ${result.navigation.screen}`);
            if (result.navigation.params) {
                console.log(`Params:`, JSON.stringify(result.navigation.params, null, 2));
            }
        }
        
        // Simuler le traitement du contrôleur
        console.log('\n🎭 SIMULATION DU CONTRÔLEUR:');
        console.log('='.repeat(40));
        
        let prompt = "هل يوجد درس اسمه الضرب في رقمين؟";
        let navigationInfo = null;
        
        if (typeof result === 'object' && 'navigation' in result) {
            navigationInfo = result.navigation;
            
            if (result.success) {
                if (result.navigation && result.navigation.screen === 'WebinarDetailScreen') {
                    const courseId = result.navigation.params?.webinarId;
                    prompt += `

✅ لقد وجدت الدرس المطلوب!

📌 رد بلهجة تونسية بسيطة للطفل لإخباره أنك وجدت الدرس المطلوب وسيتم عرضه له فوراً.`;
                } else if (result.data && result.data.courses && Array.isArray(result.data.courses)) {
                    const coursesList = result.data.courses.map(c => `- ${c.title || c.titre}`).join('\n');
                    prompt += `

✅ لقد وجدت هذه الدروس:
${coursesList}

📌 رد بلهجة تونسية بسيطة للطفل لإخباره أنك وجدت ${result.data.courses.length} دروس تطابق طلبه وسيتم عرض قائمة الدروس له للاختيار.`;
                } else {
                    prompt += `

✅ ${result.message || result.messageAr}

📌 رد بلهجة تونسية بسيطة للطفل بناءً على المعلومات أعلاه.`;
                }
            } else {
                prompt += `

❌ لم يتم العثور على الدرس المحدد المطلوب.

📌 اشرح للطفل باللهجة التونسية البسيطة أنك لم تجد الدرس المحدد، ولكنك ستقترح عليه دروسًا أخرى في نفس المادة قد تهمه.`;
            }
        }
        
        console.log('📝 PROMPT GÉNÉRÉ POUR GPT:');
        console.log('-'.repeat(40));
        console.log(prompt);
        console.log('-'.repeat(40));
        
        console.log('\n🧭 NAVIGATION INFO:');
        if (navigationInfo) {
            console.log(JSON.stringify(navigationInfo, null, 2));
        } else {
            console.log('Aucune information de navigation');
        }
        
        console.log('\n🎉 TEST TERMINÉ');
        
    } catch (error) {
        console.error('❌ Erreur:', error.message);
        console.error(error.stack);
    } finally {
        // Fermer la connexion
        try {
            const db = require('./public/src/models');
            if (db && db.sequelize) {
                await db.sequelize.close();
                console.log('\n🔌 Connexion fermée');
            }
        } catch (closeError) {
            console.error('⚠️ Erreur fermeture DB:', closeError.message);
        }
    }
}

// Exécuter si appelé directement
if (require.main === module) {
    testChatbotResponse().catch(console.error);
}

module.exports = { testChatbotResponse };
