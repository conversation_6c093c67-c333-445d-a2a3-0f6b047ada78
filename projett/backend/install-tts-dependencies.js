#!/usr/bin/env node

/**
 * Script d'installation des dépendances TTS pour l'arabe
 */

const { exec } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🎯 Installation des dépendances TTS pour l\'arabe...');

// Liste des packages à installer
const packages = [
    '@google-cloud/text-to-speech',  // Google Cloud TTS
    'microsoft-cognitiveservices-speech-sdk',  // Azure Speech Services
    'axios'  // Pour les services TTS en ligne
];

/**
 * Installe un package npm
 * @param {string} packageName - Nom du package
 * @returns {Promise<boolean>} - True si succès
 */
function installPackage(packageName) {
    return new Promise((resolve, reject) => {
        console.log(`📦 Installation de ${packageName}...`);
        
        exec(`npm install ${packageName}`, (error, stdout, stderr) => {
            if (error) {
                console.error(`❌ Erreur installation ${packageName}:`, error.message);
                resolve(false);
            } else {
                console.log(`✅ ${packageName} installé avec succès`);
                resolve(true);
            }
        });
    });
}

/**
 * Vérifie si un package est déjà installé
 * @param {string} packageName - Nom du package
 * @returns {boolean} - True si installé
 */
function isPackageInstalled(packageName) {
    try {
        require.resolve(packageName);
        return true;
    } catch (error) {
        return false;
    }
}

/**
 * Crée un fichier de configuration exemple
 */
function createConfigExample() {
    const configContent = `# Configuration TTS pour l'arabe
# Copiez ce fichier vers .env et remplissez les valeurs

# Google Cloud TTS (Recommandé pour l'arabe)
# 1. Créez un projet sur https://console.cloud.google.com/
# 2. Activez l'API Text-to-Speech
# 3. Créez une clé de service et téléchargez le fichier JSON
# 4. Définissez le chemin vers le fichier JSON
GOOGLE_APPLICATION_CREDENTIALS=/path/to/your/google-credentials.json

# Ou utilisez une clé API directe
GOOGLE_CLOUD_API_KEY=your_google_api_key_here

# Azure Speech Services (Alternative)
# 1. Créez un compte sur https://azure.microsoft.com/
# 2. Créez une ressource Speech Services
# 3. Copiez la clé et la région
AZURE_SPEECH_KEY=your_azure_speech_key_here
AZURE_SPEECH_REGION=your_azure_region_here

# Configuration existante
BASE_URL=https://your-ngrok-url.ngrok-free.app
ELEVENLABS_API_KEY=your_elevenlabs_key_here
`;

    const configPath = path.join(__dirname, '.env.tts.example');
    fs.writeFileSync(configPath, configContent);
    console.log(`📄 Fichier de configuration exemple créé: ${configPath}`);
}

/**
 * Vérifie les voix disponibles sur le système
 */
function checkSystemVoices() {
    return new Promise((resolve) => {
        console.log('🔍 Vérification des voix système disponibles...');
        
        exec('say -v "?"', (error, stdout, stderr) => {
            if (error) {
                console.log('⚠️ Impossible de lister les voix système');
                resolve([]);
            } else {
                const voices = stdout.split('\n')
                    .filter(line => line.trim())
                    .map(line => line.split(/\s+/)[0])
                    .filter(voice => voice);
                
                console.log(`📢 Voix système trouvées: ${voices.length}`);
                
                // Chercher des voix arabes
                const arabicVoices = voices.filter(voice => 
                    voice.toLowerCase().includes('arab') ||
                    voice.toLowerCase().includes('majed') ||
                    voice.toLowerCase().includes('tarik') ||
                    voice.toLowerCase().includes('maged')
                );
                
                if (arabicVoices.length > 0) {
                    console.log(`✅ Voix arabes trouvées: ${arabicVoices.join(', ')}`);
                } else {
                    console.log('⚠️ Aucune voix arabe trouvée sur le système');
                    console.log('💡 Vous pouvez télécharger des voix arabes depuis les Préférences Système > Accessibilité > Contenu parlé');
                }
                
                resolve(voices);
            }
        });
    });
}

/**
 * Teste la génération TTS
 */
async function testTtsGeneration() {
    console.log('🧪 Test de génération TTS...');
    
    try {
        const hybridTtsService = require('./public/src/services/hybridTtsService');
        const testText = 'مرحبا، هذا اختبار للصوت العربي';
        
        console.log('🎵 Test avec le service hybride...');
        const result = await hybridTtsService.generateBestAudio(testText);
        
        if (result) {
            console.log('✅ Test TTS réussi !');
            console.log(`🔊 Fichier généré: ${result}`);
        } else {
            console.log('⚠️ Test TTS échoué, mais les services sont installés');
        }
        
    } catch (error) {
        console.log('⚠️ Erreur lors du test TTS:', error.message);
        console.log('💡 Les dépendances sont installées, configurez les clés API pour tester');
    }
}

/**
 * Fonction principale
 */
async function main() {
    console.log('🚀 Début de l\'installation...\n');
    
    // Vérifier les packages déjà installés
    console.log('📋 Vérification des packages existants...');
    for (const pkg of packages) {
        if (isPackageInstalled(pkg)) {
            console.log(`✅ ${pkg} déjà installé`);
        } else {
            console.log(`❌ ${pkg} non installé`);
        }
    }
    console.log('');
    
    // Installer les packages manquants
    console.log('📦 Installation des packages...');
    let successCount = 0;
    for (const pkg of packages) {
        if (!isPackageInstalled(pkg)) {
            const success = await installPackage(pkg);
            if (success) successCount++;
        } else {
            successCount++;
        }
    }
    console.log('');
    
    // Vérifier les voix système
    await checkSystemVoices();
    console.log('');
    
    // Créer le fichier de configuration exemple
    createConfigExample();
    console.log('');
    
    // Tester la génération TTS
    await testTtsGeneration();
    console.log('');
    
    // Résumé
    console.log('📊 RÉSUMÉ DE L\'INSTALLATION');
    console.log('================================');
    console.log(`✅ Packages installés: ${successCount}/${packages.length}`);
    console.log('📄 Fichier de configuration exemple créé');
    console.log('');
    
    if (successCount === packages.length) {
        console.log('🎉 Installation terminée avec succès !');
        console.log('');
        console.log('📝 ÉTAPES SUIVANTES:');
        console.log('1. Configurez vos clés API dans le fichier .env');
        console.log('2. Pour Google Cloud TTS: https://console.cloud.google.com/');
        console.log('3. Pour Azure Speech: https://azure.microsoft.com/services/cognitive-services/speech-services/');
        console.log('4. Redémarrez votre serveur backend');
        console.log('5. Testez avec votre frontend mobile');
    } else {
        console.log('⚠️ Installation partielle. Vérifiez les erreurs ci-dessus.');
    }
}

// Exécuter si appelé directement
if (require.main === module) {
    main().catch(console.error);
}

module.exports = { main };
