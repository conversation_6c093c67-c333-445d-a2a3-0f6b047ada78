#!/usr/bin/env node

/**
 * Test spécifique pour vérifier les données courseListData
 */

const axios = require('axios');

async function testCourseListData() {
    console.log('🧪 TEST DES DONNÉES COURSE LIST');
    console.log('='.repeat(50));
    
    const baseURL = 'http://localhost:5001';
    const testMessage = 'هل يوجد درس اسمه الضرب في رقمين؟';
    const userId = 3712;
    
    try {
        console.log('\n📤 Envoi de la requête API...');
        console.log(`Message: "${testMessage}"`);
        
        const response = await axios.post(`${baseURL}/api/chatbot/ask`, {
            message: testMessage,
            userId: userId,
            messageType: 'text'
        }, {
            headers: {
                'Content-Type': 'application/json'
            },
            timeout: 30000
        });
        
        console.log('\n✅ RÉPONSE REÇUE:');
        console.log('='.repeat(30));
        console.log(`Status: ${response.status}`);
        console.log(`Reply: ${response.data.reply}`);
        
        // Vérifier les données de navigation
        if (response.data.navigation) {
            console.log('\n🧭 NAVIGATION DÉTECTÉE:');
            console.log(`Screen: ${response.data.navigation.screen}`);
            
            if (response.data.navigation.params?.filteredCourses) {
                const courses = response.data.navigation.params.filteredCourses;
                console.log(`Nombre de cours: ${courses.length}`);
                
                courses.forEach((course, index) => {
                    console.log(`\n📚 Cours ${index + 1}:`);
                    console.log(`  ID: ${course.id}`);
                    console.log(`  Titre: ${course.title}`);
                    console.log(`  Enseignant: ${course.teacher}`);
                    console.log(`  Score: ${course.score}`);
                    console.log(`  Image: ${course.image}`);
                });
            }
        }
        
        // Vérifier les données courseListData
        if (response.data.courseListData) {
            console.log('\n🎯 COURSE LIST DATA DÉTECTÉES:');
            console.log(`Titre: ${response.data.courseListData.title}`);
            console.log(`Requête de recherche: ${response.data.courseListData.searchQuery}`);
            console.log(`Nombre de cours: ${response.data.courseListData.courses.length}`);
            
            response.data.courseListData.courses.forEach((course, index) => {
                console.log(`\n📖 Cours ${index + 1} (courseListData):`);
                console.log(`  ID: ${course.id}`);
                console.log(`  Titre: ${course.title}`);
                console.log(`  Enseignant: ${course.teacher}`);
                console.log(`  Score: ${course.score}`);
            });
            
            console.log('\n✅ COURSE LIST DATA PRÉSENTES !');
        } else {
            console.log('\n❌ COURSE LIST DATA MANQUANTES !');
            console.log('Les données courseListData ne sont pas présentes dans la réponse.');
        }
        
        // Vérifier la structure complète de la réponse
        console.log('\n🔍 STRUCTURE COMPLÈTE DE LA RÉPONSE:');
        console.log('Champs présents:', Object.keys(response.data));
        
        // Vérifications spécifiques
        const checks = [
            { name: 'reply', present: !!response.data.reply },
            { name: 'audio', present: !!response.data.audio },
            { name: 'navigation', present: !!response.data.navigation },
            { name: 'courseListData', present: !!response.data.courseListData },
            { name: 'voiceId', present: !!response.data.voiceId },
            { name: 'gender', present: !!response.data.gender }
        ];
        
        console.log('\n📋 VÉRIFICATIONS:');
        checks.forEach(check => {
            const status = check.present ? '✅' : '❌';
            console.log(`${status} ${check.name}: ${check.present ? 'PRÉSENT' : 'MANQUANT'}`);
        });
        
        // Résultat final
        const hasNavigation = !!response.data.navigation;
        const hasCourseListData = !!response.data.courseListData;
        
        if (hasNavigation && hasCourseListData) {
            console.log('\n🎉 TEST RÉUSSI !');
            console.log('✅ Navigation ET courseListData présentes');
            console.log('✅ Le frontend peut afficher la carte de liste de cours');
        } else if (hasNavigation && !hasCourseListData) {
            console.log('\n⚠️ TEST PARTIELLEMENT RÉUSSI');
            console.log('✅ Navigation présente');
            console.log('❌ courseListData manquantes');
            console.log('⚠️ Le frontend ne pourra pas afficher la carte');
        } else {
            console.log('\n❌ TEST ÉCHOUÉ');
            console.log('❌ Données de navigation manquantes');
        }
        
    } catch (error) {
        console.error('\n❌ ERREUR LORS DU TEST:');
        
        if (error.response) {
            console.error(`Status: ${error.response.status}`);
            console.error(`Data:`, error.response.data);
        } else if (error.request) {
            console.error('Aucune réponse reçue du serveur');
        } else {
            console.error('Erreur de configuration:', error.message);
        }
    }
}

// Exécuter si appelé directement
if (require.main === module) {
    testCourseListData().catch(console.error);
}

module.exports = { testCourseListData };
