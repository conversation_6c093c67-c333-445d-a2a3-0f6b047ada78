#!/usr/bin/env node

/**
 * Script de test pour le système de navigation intelligente des cours
 */

const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '.env') });

// Importer les services
const ChatbotMobileService = require('./public/src/services/ChatbotMobileService');
const CourseNavigationService = require('./public/src/services/CourseNavigationService');

/**
 * Tests de navigation intelligente
 */
async function testCourseNavigation() {
    console.log('🧪 TESTS DE NAVIGATION INTELLIGENTE DES COURS');
    console.log('='.repeat(60));
    
    // Paramètres de test (niveau 6 = 1ère année)
    const testCases = [
        {
            name: "Recherche spécifique - fractions",
            levelId: 6,
            matiereName: "Mathématiques",
            userMessage: "نحب نشوف cours sur les fractions"
        },
        {
            name: "Recherche spécifique - géométrie",
            levelId: 6,
            matiereName: "Mathématiques",
            userMessage: "أريد درس في الهندسة"
        },
        {
            name: "Recherche générale - mathématiques",
            levelId: 6,
            matiereName: "Mathématiques",
            userMessage: "عرضلي دروس الرياضيات"
        },
        {
            name: "Recherche sans message spécifique",
            levelId: 6,
            matiereName: "Mathématiques",
            userMessage: ""
        },
        {
            name: "Recherche cours inexistant",
            levelId: 6,
            matiereName: "Mathématiques",
            userMessage: "نحب نشوف cours sur la physique quantique"
        }
    ];
    
    for (let i = 0; i < testCases.length; i++) {
        const testCase = testCases[i];
        console.log(`\n📋 Test ${i + 1}: ${testCase.name}`);
        console.log('-'.repeat(40));
        console.log(`📊 Niveau: ${testCase.levelId}`);
        console.log(`📚 Matière: ${testCase.matiereName}`);
        console.log(`💬 Message: "${testCase.userMessage}"`);
        
        try {
            const startTime = Date.now();
            
            // Appeler le service de navigation
            const result = await ChatbotMobileService.voir_cours(
                testCase.levelId,
                testCase.matiereName,
                testCase.userMessage
            );
            
            const endTime = Date.now();
            const duration = endTime - startTime;
            
            console.log(`⏱️ Durée: ${duration}ms`);
            console.log(`✅ Succès: ${result.success}`);
            console.log(`💬 Message: ${result.message || result.messageAr || 'Aucun message'}`);
            
            if (result.navigation) {
                console.log(`🧭 Navigation: ${result.navigation.screen}`);
                if (result.navigation.params) {
                    console.log(`📋 Paramètres: ${JSON.stringify(result.navigation.params, null, 2)}`);
                }
            }
            
            if (result.data && result.data.courses) {
                console.log(`📚 Cours trouvés: ${result.data.courses.length}`);
                result.data.courses.forEach((course, index) => {
                    console.log(`  ${index + 1}. ${course.title} (ID: ${course.id})`);
                    if (course.score) {
                        console.log(`     Score: ${course.score}`);
                    }
                    if (course.raison) {
                        console.log(`     Raison: ${course.raison}`);
                    }
                });
            }
            
        } catch (error) {
            console.error(`❌ Erreur: ${error.message}`);
            console.error(error.stack);
        }
        
        // Pause entre les tests
        if (i < testCases.length - 1) {
            await new Promise(resolve => setTimeout(resolve, 1000));
        }
    }
}

/**
 * Test direct du service CourseNavigationService
 */
async function testCourseNavigationService() {
    console.log('\n\n🔬 TESTS DIRECTS DU SERVICE DE NAVIGATION');
    console.log('='.repeat(60));
    
    try {
        // Test de chargement des données
        console.log('\n📚 Test 1: Chargement des données des cours');
        const courses = await CourseNavigationService.loadCompleteCoursesData(6, [1, 2, 3]);
        console.log(`✅ ${courses.length} cours chargés`);
        
        if (courses.length > 0) {
            console.log(`📋 Premier cours: ${courses[0].id}`);
            console.log(`📋 Chapitres: ${courses[0].chapters?.length || 0}`);
            
            // Test de structuration pour GPT
            console.log('\n🤖 Test 2: Structuration des données pour GPT');
            const structuredData = CourseNavigationService.structureCourseDataForGPT(courses);
            console.log(`✅ ${structuredData.length} cours structurés`);
            console.log(`📋 Structure du premier cours:`, JSON.stringify(structuredData[0], null, 2));
            
            // Test de correspondance GPT
            console.log('\n🧠 Test 3: Correspondance GPT');
            const gptResult = await CourseNavigationService.findMatchingCoursesWithGPT(
                "نحب نشوف cours sur les fractions",
                structuredData.slice(0, 3) // Limiter pour le test
            );
            console.log(`✅ Résultat GPT:`, JSON.stringify(gptResult, null, 2));
        }
        
    } catch (error) {
        console.error(`❌ Erreur dans les tests directs: ${error.message}`);
        console.error(error.stack);
    }
}

/**
 * Test de performance
 */
async function testPerformance() {
    console.log('\n\n⚡ TESTS DE PERFORMANCE');
    console.log('='.repeat(60));
    
    const iterations = 5;
    const testMessage = "نحب نشوف cours sur les fractions";
    const times = [];
    
    for (let i = 0; i < iterations; i++) {
        console.log(`\n🏃 Itération ${i + 1}/${iterations}`);
        
        const startTime = Date.now();
        
        try {
            const result = await ChatbotMobileService.voir_cours(6, "Mathématiques", testMessage);
            const endTime = Date.now();
            const duration = endTime - startTime;
            
            times.push(duration);
            console.log(`⏱️ Durée: ${duration}ms`);
            console.log(`✅ Succès: ${result.success}`);
            
        } catch (error) {
            console.error(`❌ Erreur: ${error.message}`);
        }
        
        // Pause entre les itérations
        await new Promise(resolve => setTimeout(resolve, 500));
    }
    
    if (times.length > 0) {
        const avgTime = times.reduce((a, b) => a + b, 0) / times.length;
        const minTime = Math.min(...times);
        const maxTime = Math.max(...times);
        
        console.log('\n📊 STATISTIQUES DE PERFORMANCE');
        console.log(`📈 Temps moyen: ${avgTime.toFixed(2)}ms`);
        console.log(`⚡ Temps minimum: ${minTime}ms`);
        console.log(`🐌 Temps maximum: ${maxTime}ms`);
    }
}

/**
 * Fonction principale
 */
async function main() {
    console.log('🚀 DÉMARRAGE DES TESTS DE NAVIGATION INTELLIGENTE');
    console.log('='.repeat(80));
    
    try {
        // Tests de navigation
        await testCourseNavigation();
        
        // Tests directs du service
        await testCourseNavigationService();
        
        // Tests de performance
        await testPerformance();
        
        console.log('\n\n🎉 TOUS LES TESTS TERMINÉS');
        console.log('='.repeat(80));
        
    } catch (error) {
        console.error('❌ Erreur générale:', error);
    } finally {
        // Fermer les connexions
        try {
            const db = require('./public/src/models');
            if (db && db.sequelize) {
                await db.sequelize.close();
                console.log('🔌 Connexion base de données fermée');
            }
        } catch (closeError) {
            console.error('⚠️ Erreur fermeture DB:', closeError.message);
        }
        
        process.exit(0);
    }
}

// Exécuter si appelé directement
if (require.main === module) {
    main().catch(console.error);
}

module.exports = { main };
