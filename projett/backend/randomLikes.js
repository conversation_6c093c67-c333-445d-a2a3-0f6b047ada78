const db = require('./public/src/models'); // ← adapte le chemin si nécessaire
const { Like, User, Video } = db;

async function generateRandomLikesForLevel9() {
  const users = await User.findAll({
    where: { role_name: 'enfant', level_id: 9 }
  });

  const videos = await Video.findAll({
    where: {
      manuel_id: { [db.Sequelize.Op.between]: [13, 19] }
    }
  });

  const usedPairs = new Set();

  for (const user of users) {
    const maxLikes = Math.floor(Math.random() * 8) + 2;

    for (let i = 0; i < maxLikes; i++) {
      const video = videos[Math.floor(Math.random() * videos.length)];
      const key = `${user.id}-${video.id}`;

      if (!usedPairs.has(key)) {
        usedPairs.add(key);

        await Like.create({
          user_id: user.id,
          video_id: video.id,
          created_at: new Date(),
          updated_at: new Date()
        });

        //await video.increment('likes');
      }
    }
  }

  console.log("✅ Insertion des likes random terminée !");
}

generateRandomLikesForLevel9().then(() => process.exit()).catch(err => {
  console.error(err);
  process.exit(1);
});
