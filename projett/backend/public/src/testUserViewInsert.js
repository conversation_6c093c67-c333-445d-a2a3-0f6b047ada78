const { Sequelize } = require("sequelize");
const Video = require("./models/Videos");
const UserView = require("./models/UserView");
const User = require("./models/User");
const getCorrectionVideoUrl = require("./services/documentService").getCorrectionVideoUrl;

(async () => {
  try {
    // 🔧 Paramètres de test
    const testManuelId = 9;
    const testIcon = 10;
    const testPage = 5;
    const testChildId = 1533; // 🔄 ID d’un enfant valide dans ta DB

    // 🔄 Avant : supprimer vue existante si présente
    await UserView.destroy({
      where: { user_id: testChildId, video_id: { [Sequelize.Op.not]: null } }
    });

    // 📦 Appel de la fonction
    const result = await getCorrectionVideoUrl(testManuelId, testIcon, testPage, testChildId);
    console.log("✅ Correction video result:", result.title);

    // 🔍 Vérifie l'insertion
    const view = await UserView.findOne({
      where: { user_id: testChildId, video_id: result.id }
    });

    if (view) {
      console.log("✅ Vue bien enregistrée dans user_views");
    } else {
      console.error("❌ Vue non enregistrée !");
    }
  } catch (error) {
    console.error("❌ Test échoué :", error);
  } finally {
    process.exit();
  }
})();
