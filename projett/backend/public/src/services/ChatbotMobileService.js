// services/ChatbotMobileService.js
const db = require("../models");
const Material = db.Material;
const { getBestManuelName } = require('./manuelFinder');
const WebinarService = require("./WebinarService");
const videoService = require("./videoService");
const manuelService = require("./manuelService");
const MeetingService = require("./MeetingService");
const LikeService = require("./LikeService");
const FollowService = require("./FollowService");
const NotificationService = require("./NotificationService");
const TeacherService = require("./TeacherService");
const enfantService = require("./enfantService");
const SaleService = require("./SaleService");
const documentService = require("./documentService");
const userService = require("./userService");
const RecommendationService = require("./RecommendationService");
const ProgressiveRecommendationService = require("./ProgressiveRecommendationService");
const RecommendationHistoryService = require("./RecommendationHistoryService");
const { getHistory, addToHistory } = require('./chatMemory');
const { getOpenAIResponse } = require('./openaiService');
const logger = require('../utils/logger');

const normalize = (s) => s?.trim()?.toLowerCase().replace(/\s/g, '');
const Webinar = require("../models/Webinar");
const User = require("../models/User");
const WebinarChapter = require("../models/WebinarChapter");
const File = require("../models/File");
const FileTranslation = require("../models/FileTranslation");
const matchMatiere = (matiereDB, matiereUser) => {
    const map = {
        'رياضيات': ['رياضيات', 'رياضة', 'math'],
        'الإيقاظ العلمي': ['إيقاظ', 'الإيقاظ', 'علوم', 'science'],
        'الفرنسية': ['فرنسية', 'فرنساوي', 'français'],
        'العربية': ['عربية', 'عربي', 'arabe']
    };

    for (let [dbLabel, variants] of Object.entries(map)) {
        if (variants.some(v => normalize(v) === normalize(matiereUser))) {
            return normalize(matiereDB) === normalize(dbLabel);
        }
    }

    // fallback exact
    return normalize(matiereDB) === normalize(matiereUser);
};


// Mapping local entre levelId et matière_ids
const manualLevelMapping = {
    1: [1, 2, 3, 4, 5], // Niveau 1 pour les tests
    6: [1, 1, 2],
    7: [3, 4, 4],
    8: [5, 5, 6, 7, 7, 8],
    9: [9, 9, 10, 11, 11, 12, 12],
    10: [13, 14, 14, 15, 16, 16, 17, 17],
    11: [18, 18, 19, 20, 21, 21, 22, 22, 23]
};



const stringSimilarity = require('string-similarity');

function findClosestManuel(manuels, manuelName) {
    const target = normalize(manuelName);
    if (!target || !manuels || manuels.length === 0) return null;

    const matches = manuels.map(m => ({
        manuel: m,
        score: stringSimilarity.compareTwoStrings(normalize(m.name), target)
    }));

    const best = matches.sort((a, b) => b.score - a.score)[0];
    return best?.score > 0.5 ? best.manuel : null;
}

async function repondreAvecMemoireMix(userId, message, context = {}) {
    try {
        const history = await getHistory(userId);
        const gptReply = await getOpenAIResponse(userId, message, history, context);
        await addToHistory(userId, message, gptReply);
        return { message: gptReply };
    } catch (error) {
        console.error('❌ Erreur GPT avec mémoire :', error.message);
        return { error: "Erreur lors de la réponse intelligente." };
    }
}

module.exports = {
    repondreAvecMemoireMix,
    async voir_cours(levelId, matiereName, userMessage = null) {
        try {
            const db = require('../models');
            const { Op } = require('sequelize');

            console.log(`🔍 Traitement de l'intention voir_cours avec message: "${userMessage}"`);

            // Fonction pour charger les données complètes des cours avec leurs chapitres et fichiers
            async function loadCourseData(levelId, matiereIds) {
                console.log(`📚 Chargement des cours pour niveau ${levelId} et matières [${matiereIds.join(', ')}]`);

                const courses = await db.Webinar.findAll({
                    where: {
                        matiere_id: { [db.Sequelize.Op.in]: matiereIds },
                        level_id: levelId,
                        status: 'active',
                        deleted_at: null
                    },
                    include: [
                        {
                            model: db.User,
                            as: 'teacher',
                            attributes: ['id', 'full_name', 'avatar']
                        },
                        {
                            model: db.WebinarTranslation,
                            attributes: ['title', 'locale', 'description']
                        },
                        {
                            model: db.WebinarChapter,
                            as: 'chapters',
                            attributes: ['id', 'order', 'webinar_id'],
                            where: { status: 'active' },
                            required: false,
                            include: [
                                {
                                    model: db.File,
                                    as: 'files',
                                    attributes: ['id', 'file', 'file_type', 'order'],
                                    where: { status: 'active' },
                                    required: false,
                                    include: [
                                        {
                                            model: db.FileTranslation,
                                            as: 'translations',
                                            attributes: ['title', 'locale', 'description']
                                        }
                                    ]
                                }
                            ]
                        }
                    ],
                    order: [
                        ['id', 'ASC'],
                        [{ model: db.WebinarChapter, as: 'chapters' }, 'order', 'ASC'],
                        [{ model: db.WebinarChapter, as: 'chapters' }, { model: db.File, as: 'files' }, 'order', 'ASC']
                    ]
                });

                console.log(`✅ ${courses.length} cours chargés avec leurs chapitres et fichiers`);
                return courses;
            }

            // 🔍 Obtenir tous les matiere_id possibles pour ce niveau
            const matiereIds = manualLevelMapping[levelId] || [];

            // 🔍 Récupérer tous les ID correspondants au nom donné (dans la liste du mapping)
            const materials = await db.Material.findAll({
                where: {
                    name: matiereName,
                    id: { [Op.in]: matiereIds }
                }
            });

            if (!materials || materials.length === 0) {
                return { 
                    success: false, 
                    message: `❌ عذرًا، لم أجد مادة "${matiereName}" في المستوى ${levelId}.`,
                    navigation: null
                };
            }

            const matchingMatiereIds = materials.map(m => m.id);

            // Charger les données complètes des cours avec leurs chapitres et fichiers
            console.log(`📚 Chargement des données complètes pour le niveau ${levelId} et les matières ${matchingMatiereIds.join(', ')}`);
            
            // Utiliser la fonction loadCourseData pour récupérer toutes les données nécessaires
            const webinars = await loadCourseData(levelId, matchingMatiereIds);

            if (!webinars || webinars.length === 0) {
                return { 
                    success: false, 
                    message: `📚 لا يوجد دروس حالياً في "${matiereName}" للمستوى ${levelId}.`,
                    navigation: null
                };
            }

            // Si un message utilisateur est fourni, utiliser le service de navigation intelligent
            if (userMessage && userMessage.trim() !== "") {
                console.log(`🎯 Utilisation du service de navigation intelligent pour: "${userMessage}"`);

                try {
                    const CourseNavigationService = require('./CourseNavigationService');
                    const navigationResult = await CourseNavigationService.processVoirCoursIntention(
                        levelId,
                        matiereName,
                        userMessage,
                        matchingMatiereIds
                    );

                    if (navigationResult.success) {
                        console.log(`✅ Navigation intelligente réussie`);
                        return {
                            success: true,
                            message: navigationResult.responseAr || navigationResult.response,
                            messageAr: navigationResult.responseAr,
                            messageFr: navigationResult.responseFr,
                            data: navigationResult.data,
                            navigation: navigationResult.navigation
                        };
                    } else {
                        console.log(`⚠️ Navigation intelligente n'a pas trouvé de correspondance`);
                        // Continuer avec la logique par défaut
                    }
                } catch (navigationError) {
                    console.error('❌ Erreur avec le service de navigation intelligent:', navigationError);
                    // Continuer avec la logique par défaut
                }
            }

            // Si aucun message utilisateur ou erreur avec courseFinder, retourner tous les cours disponibles
            let allWebinars = [];
            try {
                allWebinars = await db.Webinar.findAll({
                    where: {
                        matiere_id: { [db.Sequelize.Op.in]: matiereIds },
                        level_id: levelId,
                        status: 'active',
                        deleted_at: null
                    },
                    include: [
                        {
                            model: db.User,
                            as: 'teacher',
                            attributes: ['id', 'full_name', 'avatar']
                        },
                        {
                            model: db.WebinarTranslation,
                            attributes: ['title', 'locale', 'description']
                        }
                    ]
                });

                if (allWebinars.length === 0) {
                    return {
                        success: false,
                        message: `للأسف، ما لقيناش دروس متوفرة في ${matiereName} للمستوى المحدد.`,
                        messageAr: `للأسف، ما لقيناش دروس متوفرة في ${matiereName} للمستوى المحدد.`,
                        messageFr: `Désolé, nous n'avons pas trouvé de cours disponibles en ${matiereName} pour le niveau spécifié.`
                    };
                }

                const formattedWebinars = allWebinars.map(webinar => ({
                    id: webinar.id,
                    title: webinar.WebinarTranslations?.find(t => t.locale === 'ar')?.title || 
                           webinar.WebinarTranslations?.find(t => t.locale === 'fr')?.title || 
                           'Cours sans titre',
                    image: webinar.image_cover,
                    teacher: webinar.teacher?.full_name || 'Enseignant inconnu'
                }));

                return {
                    success: true,
                    message: `عندنا ${formattedWebinars.length} دروس متوفرة في ${matiereName}. تفضل:`,
                    messageAr: `عندنا ${formattedWebinars.length} دروس متوفرة في ${matiereName}. تفضل:`,
                    messageFr: `Nous avons ${formattedWebinars.length} cours disponibles en ${matiereName}. Les voici:`,
                    data: {
                        courses: formattedWebinars,
                        navigation: {
                            screen: 'CourseList',
                            params: {
                                matiere: matiereName,
                                levelId: levelId
                            }
                        }
                    }
                };
            } catch (dbError) {
                console.error('❌ Erreur lors de la recherche des cours disponibles:', dbError);
                return {
                    success: false,
                    message: 'Désolé, une erreur est survenue lors de la recherche des cours.',
                    messageAr: 'عذرًا، حدث خطأ أثناء البحث عن الدروس.',
                    messageFr: 'Désolé, une erreur est survenue lors de la recherche des cours.'
                };
            }
        } catch (error) {
            console.error('❌ Erreur dans voir_cours:', error);
            return {
                success: false,
                message: 'Désolé, une erreur est survenue lors de la recherche des cours.',
                messageAr: 'عذرًا، حدث خطأ أثناء البحث عن الدروس.',
                messageFr: 'Désolé, une erreur est survenue lors de la recherche des cours.'
            };
        }
    },


    async voir_exercices(levelId, matiere, options = {}) {
        try {
            const { manuelName, page } = options;

            // 📘 Récupération de tous les manuels du niveau
            const manuels = await manuelService.getManuelsByLevel(levelId);

            // 🧠 Fonction d’aide pour trouver le manuel le plus proche
            const normalize = (s) => s?.trim()?.toLowerCase().replace(/\s/g, '');

            // 🔍 Trouver le manuel correspondant par nom ou matière
            const manuel = manuelName
                ? findClosestManuel(manuels, manuelName)
                : manuels.find(m => matchMatiere(m.material?.name, matiere));

            if (!manuel) {
                return {
                    message: `❌ لم أجد كتابًا لهذه المادة أو الاسم "${manuelName}" غير معروف.`
                };
            }

            // ✅ Forcer la matière à celle du manuel détecté
            if (manuel.material && manuel.material.name) {
                matiere = manuel.material.name;
                console.log("🎯 Mise à jour automatique de la matière selon le manuel :", matiere);
            }

            console.log("📘 Manuel sélectionné :", manuel.name, "| ID:", manuel.id);

            // 🎯 Récupérer les vidéos associées à ce manuel
            const videos = await videoService.getVideosByManuelId(manuel.id, page);

            const filteredVideos = page
                ? videos.filter(v => v.page && String(v.page).trim() === String(page).trim())
                : videos;

            if (filteredVideos.length > 0) {
                return filteredVideos.map(v => ({
                    titre: v.titre || "📹 فيديو تمرين",
                    lien: v.video,
                    page: v.page,
                    description: v.description || '',
                    thumbnail: v.thumbnail || null
                }));
            }

            // 📄 Fallback PDF (uniquement s’il contient "تمرين" ou "exercice" dans le nom ?)
            const docs = await documentService.getDocumentsByManuel(manuel.id);

            if (docs.length > 0) {
                const exerciseDocs = docs.filter(d => /تمرين|exercice|كراس/i.test(d.name));

                if (exerciseDocs.length > 0) {
                    return exerciseDocs.map(d => ({
                        titre: d.name,
                        lien: d.pdf,
                        type: "PDF",
                        pages: d.nombre_page
                    }));
                }
            }

            // ❌ Aucun exercice trouvé
            return {
                message: `📘 تم العثور على كتاب "${manuel.name}" لكن لا يحتوي على تمارين أو فيديوهات مرتبطة حاليًا.`
            };

        } catch (error) {
            console.error("❌ Erreur dans voir_exercices :", error.message);
            return { error: "Erreur lors de la récupération des exercices." };
        }
    }

    ,

    async voir_meets(levelId) {
        const meetings = await MeetingService.getMeetingsByLevel(levelId);

        if (!meetings || meetings.length === 0) {
            return {
                message: `📭 لا توجد عروض مباشرة حالياً لمستواك. تابعنا بانتظام، سيتم إضافة عروض قريبة قريبًا إن شاء الله !`
            };
        }

        return meetings.map(meeting => {
            const time = meeting.times?.[0] || {};
            const date = time.meet_date
                ? new Date(time.meet_date * 1000).toISOString().split('T')[0]
                : '📅 غير محدد';
            const heure = time.start_time
                ? new Date(time.start_time * 1000).toISOString().split('T')[1]?.slice(0, 5)
                : '🕒 غير محدد';
            const fichiers = meeting.files?.map(f => `- ${f.file_path}`).join('\n') || '📂 لا توجد ملفات مرفقة';

            return `✅ عرض مباشر متاح:
      👨‍🏫 المعلم: ${meeting.teacher?.full_name || 'أستاذ غير معروف'}
      📘 المادة: ${time.material?.name || 'غير محددة'}
      📅 التاريخ: ${date}
      🕒 الساعة: ${heure}
      📁 ملفات مرفقة:
      ${fichiers}`;
        });
    }
    ,

    async abonnement_prof(follower, user_id) {
        try {
            const result = await FollowService.subscribe(follower, user_id);
            console.log("✅ Résultat abonnement :", result);
            return result;
        } catch (error) {
            console.error("❌ Erreur dans abonnement_prof :", error.message);
            throw error;
        }
    },

    async abonnement_platforme(userId) {
        const user = await userService.getLoggedInUser(userId);
        return user ? { message: "Abonnement demandé avec succès" } : { error: "Utilisateur introuvable" };
    },
    async voir_manuels(levelId) {
        return await manuelService.getManuelsByLevel(levelId);
    },
    async voir_details_manuel(userId, userMessage, levelId) {
        const manuels = await manuelService.getManuelsByLevel(levelId);
        if (!manuels || manuels.length === 0) {
            return { message: `❌ لا توجد كتب متاحة حاليًا للمستوى ${levelId}.` };
        }

        const manuelName = await getBestManuelName(userMessage, manuels);
        const manuelTrouve = manuels.find(m => m.name === manuelName);

        if (!manuelTrouve) {
            return {
                message: `❌ لم أتمكن من العثور على الكتاب المطلوب في مستواك.`,
                suggestions: manuels.map(m => `📘 ${m.name}`).join('\n')
            };
        }

        // ✅ Récupérer les documents liés
        const documents = await manuelService.getDocumentsByManuel(manuelTrouve.id);

        if (!documents || documents.length === 0) {
            return {
                titre: manuelTrouve.name,
                couverture: manuelTrouve.logo ? `🖼️ الغلاف: ${manuelTrouve.logo}` : "❌ لا توجد صورة متاحة",
                contenu: "⚠️ لا توجد ملفات مرتبطة بهذا الكتاب حاليًا."
            };
        }

        const docsFormatted = documents.map(doc => `
      📘 ${doc.name}
      📄 عدد الصفحات: ${doc.nombre_page}
      📎 الرابط: ${doc.pdf}
      🧒 نسخة ثلاثية الأبعاد (تلميذ): ${doc["pathenfant"] || "❌ غير متوفرة"}
      `).join("\n\n");

        return {
            titre: manuelTrouve.name,
            couverture: manuelTrouve.logo ? `🖼️ الغلاف: ${manuelTrouve.logo}` : "❌ لا توجد صورة متاحة",
            contenu: docsFormatted
        };
    },

    async voir_profil_prof(teacherId) {
        return await TeacherService.getTeacherById(teacherId);
    },

    //async voir_quizz() {
    // À personnaliser selon ta logique de quiz
    //return { message: "Voici un quiz à faire" };
    //},

    // async voir_favoris(userId) {
    //     return await LikeService.getFavoriteWebinars(userId);
    // },

    // async voir_videos_manuels(manuelId) {
    //     return await videoService.getVideosByManuelId(manuelId);
    // },

    async notifier_parent(userId, data) {
        return await NotificationService.sendNotification({
            user_id: userId,
            title: data.title,
            message: data.message,
            data: data.payload || {},
        });
    },

    async storytime(preferences) {
        return { story: `Une histoire pour toi qui aime ${preferences.join(", ")}` };
    },

    // async recommander_professeur(levelId) {
    //     const all = await WebinarService.getByLevelId(levelId);
    //     return all.length > 0 ? all[0].teacher : null;
    // },
    async recommander_professeur(userId, matiereName, userMessage = null, history = null) {
        try {
            // Récupérer le niveau de l'utilisateur
            const user = await db.User.findByPk(userId);
            if (!user || !user.level_id) {
                console.error(`❌ Utilisateur ${userId} non trouvé ou sans niveau défini`);
                return null;
            }

            const levelId = user.level_id;
            console.log(`🔍 Recommandation de professeur pour l'utilisateur ${userId}, matière '${matiereName}', niveau ${levelId}`);

            // 📡 Appeler le moteur de recommandation pour obtenir la liste complète
            const teacherList = await RecommendationService.recommendProf(userId, matiereName, levelId, 10);

            if (!teacherList || teacherList.length === 0) {
                console.warn(`⚠️ Aucun professeur trouvé pour l'utilisateur ${userId} (niveau ${levelId}) dans la matière ${matiereName}`);
                return null;
            }
            console.log(`✅ ${teacherList.length} professeurs recommandés trouvés`);

            // 🧾 Enregistrer la recommandation dans l'historique
            const savedRecommendation = await RecommendationHistoryService.saveRecommendation(
                userId,
                'teacher',
                teacherList,
                matiereName,
                levelId
            );

            // 📚 Récupérer l'historique des éléments déjà montrés
            const shownTeachers = await RecommendationHistoryService.getShownItems(userId, 'teacher', 7);
            console.log(`📚 Professeurs déjà montrés: ${shownTeachers.length}`);

            // 🔍 Trouver le prochain professeur à montrer
            const nextTeacher = RecommendationHistoryService.findNextItemToShow(teacherList, shownTeachers);

            if (!nextTeacher) {
                console.warn(`⚠️ Tous les professeurs ont déjà été montrés à l'utilisateur ${userId}`);
                return { noMoreRecommendations: true };
            }

            // ✅ Marquer ce professeur comme montré
            await RecommendationHistoryService.markItemAsShown(
                savedRecommendation.session_request_id,
                nextTeacher.id
            );

            // 🎯 Enrichir les informations du professeur
            const webinarCount = await db.Webinar.count({
                where: { teacher_id: nextTeacher.id }
            });

            const lastCourses = await db.Webinar.findAll({
                where: { teacher_id: nextTeacher.id },
                order: [['created_at', 'DESC']],
                limit: 3
            });

            const courseNames = lastCourses.map(w => (w.slug && w.slug.trim()) ? w.slug : "درس بدون عنوان");

            // Chercher la matière réelle de ce prof
            const profMatiereWebinar = await db.Webinar.findOne({
                where: { teacher_id: nextTeacher.id },
                order: [['created_at', 'DESC']]
            });

            const profMatiereId = profMatiereWebinar?.matiere_id;

            let profMatiere = null;
            if (profMatiereId) {
                const matiere = await db.Material.findByPk(profMatiereId);
                profMatiere = matiere?.name || "مادة غير محددة";
            }

            const enrichedTeacher = {
                ...nextTeacher,
                webinarCount,
                lastCourses: courseNames,
                realMatiere: profMatiere
            };

            // 📊 Calculer la position dans la liste
            const currentPosition = shownTeachers.length + 1;
            const totalAvailable = teacherList.length;

            console.log(`✅ Professeur recommandé: ${enrichedTeacher.name} (${currentPosition}/${totalAvailable})`);

            return {
                professeur: enrichedTeacher,
                index: currentPosition,
                total: totalAvailable,
                sessionId: savedRecommendation.session_request_id,
                newRecommendationList: teacherList,
                previouslyShownIds: shownTeachers
            };
        } catch (error) {
            logger.error(`❌ Erreur dans recommander_professeur: ${error.message}`);
            logger.error(error.stack);
            return null;
        }
    },

    /**
     * Recommande des exercices pour un enfant basés sur son niveau scolaire
     * @param {number} userId - ID de l'enfant
     * @param {string} matiereName - Nom de la matière
     * @param {string} message - Message de l'utilisateur
     * @param {Array} conversationHistory - Historique de la conversation
     * @param {number} limit - Nombre de recommandations souhaitées
     * @returns {Promise<Object|null>} Objet contenant l'exercice recommandé ou null si aucun exercice trouvé
     */
    async recommander_exercice(userId, matiereName, message = '', conversationHistory = [], limit = 10) {
        try {
            // Récupérer le niveau de l'utilisateur
            const user = await db.User.findByPk(userId);
            if (!user || !user.level_id) {
                console.error(`❌ Utilisateur ${userId} non trouvé ou sans niveau défini`);
                return null;
            }

            const levelId = user.level_id;
            console.log(`🔍 Recommandation d'exercices pour l'utilisateur ${userId}, matière '${matiereName}', niveau ${levelId}`);

            // 📡 Appeler le moteur de recommandation pour obtenir la liste complète
            const response = await RecommendationService.recommendExercisesByManuel(userId, matiereName, levelId, limit);

            if (!response || !response.recommendations || response.recommendations.length === 0) {
                console.warn(`⚠️ Aucun exercice trouvé pour l'utilisateur ${userId} (niveau ${levelId}) dans la matière ${matiereName}`);
                return null;
            }

            const exerciseList = response.recommendations;
            console.log(`✅ ${exerciseList.length} exercices recommandés trouvés`);

            // 🧾 Enregistrer la recommandation dans l'historique
            const savedRecommendation = await RecommendationHistoryService.saveRecommendation(
                userId,
                'exercise',
                exerciseList,
                matiereName,
                levelId
            );

            // 📚 Récupérer l'historique des éléments déjà montrés
            const shownExercises = await RecommendationHistoryService.getShownItems(userId, 'exercise', 7);
            console.log(`📚 Exercices déjà montrés: ${shownExercises.length}`);

            // 🔍 Trouver le prochain exercice à montrer
            const nextExercise = RecommendationHistoryService.findNextItemToShow(exerciseList, shownExercises);

            if (!nextExercise) {
                console.warn(`⚠️ Tous les exercices ont déjà été montrés à l'utilisateur ${userId}`);
                return { noMoreRecommendations: true };
            }

            // ✅ Marquer cet exercice comme montré
            await RecommendationHistoryService.markItemAsShown(
                savedRecommendation.session_request_id,
                nextExercise.id
            );

            // 🎯 Enrichir les informations de l'exercice
            let manuelName = nextExercise.manuel_name;
            let teacherName = nextExercise.teacher_name;

            // Si nous n'avons pas le nom du manuel, le récupérer
            if (!manuelName && nextExercise.manuel_id) {
                try {
                    const manuel = await db.Manuel.findByPk(nextExercise.manuel_id);
                    manuelName = manuel?.name || 'كتاب غير معروف';
                } catch (error) {
                    console.error(`❌ Erreur lors de la récupération du manuel ${nextExercise.manuel_id}: ${error.message}`);
                }
            }

            // Si nous n'avons pas le nom de l'enseignant, le récupérer
            if (!teacherName && nextExercise.teacher_id) {
                try {
                    const teacher = await db.User.findByPk(nextExercise.teacher_id);
                    teacherName = teacher?.full_name || 'مدرس غير معروف';
                } catch (error) {
                    console.error(`❌ Erreur lors de la récupération de l'enseignant ${nextExercise.teacher_id}: ${error.message}`);
                }
            }

            const enrichedExercise = {
                ...nextExercise,
                manuel_name: manuelName,
                teacher_name: teacherName,
                // Conserver les informations complètes du professeur si elles existent
                teacher: nextExercise.teacher || {
                    id: nextExercise.teacher_id || nextExercise.user_id,
                    name: teacherName,
                    full_name: teacherName,
                    avatar: nextExercise.teacher_avatar || null
                }
            };

            // 📊 Calculer la position dans la liste
            const currentPosition = shownExercises.length + 1;
            const totalAvailable = exerciseList.length;

            console.log(`✅ Exercice recommandé: ${enrichedExercise.title || enrichedExercise.titre} (${currentPosition}/${totalAvailable})`);

            return {
                exercise: enrichedExercise,
                index: currentPosition,
                total: totalAvailable,
                sessionId: savedRecommendation.session_request_id,
                newRecommendationList: exerciseList,
                previouslyShownIds: shownExercises
            };
        } catch (error) {
            console.error(`❌ Erreur dans recommander_exercice: ${error.message}`);
            console.error(error.stack);
            return null;
        }
    },

    /**
     * Recommande des cours pour un enfant
     * @param {number} userId - ID de l'enfant
     * @param {string} matiereName - Nom de la matière
     * @param {string} message - Message de l'utilisateur
     * @param {Array} conversationHistory - Historique de la conversation
     * @param {number} limit - Nombre de recommandations souhaitées
     * @param {number} levelId - ID du niveau (optionnel, sera récupéré depuis l'utilisateur si non fourni)
     * @returns {Promise<Object|null>} Objet contenant le cours recommandé ou null si aucun cours trouvé
     */
    async recommander_cours(userId, matiereName, message = '', conversationHistory = [], limit = 5, levelId = null) {
        try {
            // Vérifier que l'utilisateur existe
            const user = await db.User.findByPk(userId);
            if (!user) {
                console.error(`❌ Utilisateur ${userId} non trouvé`);
                return null;
            }

            // Si levelId n'est pas fourni, utiliser le niveau de l'utilisateur
            if (!levelId) {
                if (!user.level_id) {
                    console.error(`❌ Utilisateur ${userId} n'a pas de niveau défini`);
                    return null;
                }
                levelId = user.level_id;
            }

            console.log(`🔍 Recommandation de cours pour l'utilisateur ${userId}, matière '${matiereName}', niveau ${levelId}`);

            // 📡 Appeler le moteur de recommandation pour obtenir la liste complète
            const response = await RecommendationService.recommendCourses(userId, matiereName, levelId, limit);

            if (!response || !response.recommendations || response.recommendations.length === 0) {
                console.warn(`⚠️ Aucun cours trouvé pour l'utilisateur ${userId} (niveau ${levelId}) dans la matière ${matiereName}`);
                return null;
            }

            const courseList = response.recommendations;
            console.log(`✅ ${courseList.length} cours recommandés trouvés`);

            // 🧾 Enregistrer la recommandation dans l'historique
            const savedRecommendation = await RecommendationHistoryService.saveRecommendation(
                userId,
                'course',
                courseList,
                matiereName,
                levelId
            );

            // 📚 Récupérer l'historique des éléments déjà montrés
            const shownCourses = await RecommendationHistoryService.getShownItems(userId, 'course', 7);
            console.log(`📚 Cours déjà montrés: ${shownCourses.length}`);

            // 🔍 Trouver le prochain cours à montrer
            const nextCourse = RecommendationHistoryService.findNextItemToShow(courseList, shownCourses);

            if (!nextCourse) {
                console.warn(`⚠️ Tous les cours ont déjà été montrés à l'utilisateur ${userId}`);
                return { noMoreRecommendations: true };
            }

            // ✅ Marquer ce cours comme montré
            await RecommendationHistoryService.markItemAsShown(
                savedRecommendation.session_request_id,
                nextCourse.id
            );

            // 🎯 Enrichir les informations du cours
            let teacherInfo = nextCourse.teacher || {};

            // Si nous n'avons pas toutes les informations sur l'enseignant, les récupérer
            if (nextCourse.teacher && nextCourse.teacher.id && (!nextCourse.teacher.name || !nextCourse.teacher.avatar)) {
                try {
                    const teacher = await db.User.findByPk(nextCourse.teacher.id);
                    if (teacher) {
                        teacherInfo = {
                            id: teacher.id,
                            name: teacher.full_name || 'مدرس غير معروف',
                            avatar: teacher.avatar || '/default-avatar.jpg'
                        };
                    }
                } catch (error) {
                    console.error(`❌ Erreur lors de la récupération de l'enseignant ${nextCourse.teacher.id}: ${error.message}`);
                }
            }

            const enrichedCourse = {
                ...nextCourse,
                teacher: teacherInfo
            };

            // 📊 Calculer la position dans la liste
            const currentPosition = shownCourses.length + 1;
            const totalAvailable = courseList.length;

            console.log(`✅ Cours recommandé: ${enrichedCourse.title} (${currentPosition}/${totalAvailable})`);

            return {
                course: enrichedCourse,
                index: currentPosition,
                total: totalAvailable,
                sessionId: savedRecommendation.session_request_id,
                newRecommendationList: courseList,
                previouslyShownIds: shownCourses
            };
        } catch (error) {
            console.error(`❌ Erreur dans recommander_cours: ${error.message}`);
            console.error(error.stack);
            return null;
        }
    },

    // async recommander_quizz(levelId) {
    //     return { quiz: `Quiz recommandé pour le niveau ${levelId}` };
    // },

    async question_generale(question) {
        return { answer: `Voici une réponse à ta question : ${question}` };
    },

    async accueil() {
        return { greeting: "👋 Bienvenue sur Abajim ! Comment puis-je t’aider aujourd’hui ?" };
    },

    async aide() {
        return {
            help: "Tu peux me demander : voir un cours, faire un quiz, voir un manuel, t'abonner à un prof, ou même écouter une histoire !"
        };
    },

    async ignorer_contenu() {
        return { message: "Je n'ai pas compris, peux-tu reformuler ?" };
    },

    async autre() {
        return { message: "Je suis là pour t'aider, n'hésite pas à poser une question !" };
    }

};
