// services/ChatbotMobileService.js
const db = require("../models");
const Material = db.Material;
const { getBestManuelName } = require('./manuelFinder');
const WebinarService = require("./WebinarService");
const videoService = require("./videoService");
const manuelService = require("./manuelService");
const MeetingService = require("./MeetingService");
const LikeService = require("./LikeService");
const FollowService = require("./FollowService");
const NotificationService = require("./NotificationService");
const TeacherService = require("./TeacherService");
const enfantService = require("./enfantService");
const SaleService = require("./SaleService");
const documentService = require("./documentService");
const userService = require("./userService");
const RecommendationService = require("./RecommendationService");
const { getHistory, addToHistory } = require('./chatMemory');
const { getOpenAIResponse } = require('./openaiService');

// Mapping des niveaux aux IDs de manuels
const manualLevelMapping = {
    6: [1, 1, 2],
    7: [3, 4, 4],
    8: [5, 5, 6, 7, 7, 8],
    9: [9, 9, 10, 11, 11, 12, 12],
    10: [13, 14, 14, 15, 16, 16, 17, 17],
    11: [18, 18, 19, 20, 21, 21, 22, 22, 23]
};

async function repondreAvecMemoireMix(userId, message, context = {}) {
    try {
        const history = await getHistory(userId);
        const gptReply = await getOpenAIResponse(userId, message, history, context);
        await addToHistory(userId, message, gptReply);
        return { message: gptReply };
    } catch (error) {
        console.error('❌ Erreur GPT avec mémoire :', error.message);
        return { error: "Erreur lors de la réponse intelligente." };
    }
}

module.exports = {
    repondreAvecMemoireMix,
    async voir_cours(levelId, matiereName) {
        try {
            const db = require('../models');
            const { Op } = require('sequelize');



            // 🔍 Obtenir tous les matiere_id possibles pour ce niveau
            const matiereIds = manualLevelMapping[levelId] || [];

            // 🔍 Récupérer tous les ID correspondants au nom donné (dans la liste du mapping)
            const materials = await db.Material.findAll({
                where: {
                    name: matiereName,
                    id: { [Op.in]: matiereIds }
                }
            });

            if (!materials || materials.length === 0) {
                return { message: `❌ عذرًا، لم أجد مادة "${matiereName}" في المستوى ${levelId}.` };
            }

            const materialIds = materials.map(m => m.id);

            // 🔍 Récupérer tous les manuels pour ces matières
            const manuels = await db.Manuel.findAll({
                where: { material_id: { [Op.in]: materialIds } },
                include: [
                    { model: db.Material, as: 'material' }
                ]
            });

            if (!manuels || manuels.length === 0) {
                return { message: `❌ عذرًا، لم أجد كتبًا في مادة "${matiereName}" للمستوى ${levelId}.` };
            }

            return manuels;
        } catch (error) {
            console.error("❌ Erreur dans voir_cours:", error.message);
            return { error: "Erreur lors de la récupération des cours." };
        }
    },

    async voir_manuel(manuelName, page) {
        try {
            const manuel = await manuelService.getManuelByName(manuelName);
            if (!manuel) {
                return { message: `❌ عذرًا، لم أجد كتاب "${manuelName}".` };
            }

            // Si une page est spécifiée, récupérer cette page
            if (page) {
                const pageContent = await manuelService.getManuelPage(manuel.id, page);
                if (!pageContent) {
                    return { message: `❌ عذرًا، لم أجد الصفحة ${page} في كتاب "${manuelName}".` };
                }
                return {
                    titre: manuel.name,
                    couverture: manuel.cover_img ? `📷 صورة الغلاف: ${manuel.cover_img}` : '',
                    contenu: `📄 محتوى الصفحة ${page}:\n${pageContent}`
                };
            }

            // Sinon, récupérer le manuel complet
            return {
                titre: manuel.name,
                couverture: manuel.cover_img ? `📷 صورة الغلاف: ${manuel.cover_img}` : '',
                contenu: `📚 هذا الكتاب يحتوي على ${manuel.pages || 'عدة'} صفحات.`
            };
        } catch (error) {
            console.error("❌ Erreur dans voir_manuel:", error.message);
            return { error: "Erreur lors de la récupération du manuel." };
        }
    },

    async voir_profil_prof(teacherId) {
        return await TeacherService.getTeacherById(teacherId);
    },

    async notifier_parent(userId, data) {
        return await NotificationService.sendNotification({
            user_id: userId,
            title: data.title,
            message: data.message,
            data: data.payload || {},
        });
    },

    async storytime(preferences) {
        return { story: `Une histoire pour toi qui aime ${preferences.join(", ")}` };
    },

    async recommander_professeur(userId, matiereName) {
        // Récupérer le niveau de l'utilisateur
        const user = await db.User.findByPk(userId);
        if (!user || !user.level_id) {
            console.error(`❌ Utilisateur ${userId} non trouvé ou sans niveau défini`);
            return null;
        }

        const levelId = user.level_id;
        console.log(`🔍 Recommandation de professeur pour l'utilisateur ${userId}, matière '${matiereName}', niveau ${levelId}`);

        try {
            // Appeler le service de recommandation avec le niveau
            const response = await RecommendationService.recommendProf(userId, matiereName, levelId);
            
            // Vérifier si la réponse est valide
            if (!response || !response.recommendations || response.recommendations.length === 0) {
                console.log(`⚠️ Aucun professeur trouvé pour l'utilisateur ${userId} (niveau ${levelId}) dans la matière ${matiereName || 'demandée'}`);
                return null;
            }
            
            // Extraire le premier professeur recommandé
            const teacher = response.recommendations[0];
            
            // Vérifier si l'objet teacher est valide
            if (!teacher || !teacher.id) {
                console.error(`❌ Format de réponse invalide pour les recommandations de professeurs`);
                return null;
            }

            // 📌 Ajouter nombre de cours (webinarCount)
            teacher.webinarCount = await db.Webinar.count({
                where: {
                    teacher_id: teacher.id
                }
            });

            // 📌 Ajouter nombre d'abonnés (followersCount)
            teacher.followersCount = await db.Follow.count({
                where: {
                    user_id: teacher.id
                }
            });

            return teacher;
        } catch (error) {
            console.error(`❌ Erreur dans recommander_professeur: ${error.message}`);
            return null;
        }
    },

    /**
     * Recommande des exercices pour un enfant basés sur son niveau scolaire
     * @param {number} userId - ID de l'enfant
     * @param {string} matiereName - Nom de la matière
     * @param {number} limit - Nombre de recommandations souhaitées
     * @returns {Promise<Array|null>} Liste des exercices recommandés ou null si aucun exercice trouvé
     */
    async recommander_exercice(userId, matiereName, limit = 3) {
        try {
            // Vérifier que l'utilisateur existe et a un niveau défini
            const user = await db.User.findByPk(userId);
            if (!user || !user.level_id) {
                console.error(`❌ Utilisateur ${userId} non trouvé ou sans niveau défini`);
                return null;
            }

            // Vérifier que la matière existe
            if (matiereName) {
                const matiere = await db.Material.findOne({ where: { name: matiereName } });
                if (!matiere) {
                    console.error(`❌ Matière "${matiereName}" non trouvée`);
                    return null;
                }
            }

            // Récupérer les exercices recommandés
            const response = await RecommendationService.recommendExercisesByManuel(userId, matiereName, limit);
            
            // Vérifier si la réponse est valide
            if (!response || !response.recommendations || response.recommendations.length === 0) {
                console.log(`⚠️ Aucun exercice trouvé pour l'utilisateur ${userId} (niveau ${user.level_id}) dans la matière ${matiereName || 'demandée'}`);
                return null;
            }
            
            return response.recommendations;
        } catch (error) {
            console.error(`❌ Erreur dans recommander_exercice: ${error.message}`);
            return null;
        }
    },

    /**
     * Recommande des cours pour un enfant
     * @param {number} userId - ID de l'enfant
     * @param {string} matiereName - Nom de la matière
     * @param {number} limit - Nombre de recommandations souhaitées
     * @returns {Promise<Array>} Liste des cours recommandés
     */
    async recommander_cours(userId, matiereName, limit = 3) {
        try {
            const response = await RecommendationService.recommendCourses(userId, matiereName, limit);
            
            // Vérifier si la réponse est valide
            if (!response || !response.recommendations || response.recommendations.length === 0) {
                console.log(`⚠️ Aucun cours trouvé pour l'utilisateur ${userId} dans la matière ${matiereName || 'demandée'}`);
                return null;
            }
            
            return response.recommendations;
        } catch (error) {
            console.error(`❌ Erreur dans recommander_cours: ${error.message}`);
            return null;
        }
    },

    async question_generale(question) {
        return { answer: `Voici une réponse à ta question : ${question}` };
    },

    async accueil() {
        return { greeting: "👋 Bienvenue sur Abajim ! Comment puis-je t'aider aujourd'hui ?" };
    },

    async aide() {
        return {
            help: "Tu peux me demander : voir un cours, faire un quiz, voir un manuel, t'abonner à un prof, ou même écouter une histoire !"
        };
    },

    async ignorer_contenu() {
        return { message: "Je n'ai pas compris, peux-tu reformuler ?" };
    },

    async autre() {
        return { message: "Je suis là pour t'aider, n'hésite pas à poser une question !" };
    },

};
