// services/ChatbotMobileService.js
const db = require("../models");
const Material = db.Material;
const { getBestManuelName } = require('./manuelFinder');
const WebinarService = require("./WebinarService");
const videoService = require("./videoService");
const manuelService = require("./manuelService");
const MeetingService = require("./MeetingService");
const LikeService = require("./LikeService");
const FollowService = require("./FollowService");
const NotificationService = require("./NotificationService");
const TeacherService = require("./TeacherService");
const enfantService = require("./enfantService");
const SaleService = require("./SaleService");
const documentService = require("./documentService");
const userService = require("./userService");
const RecommendationService = require("./RecommendationService");
const { getHistory, addToHistory } = require('./chatMemory');
const { getOpenAIResponse } = require('./openaiService');

const normalize = (s) => s?.trim()?.toLowerCase().replace(/\s/g, '');
const Webinar = require("../models/Webinar");
const User = require("../models/User");
const WebinarChapter = require("../models/WebinarChapter");
const File = require("../models/File");
const FileTranslation = require("../models/FileTranslation");
const matchMatiere = (matiereDB, matiereUser) => {
    const map = {
        'رياضيات': ['رياضيات', 'رياضة', 'math'],
        'الإيقاظ العلمي': ['إيقاظ', 'الإيقاظ', 'علوم', 'science'],
        'الفرنسية': ['فرنسية', 'فرنساوي', 'français'],
        'العربية': ['عربية', 'عربي', 'arabe']
    };

    for (let [dbLabel, variants] of Object.entries(map)) {
        if (variants.some(v => normalize(v) === normalize(matiereUser))) {
            return normalize(matiereDB) === normalize(dbLabel);
        }
    }

    // fallback exact
    return normalize(matiereDB) === normalize(matiereUser);
};


// Mapping local entre levelId et matière_ids
const manualLevelMapping = {
    6: [1, 1, 2],
    7: [3, 4, 4],
    8: [5, 5, 6, 7, 7, 8],
    9: [9, 9, 10, 11, 11, 12, 12],
    10: [13, 14, 14, 15, 16, 16, 17, 17],
    11: [18, 18, 19, 20, 21, 21, 22, 22, 23]
};



const stringSimilarity = require('string-similarity');

function findClosestManuel(manuels, manuelName) {
    const target = normalize(manuelName);
    if (!target || !manuels || manuels.length === 0) return null;

    const matches = manuels.map(m => ({
        manuel: m,
        score: stringSimilarity.compareTwoStrings(normalize(m.name), target)
    }));

    const best = matches.sort((a, b) => b.score - a.score)[0];
    return best?.score > 0.5 ? best.manuel : null;
}

async function repondreAvecMemoireMix(userId, message, context = {}) {
    try {
        const history = await getHistory(userId);
        const gptReply = await getOpenAIResponse(userId, message, history, context);
        await addToHistory(userId, message, gptReply);
        return { message: gptReply };
    } catch (error) {
        console.error('❌ Erreur GPT avec mémoire :', error.message);
        return { error: "Erreur lors de la réponse intelligente." };
    }
}

module.exports = {
    repondreAvecMemoireMix,
    async voir_cours(levelId, matiereName) {
        try {
            const db = require('../models');
            const { Op } = require('sequelize');



            // 🔍 Obtenir tous les matiere_id possibles pour ce niveau
            const matiereIds = manualLevelMapping[levelId] || [];

            // 🔍 Récupérer tous les ID correspondants au nom donné (dans la liste du mapping)
            const materials = await db.Material.findAll({
                where: {
                    name: matiereName,
                    id: { [Op.in]: matiereIds }
                }
            });

            if (!materials || materials.length === 0) {
                return { message: `❌ عذرًا، لم أجد مادة "${matiereName}" في المستوى ${levelId}.` };
            }

            const matchingMatiereIds = materials.map(m => m.id);

            const webinars = await db.Webinar.findAll({
                where: {
                    level_id: levelId,
                    matiere_id: { [Op.in]: matchingMatiereIds },
                    deleted_at: null
                },
                include: [
                    {
                        model: db.User,
                        as: "teacher",
                        attributes: ["id", "full_name", "avatar"]
                    },
                    {
                        model: db.WebinarChapter,
                        as: "chapters",
                        include: [
                            {
                                model: db.File,
                                as: "files",
                                where: {
                                    file_type: "video",
                                    status: "active"
                                },
                                required: true,
                                include: [
                                    {
                                        model: db.FileTranslation,
                                        as: "translations",
                                        where: { locale: "ar" },
                                        required: false,
                                        attributes: ["title"]
                                    }
                                ]
                            }
                        ]
                    }
                ]
            });

            if (!webinars || webinars.length === 0) {
                return { message: `📚 لا يوجد دروس حالياً في "${matiereName}" للمستوى ${levelId}.` };
            }

            return webinars.map((w) => ({
                id: w.id,
                titre: w.slug,
                matiere_id: w.matiere_id,
                image: w.image_cover,
                enseignant: w.teacher?.full_name || "أستاذ غير معروف",
                chapitres: (w.chapters || []).map((ch) => ({
                    id: ch.id,
                    fichiers: (ch.files || []).map((f) => ({
                        id: f.id,
                        titre: f.translations?.[0]?.title || "فيديو بدون عنوان",
                        lien: f.file,
                        type: f.file_type
                    }))
                }))
            }));
        } catch (error) {
            console.error("❌ Erreur dans voir_cours :", error.message);
            return { error: "Erreur lors de la récupération des cours." };
        }
    }
    ,



    async voir_exercices(levelId, matiere, options = {}) {
        try {
            const { manuelName, page } = options;

            // 📘 Récupération de tous les manuels du niveau
            const manuels = await manuelService.getManuelsByLevel(levelId);

            // 🧠 Fonction d’aide pour trouver le manuel le plus proche
            const normalize = (s) => s?.trim()?.toLowerCase().replace(/\s/g, '');

            // 🔍 Trouver le manuel correspondant par nom ou matière
            const manuel = manuelName
                ? findClosestManuel(manuels, manuelName)
                : manuels.find(m => matchMatiere(m.material?.name, matiere));

            if (!manuel) {
                return {
                    message: `❌ لم أجد كتابًا لهذه المادة أو الاسم "${manuelName}" غير معروف.`
                };
            }

            // ✅ Forcer la matière à celle du manuel détecté
            if (manuel.material && manuel.material.name) {
                matiere = manuel.material.name;
                console.log("🎯 Mise à jour automatique de la matière selon le manuel :", matiere);
            }

            console.log("📘 Manuel sélectionné :", manuel.name, "| ID:", manuel.id);

            // 🎯 Récupérer les vidéos associées à ce manuel
            const videos = await videoService.getVideosByManuelId(manuel.id, page);

            const filteredVideos = page
                ? videos.filter(v => v.page && String(v.page).trim() === String(page).trim())
                : videos;

            if (filteredVideos.length > 0) {
                return filteredVideos.map(v => ({
                    titre: v.titre || "📹 فيديو تمرين",
                    lien: v.video,
                    page: v.page,
                    description: v.description || '',
                    thumbnail: v.thumbnail || null
                }));
            }

            // 📄 Fallback PDF (uniquement s’il contient "تمرين" ou "exercice" dans le nom ?)
            const docs = await documentService.getDocumentsByManuel(manuel.id);

            if (docs.length > 0) {
                const exerciseDocs = docs.filter(d => /تمرين|exercice|كراس/i.test(d.name));

                if (exerciseDocs.length > 0) {
                    return exerciseDocs.map(d => ({
                        titre: d.name,
                        lien: d.pdf,
                        type: "PDF",
                        pages: d.nombre_page
                    }));
                }
            }

            // ❌ Aucun exercice trouvé
            return {
                message: `📘 تم العثور على كتاب "${manuel.name}" لكن لا يحتوي على تمارين أو فيديوهات مرتبطة حاليًا.`
            };

        } catch (error) {
            console.error("❌ Erreur dans voir_exercices :", error.message);
            return { error: "Erreur lors de la récupération des exercices." };
        }
    }

    ,

    async voir_meets(levelId) {
        const meetings = await MeetingService.getMeetingsByLevel(levelId);

        if (!meetings || meetings.length === 0) {
            return {
                message: `📭 لا توجد عروض مباشرة حالياً لمستواك. تابعنا بانتظام، سيتم إضافة عروض قريبة قريبًا إن شاء الله !`
            };
        }

        return meetings.map(meeting => {
            const time = meeting.times?.[0] || {};
            const date = time.meet_date
                ? new Date(time.meet_date * 1000).toISOString().split('T')[0]
                : '📅 غير محدد';
            const heure = time.start_time
                ? new Date(time.start_time * 1000).toISOString().split('T')[1]?.slice(0, 5)
                : '🕒 غير محدد';
            const fichiers = meeting.files?.map(f => `- ${f.file_path}`).join('\n') || '📂 لا توجد ملفات مرفقة';

            return `✅ عرض مباشر متاح:
      👨‍🏫 المعلم: ${meeting.teacher?.full_name || 'أستاذ غير معروف'}
      📘 المادة: ${time.material?.name || 'غير محددة'}
      📅 التاريخ: ${date}
      🕒 الساعة: ${heure}
      📁 ملفات مرفقة:
      ${fichiers}`;
        });
    }
    ,

    async abonnement_prof(follower, user_id) {
        try {
            const result = await FollowService.subscribe(follower, user_id);
            console.log("✅ Résultat abonnement :", result);
            return result;
        } catch (error) {
            console.error("❌ Erreur dans abonnement_prof :", error.message);
            throw error;
        }
    },

    async abonnement_platforme(userId) {
        const user = await userService.getLoggedInUser(userId);
        return user ? { message: "Abonnement demandé avec succès" } : { error: "Utilisateur introuvable" };
    },
    async voir_manuels(levelId) {
        return await manuelService.getManuelsByLevel(levelId);
    },
    async voir_details_manuel(userId, userMessage, levelId) {
        const manuels = await manuelService.getManuelsByLevel(levelId);
        if (!manuels || manuels.length === 0) {
            return { message: `❌ لا توجد كتب متاحة حاليًا للمستوى ${levelId}.` };
        }

        const manuelName = await getBestManuelName(userMessage, manuels);
        const manuelTrouve = manuels.find(m => m.name === manuelName);

        if (!manuelTrouve) {
            return {
                message: `❌ لم أتمكن من العثور على الكتاب المطلوب في مستواك.`,
                suggestions: manuels.map(m => `📘 ${m.name}`).join('\n')
            };
        }

        // ✅ Récupérer les documents liés
        const documents = await manuelService.getDocumentsByManuel(manuelTrouve.id);

        if (!documents || documents.length === 0) {
            return {
                titre: manuelTrouve.name,
                couverture: manuelTrouve.logo ? `🖼️ الغلاف: ${manuelTrouve.logo}` : "❌ لا توجد صورة متاحة",
                contenu: "⚠️ لا توجد ملفات مرتبطة بهذا الكتاب حاليًا."
            };
        }

        const docsFormatted = documents.map(doc => `
      📘 ${doc.name}
      📄 عدد الصفحات: ${doc.nombre_page}
      📎 الرابط: ${doc.pdf}
      🧒 نسخة ثلاثية الأبعاد (تلميذ): ${doc["pathenfant"] || "❌ غير متوفرة"}
      `).join("\n\n");

        return {
            titre: manuelTrouve.name,
            couverture: manuelTrouve.logo ? `🖼️ الغلاف: ${manuelTrouve.logo}` : "❌ لا توجد صورة متاحة",
            contenu: docsFormatted
        };
    },

    async voir_profil_prof(teacherId) {
        return await TeacherService.getTeacherById(teacherId);
    },

    //async voir_quizz() {
    // À personnaliser selon ta logique de quiz
    //return { message: "Voici un quiz à faire" };
    //},

    // async voir_favoris(userId) {
    //     return await LikeService.getFavoriteWebinars(userId);
    // },

    // async voir_videos_manuels(manuelId) {
    //     return await videoService.getVideosByManuelId(manuelId);
    // },

    async notifier_parent(userId, data) {
        return await NotificationService.sendNotification({
            user_id: userId,
            title: data.title,
            message: data.message,
            data: data.payload || {},
        });
    },

    async storytime(preferences) {
        return { story: `Une histoire pour toi qui aime ${preferences.join(", ")}` };
    },

    // async recommander_professeur(levelId) {
    //     const all = await WebinarService.getByLevelId(levelId);
    //     return all.length > 0 ? all[0].teacher : null;
    // },
    async recommander_professeur(userId, matiereName) {
        // Récupérer le niveau de l'utilisateur
        const user = await db.User.findByPk(userId);
        if (!user || !user.level_id) {
            console.error(`❌ Utilisateur ${userId} non trouvé ou sans niveau défini`);
            return null;
        }

        const levelId = user.level_id;
        console.log(`🔍 Recommandation de professeur pour l'utilisateur ${userId}, matière '${matiereName}', niveau ${levelId}`);

        // Appeler le service de recommandation avec le niveau
        const response = await RecommendationService.recommendProf(userId, matiereName, levelId);
        const professeurs = response.recommendations || [];

        if (!professeurs || professeurs.length === 0) {
            console.log(`⚠️ Aucun professeur trouvé pour l'utilisateur ${userId} (niveau ${levelId}) dans la matière ${matiereName || 'demandée'}`);
            return null;
        }

        const teacher = professeurs[0];

        // 📌 Ajouter nombre de cours (webinarCount)
        teacher.webinarCount = await db.Webinar.count({
            where: {
                teacher_id: teacher.id
            }
        });

        // 📌 Ajouter derniers cours (lastCourses)
        const lastCourses = await db.Webinar.findAll({
            where: {
                teacher_id: teacher.id
            },
            order: [['created_at', 'DESC']],
            limit: 3
        });

        teacher.lastCourses = lastCourses.map(w => (w.slug && w.slug.trim()) ? w.slug : "درس بدون عنوان");

        // 📌 Chercher la matière réelle de ce prof
        const profMatiereWebinar = await db.Webinar.findOne({
            where: { teacher_id: teacher.id },
            order: [['created_at', 'DESC']]
        });

        const profMatiereId = profMatiereWebinar?.matiere_id;

        let profMatiere = null;
        if (profMatiereId) {
            const matiere = await db.Material.findByPk(profMatiereId);
            profMatiere = matiere?.name || "مادة غير محددة";
        }

        teacher.realMatiere = profMatiere;

        return teacher;
    },

    /**
     * Recommande des exercices pour un enfant basés sur son niveau scolaire
     * @param {number} userId - ID de l'enfant
     * @param {string} matiereName - Nom de la matière
     * @param {number} limit - Nombre de recommandations souhaitées
     * @returns {Promise<Array|null>} Liste des exercices recommandés ou null si aucun exercice trouvé
     */
    async recommander_exercice(userId, matiereName, limit = 3) {
        try {
            // Vérifier que l'utilisateur existe et a un niveau défini
            const user = await db.User.findByPk(userId);
            if (!user || !user.level_id) {
                console.error(`❌ Utilisateur ${userId} non trouvé ou sans niveau défini`);
                return null;
            }

            // Vérifier que la matière existe
            if (matiereName) {
                const matiere = await db.Material.findOne({ where: { name: matiereName } });
                if (!matiere) {
                    console.error(`❌ Matière "${matiereName}" non trouvée`);
                    return null;
                }
            }

            // Récupérer les exercices recommandés
            const exercises = await RecommendationService.recommendExercisesByManuel(userId, matiereName, limit);

            if (!exercises || exercises.length === 0) {
                console.log(`⚠️ Aucun exercice trouvé pour l'utilisateur ${userId} (niveau ${user.level_id}) dans la matière ${matiereName || 'demandée'}`);
                return null;
            }

            console.log(`✅ ${exercises.length} exercices recommandés pour l'utilisateur ${userId} (niveau ${user.level_id}) en ${matiereName || 'toutes matières'}`);
            return exercises;
        } catch (error) {
            console.error(`❌ Erreur dans recommander_exercice: ${error.message}`);
            return null;
        }
    },

    /**
     * Recommande des cours pour un enfant
     * @param {number} userId - ID de l'enfant
     * @param {string} matiereName - Nom de la matière
     * @param {number} limit - Nombre de recommandations souhaitées
     * @returns {Promise<Array>} Liste des cours recommandés
     */
    async recommander_cours(userId, matiereName, limit = 3) {
        try {
            const courses = await RecommendationService.recommendCourses(userId, matiereName, limit);

            if (!courses || courses.length === 0) {
                console.log("⚠️ Aucun cours trouvé pour", userId, "dans la matière", matiereName);
                return null;
            }

            console.log(`✅ ${courses.length} cours recommandés pour ${userId} en ${matiereName}`);
            return courses;
        } catch (error) {
            console.error("❌ Erreur dans recommander_cours:", error.message);
            return null;
        }
    },

    // async recommander_quizz(levelId) {
    //     return { quiz: `Quiz recommandé pour le niveau ${levelId}` };
    // },

    async question_generale(question) {
        return { answer: `Voici une réponse à ta question : ${question}` };
    },

    async accueil() {
        return { greeting: "👋 Bienvenue sur Abajim ! Comment puis-je t’aider aujourd’hui ?" };
    },

    async aide() {
        return {
            help: "Tu peux me demander : voir un cours, faire un quiz, voir un manuel, t’abonner à un prof, ou même écouter une histoire !"
        };
    },

    async ignorer_contenu() {
        return { message: "Je n’ai pas compris, peux-tu reformuler ?" };
    },

    async autre() {
        return { message: "Je suis là pour t’aider, n’hésite pas à poser une question !" };
    },

};

