const { OpenAI } = require('openai');
const openai = new OpenAI({ apiKey: process.env.OPENAI_API_KEY });

/**
 * Détecte l'intention de l'utilisateur en fonction du message fourni
 * @param {string} message - Message de l'utilisateur
 * @returns {Promise<string>} - Intention détectée
 */
const detectIntent = async (message, history = []) => {
  // Vérifier si c'est une demande de recommandation suivante
  const isNextRequest = /suivant|autre|التالي|آخر|أخرى|غيره|غيرها/i.test(message);

  // Rechercher la dernière intention dans l'historique
  let lastIntent = null;
  if (isNextRequest && Array.isArray(history)) {
    // Parcourir l'historique à l'envers pour trouver la dernière intention
    for (let i = history.length - 1; i >= 0; i--) {
      const entry = history[i];
      if (entry.role === 'assistant' && entry.content) {
        // Si la dernière réponse contient une recommandation d'exercice
        if (entry.content.includes('تمرين') &&
            (entry.content.includes('أريد تمرينًا آخر') ||
             entry.content.includes('التالي'))) {
          console.log("🔄 Détection d'une demande de recommandation suivante d'exercice");
          return 'نية: "recommander_exercice"';
        }
        // Si la dernière réponse contient une recommandation de professeur
        else if (entry.content.includes('أستاذ') &&
                (entry.content.includes('أريد أستاذًا آخر') ||
                 entry.content.includes('التالي'))) {
          console.log("🔄 Détection d'une demande de recommandation suivante de professeur");
          return 'نية: "recommander_professeur"';
        }
        // Si la dernière réponse contient une recommandation de cours
        else if ((entry.content.includes('درس') || entry.content.includes('دورة') || entry.content.includes('دروس')) &&
                (entry.content.includes('أريد درسًا آخر') ||
                 entry.content.includes('التالي'))) {
          console.log("🔄 Détection d'une demande de recommandation suivante de cours");
          return 'نية: "recommander_cours"';
        }
        break;
      }
    }

    // Si nous n'avons pas pu déterminer l'intention à partir de l'historique,
    // mais que c'est une demande de "suivant" ou "autre", vérifier s'il existe une session active
    const db = require('../models');
    try {
      const activeSession = await db.ChatbotRecommendation.findOne({
        where: { user_id: history[0]?.user_id },
        order: [['created_at', 'DESC']]
      });

      if (activeSession) {
        // Vérifier le type de recommandation
        if (activeSession.recommendation_type === 'exercise') {
          console.log("🔄 Session active de recommandation d'exercices détectée");
          return 'نية: "recommander_exercice"';
        } else if (activeSession.recommendation_type === 'teacher') {
          console.log("🔄 Session active de recommandation de professeurs détectée");
          return 'نية: "recommander_professeur"';
        } else if (activeSession.recommendation_type === 'course') {
          console.log("🔄 Session active de recommandation de cours détectée");
          return 'نية: "recommander_cours"';
        }
      }
    } catch (error) {
      console.error("❌ Erreur lors de la vérification de la session active:", error.message);
    }
  }

  const historiqueTextuel = history
    .map(entry => `${entry.role === 'user' ? '👦 المستخدم' : '🤖 المساعد'}: ${entry.content}`)
    .join('\n');

  const prompt = `
أنت مساعد ذكي في تصنيف نية الطفل داخل تطبيق تعليمي. يجب أن تعتمد على الجملة الأخيرة **مع الأخذ بعين الاعتبار المحادثة السابقة** لفهم السياق الكامل.

# قائمة النوايا الممكنة:
- "voir_cours":  المستخدم يريد مشاهدة دروس محددة. هذا يشمل أي طلب لمشاهدة درس معين بالاسم (مثل: "نحب نشوف cours sur les fractions" أو "أريد أن أرى درس الضرب").
- "voir_exercices": المستخدم يريد رؤية قائمة التمارين المتاحة في كتاب أو صفحة معينة (مثل: "أرني التمارين في الصفحة 5" أو "ما هي التمارين المتاحة في كتاب الرياضيات").
- "voir_meets": المستخدم يريد لقاء مباشر مع معلم.
- "abonnement_prof": المستخدم يريد الاشتراك في أستاذ معين.
- "abonnement_platforme": المستخدم يريد الاشتراك في المنصة بشكل عام.
- "voir_manuels": المستخدم يريد فتح أو تصفح كتاب مدرسي.
- "voir_details_manuel": المستخدم يريد معرفة تفاصيل كتاب مدرسي محدد.
- "voir_profil_prof": المستخدم يريد معرفة ملف معلم.
- "voir_videos_manuels": المستخدم يريد مشاهدة فيديوهات في كتاب.
- "storytime": المستخدم يطلب قصة ترفيهية.
- "recommander_professeur": المستخدم يطلب اقتراح معلم.
- "recommander_exercice": المستخدم يطلب اقتراح تمرين مناسب له أو توصية شخصية (مثل: "أريد تمرينًا في الرياضيات" أو "اقترح لي تمرينًا" أو "أوصني بتمرين").
- "recommander_cours": المستخدم يطلب اقتراح دروس أو مواد تعليمية.
- "recommander_quizz": المستخدم يطلب اقتراح كويز.
- "question_generale": سؤال عام في التعليم.
- "accueil": تحية أو بدء المحادثة.
- "aide": طلب توجيه عام حول استخدام التطبيق.
- "ignorer_contenu": غير مفهوم.
- "autre": لا ينتمي لأي مما سبق.

# ملاحظة مهمة:
إذا كان المستخدم يطلب "تمرين آخر" أو "التالي" بعد أن تم اقتراح تمرين له، فهذه نية "recommander_exercice" وليست "voir_exercices".
إذا كان المستخدم يطلب "أستاذ آخر" أو "التالي" بعد أن تم اقتراح أستاذ له، فهذه نية "recommander_professeur".
إذا كان المستخدم يطلب "درس آخر" أو "التالي" بعد أن تم اقتراح درس له، فهذه نية "recommander_cours".

# الفرق بين voir_exercices و recommander_exercice:
- "voir_exercices": عندما يريد المستخدم رؤية قائمة التمارين المتاحة في كتاب أو صفحة محددة
- "recommander_exercice": عندما يريد المستخدم توصية شخصية لتمرين مناسب له (مثل: "أريد تمرينًا" أو "اقترح لي تمرينًا")

# سياق المحادثة:
${historiqueTextuel}

# الجملة الأخيرة التي أرسلها الطفل:
"${message}"

✅ استخرج فقط النية **المرتبطة بالجملة الأخيرة** مع أخذ ما قبلها بعين الاعتبار. لا تشرح، فقط أجب بهذا الشكل:
نية: "اسم_النية"
  `;

  try {
    const result = await openai.chat.completions.create({
      model: 'gpt-4o-mini',
      messages: [
        { role: 'system', content: 'أنت مصنف نيات ذكي للأطفال. استخدم المحادثة بالكامل لتحديد نيتهم الأخيرة.' },
        { role: 'user', content: prompt }
      ],
      temperature: 0.5,
      max_tokens: 100,
    });

    const reply = result.choices[0].message.content.trim();
    console.log("🎯 Intention détectée :", reply);
    return reply;
  } catch (error) {
    console.error('❌ Erreur dans detectIntent:', error.message);
    return 'نية: "ignorer_contenu"';
  }
};


module.exports = { detectIntent };
