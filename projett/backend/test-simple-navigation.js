#!/usr/bin/env node

/**
 * Test simple du système de navigation
 */

const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '.env') });

const ChatbotMobileService = require('./public/src/services/ChatbotMobileService');

async function testSimpleNavigation() {
    console.log('🧪 TEST SIMPLE DE NAVIGATION');
    console.log('='.repeat(40));
    
    try {
        // Test avec niveau 6 (1ère année) et une matière existante
        console.log('\n📋 Test: Recherche de cours en mathématiques');
        console.log('📊 Niveau: 6 (1ère année)');
        console.log('📚 Matière: Mathématiques');
        console.log('💬 Message: "نحب نشوف cours sur les fractions"');
        
        const result = await ChatbotMobileService.voir_cours(
            6, 
            "Mathématiques", 
            "نحب نشوف cours sur les fractions"
        );
        
        console.log('\n✅ RÉSULTAT:');
        console.log(`Success: ${result.success}`);
        console.log(`Message: ${result.message || result.messageAr || 'Aucun message'}`);
        
        if (result.data && result.data.courses) {
            console.log(`📚 Cours trouvés: ${result.data.courses.length}`);
            result.data.courses.forEach((course, index) => {
                console.log(`  ${index + 1}. ${course.title} (ID: ${course.id})`);
            });
        }
        
        if (result.navigation) {
            console.log(`🧭 Navigation: ${result.navigation.screen}`);
        }
        
    } catch (error) {
        console.error('❌ Erreur:', error.message);
        console.error(error.stack);
    } finally {
        // Fermer la connexion
        try {
            const db = require('./public/src/models');
            if (db && db.sequelize) {
                await db.sequelize.close();
                console.log('\n🔌 Connexion fermée');
            }
        } catch (closeError) {
            console.error('⚠️ Erreur fermeture DB:', closeError.message);
        }
    }
}

// Exécuter si appelé directement
if (require.main === module) {
    testSimpleNavigation().catch(console.error);
}

module.exports = { testSimpleNavigation };
