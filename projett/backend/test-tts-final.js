#!/usr/bin/env node

/**
 * Test final du système TTS
 * Vérifie que tout fonctionne correctement
 */

const axios = require('axios');
const fs = require('fs');
const path = require('path');

const BASE_URL = 'http://192.168.0.123:5001';
const TEST_USER_ID = 3712;

async function testTTSSystem() {
    console.log('🧪 TEST FINAL DU SYSTÈME TTS');
    console.log('============================');
    
    try {
        // Test 1: Vérifier que le serveur répond
        console.log('1️⃣ Test de connexion au serveur...');
        const healthCheck = await axios.get(`${BASE_URL}/api/health`).catch(() => null);
        if (!healthCheck) {
            console.log('⚠️ Endpoint /health non disponible, test avec /uploads...');
        } else {
            console.log('✅ Serveur accessible');
        }
        
        // Test 2: Vérifier que le fichier audio fallback existe et est accessible
        console.log('2️⃣ Test d\'accessibilité du fichier audio fallback...');
        const audioResponse = await axios.head(`${BASE_URL}/uploads/asmaa_cache/asmaa_fallback.mp3`);
        console.log(`✅ Fichier audio accessible (${audioResponse.headers['content-length']} bytes)`);
        console.log(`📊 Type: ${audioResponse.headers['content-type']}`);
        console.log(`🔒 CORS: ${audioResponse.headers['access-control-allow-origin']}`);
        
        // Test 3: Envoyer un message au chatbot
        console.log('3️⃣ Test d\'envoi de message au chatbot...');
        const chatResponse = await axios.post(`${BASE_URL}/api/chatbot/ask`, {
            message: 'Test TTS',
            userId: TEST_USER_ID,
            messageType: 'text'
        });
        
        console.log('✅ Message envoyé avec succès');
        console.log(`📝 Réponse: ${chatResponse.data.reply?.substring(0, 50)}...`);
        
        // Test 4: Vérifier l'URL audio dans la réponse
        console.log('4️⃣ Test de l\'URL audio générée...');
        console.log('📊 Réponse complète:', JSON.stringify(chatResponse.data, null, 2));

        const audioUrl = chatResponse.data.audio || chatResponse.data.tts;
        if (audioUrl) {
            console.log(`🔊 URL audio trouvée: ${audioUrl}`);

            // Vérifier que l'URL n'a pas de paramètres dupliqués
            const duplicateParams = audioUrl.match(/(\?|&)([^=]+=[^&]*)\1\2/);
            if (duplicateParams) {
                console.log('❌ ERREUR: Paramètres dupliqués détectés dans l\'URL');
                console.log(`🔍 Duplication: ${duplicateParams[0]}`);
            } else {
                console.log('✅ URL audio propre (pas de duplication)');
            }

            // Tester l'accès à l'URL audio
            const fullAudioUrl = audioUrl.startsWith('http') ? audioUrl : `${BASE_URL}${audioUrl}`;
            const audioTest = await axios.head(fullAudioUrl);
            console.log('✅ Fichier audio accessible via l\'URL générée');

        } else {
            console.log('❌ ERREUR: Aucune URL audio dans la réponse');
            console.log('🔍 Clés disponibles:', Object.keys(chatResponse.data));
        }
        
        // Test 5: Vérifier les logs du serveur
        console.log('5️⃣ Résumé des tests...');
        console.log('✅ Serveur accessible');
        console.log('✅ Fichier audio fallback valide');
        console.log('✅ Chatbot répond correctement');
        console.log('✅ URL audio générée et accessible');
        console.log('✅ Headers CORS configurés');
        
        console.log('\n🎉 TOUS LES TESTS RÉUSSIS !');
        console.log('🎵 Le système TTS est opérationnel');
        console.log('\n📱 Vous pouvez maintenant tester depuis votre frontend mobile');
        
    } catch (error) {
        console.error('❌ ERREUR LORS DU TEST:', error.message);
        
        if (error.response) {
            console.error(`📊 Status: ${error.response.status}`);
            console.error(`📝 Data: ${JSON.stringify(error.response.data, null, 2)}`);
        }
        
        console.log('\n🔧 ACTIONS À PRENDRE:');
        console.log('1. Vérifier que le serveur backend tourne sur le port 5001');
        console.log('2. Vérifier que le fichier asmaa_fallback.mp3 existe');
        console.log('3. Vérifier les logs du serveur pour plus de détails');
        
        process.exit(1);
    }
}

// Fonction pour vérifier les fichiers locaux
function checkLocalFiles() {
    console.log('\n📁 VÉRIFICATION DES FICHIERS LOCAUX:');
    
    const files = [
        'uploads/asmaa_cache/asmaa_fallback.mp3',
        'assets/audio/asmaa_fallback.mp3'
    ];
    
    files.forEach(file => {
        const fullPath = path.join(__dirname, file);
        if (fs.existsSync(fullPath)) {
            const stats = fs.statSync(fullPath);
            console.log(`✅ ${file} (${stats.size} bytes)`);
        } else {
            console.log(`❌ ${file} manquant`);
        }
    });
}

// Exécuter les tests
if (require.main === module) {
    checkLocalFiles();
    testTTSSystem();
}

module.exports = { testTTSSystem, checkLocalFiles };
