#!/usr/bin/env node

/**
 * Script pour créer des données de test pour le système de navigation
 */

const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '.env') });

const db = require('./public/src/models');

async function createTestData() {
    console.log('🎯 CRÉATION DES DONNÉES DE TEST');
    console.log('='.repeat(50));
    
    try {
        // 1. Créer une matière "Mathématiques" si elle n'existe pas
        console.log('\n📚 Création de la matière Mathématiques...');
        const [mathMaterial, created] = await db.Material.findOrCreate({
            where: { name: 'Mathématiques' },
            defaults: {
                name: 'Mathématiques',
                section_id: 1
            }
        });
        
        if (created) {
            console.log(`✅ Matière créée: ${mathMaterial.name} (ID: ${mathMaterial.id})`);
        } else {
            console.log(`✅ Matière existante: ${mathMaterial.name} (ID: ${mathMaterial.id})`);
        }
        
        // 2. C<PERSON>er un utilisateur enseignant si il n'existe pas
        console.log('\n👨‍🏫 Création de l\'enseignant...');
        const [teacher, teacherCreated] = await db.User.findOrCreate({
            where: { email: '<EMAIL>' },
            defaults: {
                full_name: 'Professeur Ahmed',
                email: '<EMAIL>',
                role_id: 4, // Role enseignant
                status: 'active'
            }
        });
        
        if (teacherCreated) {
            console.log(`✅ Enseignant créé: ${teacher.full_name} (ID: ${teacher.id})`);
        } else {
            console.log(`✅ Enseignant existant: ${teacher.full_name} (ID: ${teacher.id})`);
        }
        
        // 3. Créer des cours de test
        console.log('\n📖 Création des cours de test...');
        const coursesData = [
            {
                slug: 'cours-fractions',
                level_id: 6, // 1ère année
                matiere_id: mathMaterial.id,
                teacher_id: teacher.id,
                status: 'active',
                translations: [
                    { title: 'درس الكسور', locale: 'ar', description: 'تعلم الكسور والعمليات عليها' },
                    { title: 'Cours sur les fractions', locale: 'fr', description: 'Apprendre les fractions et leurs opérations' }
                ]
            },
            {
                slug: 'cours-geometrie',
                level_id: 6, // 1ère année
                matiere_id: mathMaterial.id,
                teacher_id: teacher.id,
                status: 'active',
                translations: [
                    { title: 'درس الهندسة', locale: 'ar', description: 'تعلم الأشكال الهندسية' },
                    { title: 'Cours de géométrie', locale: 'fr', description: 'Apprendre les formes géométriques' }
                ]
            },
            {
                slug: 'cours-addition',
                level_id: 6, // 1ère année
                matiere_id: mathMaterial.id,
                teacher_id: teacher.id,
                status: 'active',
                translations: [
                    { title: 'درس الجمع', locale: 'ar', description: 'تعلم عمليات الجمع' },
                    { title: 'Cours d\'addition', locale: 'fr', description: 'Apprendre les additions' }
                ]
            }
        ];
        
        for (const courseData of coursesData) {
            const [course, courseCreated] = await db.Webinar.findOrCreate({
                where: { slug: courseData.slug },
                defaults: {
                    slug: courseData.slug,
                    level_id: courseData.level_id,
                    matiere_id: courseData.matiere_id,
                    teacher_id: courseData.teacher_id,
                    status: courseData.status,
                    type: 'course',
                    private: 0
                }
            });
            
            if (courseCreated) {
                console.log(`✅ Cours créé: ${courseData.slug} (ID: ${course.id})`);
                
                // Créer les traductions
                for (const translation of courseData.translations) {
                    await db.WebinarTranslation.findOrCreate({
                        where: { 
                            webinar_id: course.id,
                            locale: translation.locale
                        },
                        defaults: {
                            webinar_id: course.id,
                            title: translation.title,
                            locale: translation.locale,
                            description: translation.description
                        }
                    });
                }
                console.log(`  📝 Traductions créées pour ${courseData.slug}`);
                
                // Créer des chapitres de test
                const chaptersData = [
                    { order: 1, status: 'active' },
                    { order: 2, status: 'active' }
                ];
                
                for (const chapterData of chaptersData) {
                    const [chapter, chapterCreated] = await db.WebinarChapter.findOrCreate({
                        where: { 
                            webinar_id: course.id,
                            order: chapterData.order
                        },
                        defaults: {
                            webinar_id: course.id,
                            order: chapterData.order,
                            status: chapterData.status
                        }
                    });
                    
                    if (chapterCreated) {
                        console.log(`  📑 Chapitre ${chapterData.order} créé (ID: ${chapter.id})`);
                        
                        // Créer des fichiers de test
                        const filesData = [
                            { 
                                file_type: 'video', 
                                order: 1, 
                                status: 'active',
                                translations: [
                                    { title: 'فيديو تعليمي', locale: 'ar', description: 'فيديو تعليمي للدرس' },
                                    { title: 'Vidéo éducative', locale: 'fr', description: 'Vidéo éducative du cours' }
                                ]
                            },
                            { 
                                file_type: 'quiz', 
                                order: 2, 
                                status: 'active',
                                translations: [
                                    { title: 'اختبار', locale: 'ar', description: 'اختبار لتقييم المعرفة' },
                                    { title: 'Quiz', locale: 'fr', description: 'Quiz pour évaluer les connaissances' }
                                ]
                            }
                        ];
                        
                        for (const fileData of filesData) {
                            const [file, fileCreated] = await db.File.findOrCreate({
                                where: { 
                                    chapter_id: chapter.id,
                                    file_type: fileData.file_type,
                                    order: fileData.order
                                },
                                defaults: {
                                    chapter_id: chapter.id,
                                    file_type: fileData.file_type,
                                    order: fileData.order,
                                    status: fileData.status,
                                    file: `test_${fileData.file_type}_${chapter.id}_${fileData.order}.mp4`
                                }
                            });
                            
                            if (fileCreated) {
                                console.log(`    📁 Fichier ${fileData.file_type} créé (ID: ${file.id})`);
                                
                                // Créer les traductions de fichier
                                for (const translation of fileData.translations) {
                                    await db.FileTranslation.findOrCreate({
                                        where: { 
                                            file_id: file.id,
                                            locale: translation.locale
                                        },
                                        defaults: {
                                            file_id: file.id,
                                            title: translation.title,
                                            locale: translation.locale,
                                            description: translation.description
                                        }
                                    });
                                }
                                console.log(`      📝 Traductions de fichier créées`);
                            }
                        }
                    }
                }
            } else {
                console.log(`✅ Cours existant: ${courseData.slug} (ID: ${course.id})`);
            }
        }
        
        console.log('\n🎉 DONNÉES DE TEST CRÉÉES AVEC SUCCÈS !');
        console.log('='.repeat(50));
        console.log('📊 Résumé:');
        console.log(`  📚 Matière: ${mathMaterial.name} (ID: ${mathMaterial.id})`);
        console.log(`  👨‍🏫 Enseignant: ${teacher.full_name} (ID: ${teacher.id})`);
        console.log(`  📖 Cours créés: ${coursesData.length}`);
        console.log('');
        console.log('🧪 Vous pouvez maintenant tester le système de navigation avec:');
        console.log('  - "نحب نشوف cours sur les fractions"');
        console.log('  - "أريد درس في الهندسة"');
        console.log('  - "عرضلي دروس الرياضيات"');
        
    } catch (error) {
        console.error('❌ Erreur lors de la création des données de test:', error);
    } finally {
        await db.sequelize.close();
        console.log('\n🔌 Connexion fermée');
    }
}

// Exécuter si appelé directement
if (require.main === module) {
    createTestData().catch(console.error);
}

module.exports = { createTestData };
