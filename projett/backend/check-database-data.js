#!/usr/bin/env node

/**
 * Script pour vérifier les données de la base de données
 */

const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '.env') });

const db = require('./public/src/models');

async function checkDatabaseData() {
    console.log('🔍 VÉRIFICATION DES DONNÉES DE LA BASE');
    console.log('='.repeat(50));
    
    try {
        // Vérifier les matières
        console.log('\n📚 MATIÈRES DISPONIBLES:');
        const materials = await db.Material.findAll({
            attributes: ['id', 'name'],
            limit: 20
        });
        
        console.log(`✅ ${materials.length} matières trouvées:`);
        materials.forEach(material => {
            console.log(`  ${material.id}: ${material.name}`);
        });
        
        // Vérifier les niveaux
        console.log('\n🎓 NIVEAUX DISPONIBLES:');
        const levels = await db.Level.findAll({
            attributes: ['id', 'name'],
            limit: 20
        });
        
        console.log(`✅ ${levels.length} niveaux trouvés:`);
        levels.forEach(level => {
            console.log(`  ${level.id}: ${level.name}`);
        });
        
        // Vérifier les cours (webinars)
        console.log('\n📖 COURS DISPONIBLES:');
        const webinars = await db.Webinar.findAll({
            attributes: ['id', 'level_id', 'matiere_id', 'status'],
            include: [
                {
                    model: db.WebinarTranslation,
                    attributes: ['title', 'locale']
                }
            ],
            limit: 10
        });
        
        console.log(`✅ ${webinars.length} cours trouvés:`);
        webinars.forEach(webinar => {
            const title = webinar.WebinarTranslations?.find(t => t.locale === 'ar')?.title || 
                         webinar.WebinarTranslations?.find(t => t.locale === 'fr')?.title || 
                         'Sans titre';
            console.log(`  ${webinar.id}: "${title}" (Niveau: ${webinar.level_id}, Matière: ${webinar.matiere_id}, Status: ${webinar.status})`);
        });
        
        // Vérifier les chapitres
        console.log('\n📑 CHAPITRES DISPONIBLES:');
        const chapters = await db.WebinarChapter.findAll({
            attributes: ['id', 'webinar_id', 'order', 'status'],
            limit: 10
        });
        
        console.log(`✅ ${chapters.length} chapitres trouvés:`);
        chapters.forEach(chapter => {
            console.log(`  ${chapter.id}: Webinar ${chapter.webinar_id}, Ordre: ${chapter.order}, Status: ${chapter.status}`);
        });
        
        // Vérifier les fichiers
        console.log('\n📁 FICHIERS DISPONIBLES:');
        const files = await db.File.findAll({
            attributes: ['id', 'chapter_id', 'file_type', 'status'],
            include: [
                {
                    model: db.FileTranslation,
                    as: 'translations',
                    attributes: ['title', 'locale']
                }
            ],
            limit: 10
        });
        
        console.log(`✅ ${files.length} fichiers trouvés:`);
        files.forEach(file => {
            const title = file.translations?.find(t => t.locale === 'ar')?.title || 
                         file.translations?.find(t => t.locale === 'fr')?.title || 
                         'Sans titre';
            console.log(`  ${file.id}: "${title}" (Chapitre: ${file.chapter_id}, Type: ${file.file_type}, Status: ${file.status})`);
        });
        
        // Analyser les relations niveau-matière
        console.log('\n🔗 ANALYSE DES RELATIONS NIVEAU-MATIÈRE:');
        const webinarStats = await db.Webinar.findAll({
            attributes: ['level_id', 'matiere_id'],
            where: {
                status: 'active',
                deleted_at: null
            },
            group: ['level_id', 'matiere_id'],
            raw: true
        });
        
        const levelMatiereMap = {};
        webinarStats.forEach(stat => {
            if (!levelMatiereMap[stat.level_id]) {
                levelMatiereMap[stat.level_id] = [];
            }
            levelMatiereMap[stat.level_id].push(stat.matiere_id);
        });
        
        console.log('📊 Mapping Niveau -> Matières:');
        Object.keys(levelMatiereMap).forEach(levelId => {
            console.log(`  Niveau ${levelId}: Matières [${levelMatiereMap[levelId].join(', ')}]`);
        });
        
        // Générer le mapping pour le code
        console.log('\n🔧 MAPPING POUR LE CODE:');
        console.log('const manualLevelMapping = {');
        Object.keys(levelMatiereMap).forEach(levelId => {
            console.log(`    ${levelId}: [${levelMatiereMap[levelId].join(', ')}],`);
        });
        console.log('};');
        
    } catch (error) {
        console.error('❌ Erreur:', error);
    } finally {
        await db.sequelize.close();
        console.log('\n🔌 Connexion fermée');
    }
}

// Exécuter si appelé directement
if (require.main === module) {
    checkDatabaseData().catch(console.error);
}

module.exports = { checkDatabaseData };
