# 🔧 SOLUTION : Problème de redémarrage en boucle de Nodemon

## 🔍 **PROBLÈME IDENTIFIÉ**

Votre backend redémarre en boucle à cause de :

1. **Nodemon surveille TOUS les fichiers** par défaut
2. **Chaque réponse TTS génère un nouveau fichier audio** dans `/uploads/`
3. **Nodemon détecte ces nouveaux fichiers** et redémarre le serveur
4. **Au redémarrage**, `audioCacheManager.js` génère un nouveau manifeste
5. **Cela crée une boucle infinie** de redémarrages

## ✅ **SOLUTIONS APPLIQUÉES**

### 1. Configuration Nodemon
- ✅ Ajout de `nodemonConfig` dans `package.json`
- ✅ Création de `.nodemonignore`
- ✅ Nodemon ignore maintenant : `uploads/`, `*.mp3`, `*.wav`, `*.m4a`, `logs/`

### 2. Optimisation du Cache Audio
- ✅ Cache intelligent dans `ttsService.js` (5 minutes)
- ✅ Évite la régénération du même texte
- ✅ Nettoyage conditionnel dans `audioCacheManager.js`

### 3. Scripts de Nettoyage
- ✅ Script `cleanup-audio.js` pour supprimer les anciens fichiers
- ✅ Commande `npm run cleanup` disponible
- ✅ Commande `npm run dev-clean` (nettoyage + démarrage)

## 🚀 **COMMENT REDÉMARRER PROPREMENT**

### Option 1 : Redémarrage simple
```bash
cd projett/backend
npm run dev
```

### Option 2 : Redémarrage avec nettoyage
```bash
cd projett/backend
npm run dev-clean
```

### Option 3 : Nettoyage manuel puis redémarrage
```bash
cd projett/backend
npm run cleanup
npm run dev
```

## 🔧 **VÉRIFICATIONS**

1. **Arrêter le serveur actuel** : `Ctrl+C` dans le terminal
2. **Vérifier qu'aucun processus Node ne tourne** : `ps aux | grep node`
3. **Nettoyer les fichiers audio** : `npm run cleanup`
4. **Redémarrer** : `npm run dev`

## 📊 **MONITORING**

Vous devriez maintenant voir :
- ✅ `⏭️ Nettoyage audio ignoré en mode développement`
- ✅ `♻️ Utilisation du cache audio` (pour les textes répétés)
- ✅ Plus de redémarrages en boucle

## 🎯 **CIRCUIT TTS OPTIMISÉ**

1. **Message utilisateur** → `ChatbotController.js`
2. **Génération réponse** → `openaiService.js`
3. **Cache check** → `ttsService.js` (nouveau)
4. **Si pas en cache** → `directElevenLabsService.js`
5. **Génération audio** → ElevenLabs API
6. **Sauvegarde + cache** → `/uploads/`
7. **Retour frontend** → Chemin audio avec cache-busting

## 🚨 **EN CAS DE PROBLÈME PERSISTANT**

Si le problème persiste :

1. **Supprimer tous les fichiers audio** :
   ```bash
   rm -rf projett/backend/uploads/*.mp3
   rm -rf projett/backend/uploads/*.wav
   rm -rf projett/backend/uploads/*.m4a
   ```

2. **Redémarrer complètement** :
   ```bash
   pkill -f "node.*index.js"
   npm run dev
   ```

3. **Vérifier la configuration** :
   - `.nodemonignore` existe
   - `NODE_ENV=development` dans `.env`
   - `nodemonConfig` dans `package.json`

## 📝 **LOGS À SURVEILLER**

- ✅ `🎵 Génération vocale pour:` (nouveau audio)
- ✅ `♻️ Utilisation du cache audio` (cache hit)
- ✅ `⏭️ Nettoyage audio ignoré` (pas de génération de fichiers au démarrage)
- ❌ `[nodemon] restarting due to changes...` (ne devrait plus apparaître en boucle)
