/**
 * Test script for the recommendation service.
 * This script tests the communication between the backend and the recommendation service.
 */

const RecommendationService = require('./public/src/services/RecommendationService');

// Test parameters
const userId = 3707;
const matiereName = 'رياضيات';
const levelId = 9;
const limit = 3;

// Test the teacher recommendation service
async function testTeacherRecommendation() {
  console.log('Testing teacher recommendation service...');
  console.log(`Parameters: userId=${userId}, matiereName=${matiereName}, levelId=${levelId}, limit=${limit}`);
  
  try {
    const result = await RecommendationService.recommendProf(userId, matiereName, levelId, limit);
    console.log('Teacher recommendation result:');
    console.log(JSON.stringify(result, null, 2));
  } catch (error) {
    console.error('Error in teacher recommendation:', error.message);
    if (error.response) {
      console.error('API response status:', error.response.status);
      console.error('API response data:', JSON.stringify(error.response.data));
    } else if (error.request) {
      console.error('No response received:', error.request);
    }
  }
}

// Test the exercise recommendation service
async function testExerciseRecommendation() {
  console.log('\nTesting exercise recommendation service...');
  console.log(`Parameters: userId=${userId}, matiereName=${matiereName}, levelId=${levelId}, limit=${limit}`);
  
  try {
    const result = await RecommendationService.recommendExercisesByManuel(userId, matiereName, levelId, limit);
    console.log('Exercise recommendation result:');
    console.log(JSON.stringify(result, null, 2));
  } catch (error) {
    console.error('Error in exercise recommendation:', error.message);
    if (error.response) {
      console.error('API response status:', error.response.status);
      console.error('API response data:', JSON.stringify(error.response.data));
    } else if (error.request) {
      console.error('No response received:', error.request);
    }
  }
}

// Check if the API is available
async function checkApiAvailability() {
  console.log('\nChecking API availability...');
  
  try {
    const isAvailable = await RecommendationService.isApiAvailable();
    console.log(`API available: ${isAvailable}`);
    return isAvailable;
  } catch (error) {
    console.error('Error checking API availability:', error.message);
    return false;
  }
}

// Run the tests
async function runTests() {
  const isApiAvailable = await checkApiAvailability();
  
  if (isApiAvailable) {
    await testTeacherRecommendation();
    await testExerciseRecommendation();
  } else {
    console.error('Recommendation API is not available. Make sure it is running on http://localhost:8000/api');
  }
}

// Run the tests
runTests().then(() => {
  console.log('\nTests completed.');
  process.exit(0);
}).catch(error => {
  console.error('Error running tests:', error);
  process.exit(1);
});
