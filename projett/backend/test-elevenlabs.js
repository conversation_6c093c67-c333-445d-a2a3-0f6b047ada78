/**
 * Script de test pour l'API ElevenLabs
 * Ce script teste directement la clé API et génère un fichier audio de test
 */
const fs = require('fs');
const path = require('path');
const axios = require('axios');
const dotenv = require('dotenv');

// Charger les variables d'environnement
dotenv.config();

// Récupérer la clé API brute
const rawApiKey = process.env.ELEVENLABS_API_KEY;
console.log(`🔑 Clé API brute: ${rawApiKey.substring(0, 5)}...${rawApiKey.substring(rawApiKey.length - 5)}`);

// Nettoyer la clé API (supprimer les caractères non alphanumériques)
const cleanedApiKey = rawApiKey.trim().replace(/[^a-zA-Z0-9]/g, '');
console.log(`🔑 Clé API nettoyée: ${cleanedApiKey.substring(0, 5)}...${cleanedApiKey.substring(cleanedApiKey.length - 5)}`);

// Voix féminine arabe Asmaa
const FEMALE_VOICE_ID = "qi4PkV9c01kb869Vh7Su";

// Texte de test simple
const testText = "مرحبا، هذا اختبار للتأكد من أن الصوت يعمل بشكل صحيح.";

/**
 * Vérifie la validité de la clé API ElevenLabs
 */
async function verifyApiKey() {
    try {
        console.log("🔍 Vérification de la clé API...");
        const response = await axios.get("https://api.elevenlabs.io/v1/user", {
            headers: {
                "xi-api-key": cleanedApiKey
            }
        });
        
        console.log("✅ Clé API valide. Utilisateur:", response.data.subscription?.tier || "Free");
        return true;
    } catch (error) {
        console.error("❌ Erreur de vérification:", 
            error.response?.status, 
            error.response?.data?.detail || error.message);
        return false;
    }
}

/**
 * Génère un fichier audio de test
 */
async function generateTestAudio() {
    try {
        console.log("🔊 Génération d'un fichier audio de test...");
        
        // Test avec l'API directe
        const response = await axios({
            method: 'post',
            url: `https://api.elevenlabs.io/v1/text-to-speech/${FEMALE_VOICE_ID}`,
            headers: {
                'xi-api-key': cleanedApiKey,
                'Content-Type': 'application/json',
                'Accept': 'audio/mpeg'
            },
            data: {
                text: testText,
                model_id: "eleven_multilingual_v1",
                voice_settings: {
                    stability: 0.8,
                    similarity_boost: 0.5
                }
            },
            responseType: 'arraybuffer'
        });
        
        // Vérifier la réponse
        if (response.status !== 200) {
            throw new Error(`Erreur API: ${response.status}`);
        }
        
        // Créer le dossier de test si nécessaire
        const testDir = path.join(__dirname, "test-audio");
        if (!fs.existsSync(testDir)) {
            fs.mkdirSync(testDir, { recursive: true });
        }
        
        // Sauvegarder le fichier audio
        const testFile = path.join(testDir, "test-asmaa.mp3");
        fs.writeFileSync(testFile, Buffer.from(response.data));
        
        console.log(`✅ Fichier audio sauvegardé: ${testFile}`);
        return true;
    } catch (error) {
        console.error("❌ Erreur de génération audio:", 
            error.response?.status || error.message,
            error.response?.data?.detail || "");
            
        // Essayer avec l'endpoint de streaming
        try {
            console.log("🚨 Tentative avec l'endpoint de streaming...");
            
            const streamResponse = await axios({
                method: 'post',
                url: `https://api.elevenlabs.io/v1/text-to-speech/${FEMALE_VOICE_ID}/stream`,
                headers: {
                    'xi-api-key': cleanedApiKey,
                    'Content-Type': 'application/json'
                },
                data: {
                    text: testText,
                    model_id: "eleven_monolingual_v1",
                    output_format: "mp3_44100_128"
                },
                responseType: 'arraybuffer'
            });
            
            // Créer le dossier de test si nécessaire
            const testDir = path.join(__dirname, "test-audio");
            if (!fs.existsSync(testDir)) {
                fs.mkdirSync(testDir, { recursive: true });
            }
            
            // Sauvegarder le fichier audio
            const testFile = path.join(testDir, "test-asmaa-stream.mp3");
            fs.writeFileSync(testFile, Buffer.from(streamResponse.data));
            
            console.log(`✅ Fichier audio de streaming sauvegardé: ${testFile}`);
            return true;
        } catch (streamError) {
            console.error("❌ Erreur avec l'endpoint de streaming:", 
                streamError.response?.status || streamError.message);
            return false;
        }
    }
}

/**
 * Fonction principale
 */
async function main() {
    console.log("🧪 Test de l'API ElevenLabs");
    
    // Vérifier la clé API
    const isValid = await verifyApiKey();
    if (!isValid) {
        console.error("❌ La clé API n'est pas valide. Impossible de continuer.");
        return;
    }
    
    // Générer un fichier audio de test
    const audioGenerated = await generateTestAudio();
    if (audioGenerated) {
        console.log("✅ Test réussi! L'API ElevenLabs fonctionne correctement.");
    } else {
        console.error("❌ Échec du test. L'API ElevenLabs ne fonctionne pas correctement.");
    }
}

// Exécuter le test
main().catch(error => {
    console.error("❌ Erreur non gérée:", error);
});
