const db = require("./public/src/models");
const { Op } = require("sequelize");

(async () => {
  try {
    await db.sequelize.authenticate();
    console.log("✅ Connexion à la base réussie");

    const levelId = 9;

    // 1. R<PERSON>cup<PERSON> tous les enfants du level
    const enfants = await db.User.findAll({
      where: { level_id: levelId, role_id: 8 }
    });

    // 2. Récupère tous les profs du même level
    const teacherLinks = await db.UserLevel.findAll({
      where: { level_id: levelId }
    });

    const teacherIds = teacherLinks.map((e) => e.teacher_id);

    // 3. <PERSON>ur chaque enfant, suivre N profs random parmi les bons
    for (const enfant of enfants) {
      // Choisir aléatoirement 3 à 5 profs différents
      const shuffled = teacherIds.sort(() => 0.5 - Math.random());
      const selectedTeachers = shuffled.slice(0, Math.floor(Math.random() * 3) + 3); // 3 à 5 profs

      for (const teacherId of selectedTeachers) {
        if (teacherId !== enfant.id) {
          const alreadyExists = await db.Follow.findOne({
            where: { follower: enfant.id, user_id: teacherId }
          });
          if (!alreadyExists) {
            await db.Follow.create({
              follower: enfant.id,
              user_id: teacherId,
              status: "accepted",
              created_at: new Date(),
              updated_at: new Date()
            });
          }
        }
      }
    }

    console.log("✅ Follows random générés avec succès !");
  } catch (error) {
    console.error("❌ Erreur :", error);
  }
})();
