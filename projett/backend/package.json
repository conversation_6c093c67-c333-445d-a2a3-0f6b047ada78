{"name": "chatbot-vocal-<PERSON><PERSON><PERSON><PERSON>", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node index.js", "dev": "nodemon index.js", "cleanup": "node cleanup-audio.js", "dev-clean": "npm run cleanup && npm run dev", "create-fallback": "node create-fallback-audio.js", "setup-audio": "npm run create-fallback && npm run cleanup"}, "nodemonConfig": {"ignore": ["uploads/*", "public/uploads/*", "logs/*", "*.log", "*.mp3", "*.wav", "*.m4a", "audio-manifest.json"], "ext": "js,json", "watch": ["public/src/**/*.js", "index.js", ".env"]}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "dependencies": {"axios": "^1.8.3", "bcrypt": "^5.1.1", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^16.4.7", "elevenlabs": "^1.59.0", "express": "^4.21.2", "express-validator": "^7.2.1", "fluent-ffmpeg": "^2.1.3", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.1", "mysql2": "^3.14.0", "openai": "^4.87.3", "sequelize": "^6.37.6", "string-similarity": "^4.0.4", "uuid": "^11.1.0", "winston": "^3.17.0"}, "devDependencies": {"sequelize-cli": "^6.6.2"}}