require('dotenv').config();
const fs = require('fs');
const path = require('path');
const { textToSpeech } = require('./public/src/services/ttsService');

// Function to clear the uploads directory of audio files
async function clearAudioCache() {
  try {
    console.log("🧹 Clearing audio cache...");
    
    // Path to the uploads directory
    const uploadsDir = path.join(__dirname, 'public/src/uploads');
    
    // Check if directory exists
    if (!fs.existsSync(uploadsDir)) {
      console.log("📁 Uploads directory doesn't exist. Creating it...");
      fs.mkdirSync(uploadsDir, { recursive: true });
      console.log("✅ Uploads directory created.");
      return;
    }
    
    // Read all files in the directory
    const files = fs.readdirSync(uploadsDir);
    
    // Filter for audio files
    const audioFiles = files.filter(file => 
      file.endsWith('.mp3') || 
      file.startsWith('speech_')
    );
    
    console.log(`🔍 Found ${audioFiles.length} audio files to remove.`);
    
    // Delete each audio file
    let deletedCount = 0;
    for (const file of audioFiles) {
      const filePath = path.join(uploadsDir, file);
      fs.unlinkSync(filePath);
      deletedCount++;
      console.log(`🗑️ Deleted: ${file}`);
    }
    
    console.log(`✅ Successfully cleared ${deletedCount} audio files from cache.`);
    
    // Generate a test audio file to verify the new voice
    console.log("🔊 Generating test audio with new voice settings...");
    const testText = "مرحبا، هذا اختبار للصوت الجديد. أنا صوت أنثوي عربي ودود.";
    const audioPath = await textToSpeech(testText);
    console.log(`✅ Test audio generated at: ${audioPath}`);
    console.log("🌟 Please test the chatbot in your front-end application now.");
    
  } catch (error) {
    console.error("❌ Error clearing audio cache:", error);
  }
}

// Run the function
clearAudioCache();
