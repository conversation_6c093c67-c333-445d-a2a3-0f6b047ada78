require('dotenv').config();
const { textToSpeech } = require('./public/src/services/ttsService');

async function testTTS() {
  try {
    // Test with Arabic text
    const arabicText = "مرحبا، كيف حالك اليوم؟ أنا هنا لمساعدتك.";
    console.log("Testing TTS with Arabic text:", arabicText);
    
    const audioPath = await textToSpeech(arabicText);
    console.log("Audio generated successfully at:", audioPath);
  } catch (error) {
    console.error("Error testing TTS:", error);
  }
}

testTTS();
