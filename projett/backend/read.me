🎯 Objectif du système
Recommander aux élèves les meilleurs enseignants à suivre dans une matière donnée, en se basant :

sur leurs interactions passées (likes, follows, vues),

sur les comportements d’élèves similaires,

et en cas d'absence de données, sur la popularité globale des enseignants.

🔁 Étapes du processus
1. 🧑‍🏫 Identification des professeurs actifs
Requête SQL pour extraire les enseignants qui :

ont publié un webinaire dans la matière et le niveau donnés,

ou ont des vidéos liées à un manuel de la matière concernée.

sql
Copier
Modifier
SELECT DISTINCT u.id AS teacher_id, u.full_name
FROM users u
LEFT JOIN webinars w ON w.teacher_id = u.id 
LEFT JOIN videos v ON v.user_id = u.id
LEFT JOIN manuels m ON v.manuel_id = m.id
WHERE u.role_id = 4 AND (
    (w.matiere_id = {matiere_id} AND w.level_id = {level_id})
    OR (m.material_id = {matiere_id})
)
✅ Résultat : liste des teacher_ids actifs dans cette matière.

2. 💬 Collecte des interactions élèves ↔ enseignants actifs
On récupère uniquement les interactions dirigées vers ces enseignants actifs.

a. 👥 Follows
sql
Copier
Modifier
SELECT follower AS child_id, user_id AS teacher_id, 1 AS score
FROM follows
WHERE status = 'accepted' AND user_id IN (teacher_ids)
Score = 1

b. ❤️ Likes sur les vidéos
sql
Copier
Modifier
SELECT l.user_id AS child_id, v.user_id AS teacher_id, 2 AS score
FROM likes l
JOIN videos v ON l.video_id = v.id
WHERE v.user_id IN (teacher_ids)
Score = 2 (plus engageant qu’un follow)

c. 👁️ Vues sur les vidéos
sql
Copier
Modifier
SELECT uv.user_id AS child_id, v.user_id AS teacher_id, 3 AS score
FROM user_views uv
JOIN videos v ON v.id = uv.video_id
WHERE v.user_id IN (teacher_ids)
Score = 3 (regarder une vidéo = fort engagement)

3. 📊 Fusion des interactions
Les interactions sont fusionnées dans un seul DataFrame :

python
Copier
Modifier
full_df = pd.concat([follow_df, like_df, user_views_df])
Puis on ajoute une colonne value = score (utile pour la matrice finale) :

python
Copier
Modifier
full_df["value"] = full_df["score"]
4. 📐 Construction de la matrice Enfant × Enseignant
On transforme les données en une matrice d’interactions avec :

python
Copier
Modifier
matrix = full_df.pivot_table(
    index="child_id",
    columns="teacher_id",
    values="value",
    aggfunc="sum",
    fill_value=0
)
Lignes = enfants

Colonnes = enseignants actifs

Valeurs = somme pondérée des interactions (1, 2, ou 3)

5. 🤝 Calcul de similarité entre enfants
On cherche les enfants les plus similaires à l’enfant cible (child_id) en comparant leurs vecteurs ligne dans la matrice :

python
Copier
Modifier
similarities = {
    cid: pearson_correlation(target_vec, matrix.loc[cid])
    for cid in matrix.index if cid != child_id
}
On garde les 10 enfants les plus similaires, avec la fonction Pearson :

python
Copier
Modifier
sim(a, b) = covariance(a, b) / (std(a) * std(b))
6. 🧮 Score collaboratif des enseignants
On agrège les notes des enfants similaires, pondérées par leur similarité :

python
Copier
Modifier
weighted_scores[tid] += similarité * note
7. 🆘 Fallback : Popularité globale
Si aucun score collaboratif n'est utile (tous = 0), on utilise :

python
Copier
Modifier
score_total = nb_follows * 2 + nb_likes * 1.5 + nb_views * 1
Basé sur :

Nombre de followers (follows)

Nombre de likes sur les vidéos (likes)

Nombre de vues sur les vidéos (user_views)

8. ✅ Résultat final
On retourne la liste des enseignants classés par score décroissant :

python
Copier
Modifier
result = final.sort_values(by="total_score", ascending=False).head(top_n)
Et le JSON envoyé à l’application mobile :

json
Copier
Modifier
[
  {
    "teacher_id": 3485,
    "full_name": "Hatem Slama",
    "total_score": 10.0
  },
  ...
]
📌 Pourquoi ce système est pertinent ?
Critère	Justification
Engagement utilisateur	Prend en compte likes, follows, vues = comportements concrets
Personnalisation	Compare les enfants entre eux pour affiner les préférences
Fallback intelligent	Basé sur la popularité des profs si pas assez de données personnelles
Filtrage contextuel	N’inclut que les professeurs actifs dans la matière & niveau visés

