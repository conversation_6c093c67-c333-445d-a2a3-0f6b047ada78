#!/bin/bash

echo "🔄 Restarting the server..."

# Find and kill any running Node.js processes for this application
echo "🛑 Stopping any running Node.js processes..."
pkill -f "node server.js" || echo "No server process found."

# Wait a moment for processes to terminate
sleep 2

# Start the server again
echo "🚀 Starting the server..."
node server.js &

echo "✅ Server restart initiated. The server should now be running with the new voice settings."
echo "🌟 Please test the chatbot in your front-end application now."
