#!/usr/bin/env node

/**
 * Script de diagnostic pour analyser pourquoi le cours "الضرب في رقمين" n'est pas trouvé
 */

const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '.env') });

const db = require('./public/src/models');

async function debugCourseSearch() {
    console.log('🔍 DIAGNOSTIC DE RECHERCHE DE COURS');
    console.log('='.repeat(60));
    console.log('🎯 Recherche du cours: "الضرب في رقمين"');
    console.log('');
    
    try {
        // 1. Vérifier les matières disponibles
        console.log('📚 ÉTAPE 1: Vérification des matières');
        console.log('-'.repeat(40));
        
        const allMaterials = await db.Material.findAll({
            attributes: ['id', 'name'],
            limit: 20
        });
        
        console.log(`✅ ${allMaterials.length} matières trouvées:`);
        allMaterials.forEach(material => {
            console.log(`  ${material.id}: "${material.name}"`);
        });
        
        // 2. Vérifier le mapping niveau-matière
        console.log('\n🗺️ ÉTAPE 2: Vérification du mapping niveau-matière');
        console.log('-'.repeat(40));
        
        const manualLevelMapping = {
            6: [1, 1, 2],        // 1ère année
            7: [3, 4, 4],        // 2ème année
            8: [5, 5, 6, 7, 7, 8],   // 3ème année
            9: [9, 9, 10, 11, 11, 12, 12],   // 4ème année
            10: [13, 14, 14, 15, 16, 16, 17, 17],   // 5ème année
            11: [18, 18, 19, 20, 21, 21, 22, 22, 23]   // 6ème année
        };
        
        console.log('📋 Mapping configuré:');
        Object.keys(manualLevelMapping).forEach(levelId => {
            console.log(`  Niveau ${levelId}: Matières [${manualLevelMapping[levelId].join(', ')}]`);
        });
        
        // 3. Tester la recherche de matière "Mathématiques" pour niveau 6
        console.log('\n🔍 ÉTAPE 3: Test de recherche matière "Mathématiques" niveau 6');
        console.log('-'.repeat(40));
        
        const matiereIds = manualLevelMapping[6] || [];
        console.log(`🎯 IDs de matières pour niveau 6: [${matiereIds.join(', ')}]`);
        
        const mathMaterials = await db.Material.findAll({
            where: {
                name: 'Mathématiques',
                id: { [db.Sequelize.Op.in]: matiereIds }
            }
        });
        
        console.log(`📊 Matières "Mathématiques" trouvées: ${mathMaterials.length}`);
        mathMaterials.forEach(material => {
            console.log(`  ✅ ID: ${material.id}, Nom: "${material.name}"`);
        });
        
        if (mathMaterials.length === 0) {
            console.log('❌ PROBLÈME: Aucune matière "Mathématiques" trouvée avec les IDs [1, 1, 2]');
            console.log('💡 Vérification des matières avec ces IDs:');
            
            for (const id of [1, 2]) {
                const material = await db.Material.findByPk(id);
                if (material) {
                    console.log(`  ID ${id}: "${material.name}"`);
                } else {
                    console.log(`  ID ${id}: ❌ N'existe pas`);
                }
            }
        }
        
        // 4. Vérifier tous les cours (webinars) disponibles
        console.log('\n📖 ÉTAPE 4: Vérification des cours disponibles');
        console.log('-'.repeat(40));
        
        const allWebinars = await db.Webinar.findAll({
            attributes: ['id', 'level_id', 'matiere_id', 'status', 'deleted_at'],
            include: [
                {
                    model: db.WebinarTranslation,
                    attributes: ['title', 'locale', 'description']
                }
            ],
            limit: 20
        });
        
        console.log(`✅ ${allWebinars.length} cours trouvés:`);
        allWebinars.forEach(webinar => {
            const titleAr = webinar.WebinarTranslations?.find(t => t.locale === 'ar')?.title || '';
            const titleFr = webinar.WebinarTranslations?.find(t => t.locale === 'fr')?.title || '';
            const title = titleAr || titleFr || 'Sans titre';
            
            console.log(`  📚 ID: ${webinar.id}`);
            console.log(`     Titre: "${title}"`);
            console.log(`     Niveau: ${webinar.level_id}, Matière: ${webinar.matiere_id}`);
            console.log(`     Status: ${webinar.status}, Supprimé: ${webinar.deleted_at ? 'Oui' : 'Non'}`);
            console.log('');
        });
        
        // 5. Rechercher spécifiquement le cours "الضرب في رقمين"
        console.log('\n🎯 ÉTAPE 5: Recherche spécifique du cours "الضرب في رقمين"');
        console.log('-'.repeat(40));
        
        const targetCourse = await db.Webinar.findAll({
            include: [
                {
                    model: db.WebinarTranslation,
                    where: {
                        title: {
                            [db.Sequelize.Op.like]: '%الضرب في رقمين%'
                        }
                    },
                    attributes: ['title', 'locale', 'description']
                }
            ],
            attributes: ['id', 'level_id', 'matiere_id', 'status', 'deleted_at']
        });
        
        if (targetCourse.length > 0) {
            console.log(`✅ Cours trouvé ! ${targetCourse.length} résultat(s):`);
            targetCourse.forEach(course => {
                console.log(`  📚 ID: ${course.id}`);
                console.log(`     Niveau: ${course.level_id}, Matière: ${course.matiere_id}`);
                console.log(`     Status: ${course.status}, Supprimé: ${course.deleted_at ? 'Oui' : 'Non'}`);
                course.WebinarTranslations.forEach(translation => {
                    console.log(`     Titre (${translation.locale}): "${translation.title}"`);
                });
            });
        } else {
            console.log('❌ Aucun cours trouvé avec le titre "الضرب في رقمين"');
            
            // Recherche plus large
            console.log('\n🔍 Recherche élargie avec "ضرب":');
            const broadSearch = await db.Webinar.findAll({
                include: [
                    {
                        model: db.WebinarTranslation,
                        where: {
                            title: {
                                [db.Sequelize.Op.like]: '%ضرب%'
                            }
                        },
                        attributes: ['title', 'locale', 'description']
                    }
                ],
                attributes: ['id', 'level_id', 'matiere_id', 'status', 'deleted_at']
            });
            
            if (broadSearch.length > 0) {
                console.log(`✅ ${broadSearch.length} cours trouvé(s) contenant "ضرب":`);
                broadSearch.forEach(course => {
                    console.log(`  📚 ID: ${course.id}`);
                    console.log(`     Niveau: ${course.level_id}, Matière: ${course.matiere_id}`);
                    course.WebinarTranslations.forEach(translation => {
                        console.log(`     Titre (${translation.locale}): "${translation.title}"`);
                    });
                });
            } else {
                console.log('❌ Aucun cours trouvé contenant "ضرب"');
            }
        }
        
        // 6. Simuler la requête exacte du système
        console.log('\n🧪 ÉTAPE 6: Simulation de la requête système');
        console.log('-'.repeat(40));
        
        if (mathMaterials.length > 0) {
            const matchingMatiereIds = mathMaterials.map(m => m.id);
            console.log(`🎯 Test avec matière IDs: [${matchingMatiereIds.join(', ')}]`);
            
            const systemQuery = await db.Webinar.findAll({
                where: {
                    matiere_id: { [db.Sequelize.Op.in]: matchingMatiereIds },
                    level_id: 6,
                    status: 'active',
                    deleted_at: null
                },
                include: [
                    {
                        model: db.User,
                        as: 'teacher',
                        attributes: ['id', 'full_name', 'avatar']
                    },
                    {
                        model: db.WebinarTranslation,
                        attributes: ['title', 'locale', 'description']
                    }
                ]
            });
            
            console.log(`📊 Résultat de la requête système: ${systemQuery.length} cours`);
            systemQuery.forEach(course => {
                const titleAr = course.WebinarTranslations?.find(t => t.locale === 'ar')?.title || '';
                const titleFr = course.WebinarTranslations?.find(t => t.locale === 'fr')?.title || '';
                const title = titleAr || titleFr || 'Sans titre';
                console.log(`  📚 "${title}" (ID: ${course.id})`);
            });
        }
        
        console.log('\n🎉 DIAGNOSTIC TERMINÉ');
        
    } catch (error) {
        console.error('❌ Erreur lors du diagnostic:', error);
        console.error(error.stack);
    } finally {
        await db.sequelize.close();
        console.log('\n🔌 Connexion fermée');
    }
}

// Exécuter si appelé directement
if (require.main === module) {
    debugCourseSearch().catch(console.error);
}

module.exports = { debugCourseSearch };
