require('dotenv').config();
const { ElevenLabsClient } = require("elevenlabs");
const fs = require("fs");
const path = require("path");

const ELEVENLABS_API_KEY = process.env.ELEVENLABS_API_KEY;
// Third option - Bella voice (good for multilingual)
const VOICE_ID = "EXAVITQu4vr4xnSDxMaL";

const client = new ElevenLabsClient({ 
    apiKey: ELEVENLABS_API_KEY,
    timeoutMs: 30000 
});

// Convert ReadableStream to Buffer
async function streamToBuffer(stream) {
    const reader = stream.getReader();
    const chunks = [];
    
    try {
        while (true) {
            const { done, value } = await reader.read();
            if (done) break;
            chunks.push(value);
        }
        
        return Buffer.concat(chunks);
    } catch (error) {
        console.error("Error in streamToBuffer:", error);
        throw error;
    }
}

async function testOption3Voice() {
    try {
        // Arabic text
        const arabicText = "مرحبا، كيف حالك اليوم؟ أنا هنا لمساعدتك.";
        console.log("Testing option 3 voice with Arabic text:", arabicText);
        
        // Using different model and settings
        const audioStream = await client.textToSpeech.convert(VOICE_ID, {
            text: arabicText,
            model_id: "eleven_multilingual_v2", // Try the multilingual model
            output_format: "mp3_44100_128", // Higher quality
            voice_settings: {
                stability: 0.7,         // Higher stability
                similarity_boost: 0.5,  // Medium similarity
                style: 0.5,             // Medium style
                use_speaker_boost: true,
                speaking_rate: 0.9      // Slightly slower for clarity
            },
            optimize_streaming_latency: 1, // Less aggressive optimization
        });

        // Convert to Buffer
        const audioBuffer = await streamToBuffer(audioStream);

        // Create uploads directory if needed
        const uploadDir = path.join(__dirname, "public/src/uploads/");
        if (!fs.existsSync(uploadDir)) {
            fs.mkdirSync(uploadDir, { recursive: true });
        }

        // Generate unique filename
        const uniqueName = `speech_opt3_${Date.now()}_${Math.floor(Math.random() * 1000)}.mp3`;
        const filePath = path.join(uploadDir, uniqueName);

        // Save file
        fs.writeFileSync(filePath, audioBuffer);

        console.log("Option 3 voice audio generated at:", filePath);
        console.log("Web path:", `/uploads/${uniqueName}`);
    } catch (error) {
        console.error("Error testing option 3 voice:", error);
    }
}

testOption3Voice();
