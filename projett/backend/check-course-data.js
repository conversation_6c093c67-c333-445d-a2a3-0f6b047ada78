#!/usr/bin/env node

/**
 * Script simple pour vérifier les données de cours
 */

const mysql = require('mysql2/promise');
require('dotenv').config();

async function checkCourseData() {
    console.log('🔍 VÉRIFICATION DES DONNÉES DE COURS');
    console.log('='.repeat(50));
    
    let connection;
    
    try {
        // Connexion à la base de données
        connection = await mysql.createConnection({
            host: process.env.DB_HOST || 'localhost',
            user: process.env.DB_USER || 'root',
            password: process.env.DB_PASSWORD || '',
            database: process.env.DB_NAME || 'abajimdb'
        });
        
        console.log('✅ Connexion à la base de données établie');
        
        // 1. Vérifier les matières
        console.log('\n📚 MATIÈRES DISPONIBLES:');
        const [materials] = await connection.execute('SELECT id, name FROM materials LIMIT 20');
        console.log(`✅ ${materials.length} matières trouvées:`);
        materials.forEach(material => {
            console.log(`  ${material.id}: "${material.name}"`);
        });
        
        // 2. Vérifier les cours avec traductions
        console.log('\n📖 COURS AVEC TRADUCTIONS:');
        const [courses] = await connection.execute(`
            SELECT 
                w.id, 
                w.level_id, 
                w.matiere_id, 
                w.status,
                w.deleted_at,
                wt.title,
                wt.locale
            FROM webinars w
            LEFT JOIN webinar_translations wt ON w.id = wt.webinar_id
            WHERE w.deleted_at IS NULL
            ORDER BY w.id, wt.locale
            LIMIT 30
        `);
        
        console.log(`✅ ${courses.length} enregistrements cours+traductions trouvés:`);
        let currentCourseId = null;
        courses.forEach(course => {
            if (course.id !== currentCourseId) {
                console.log(`\n  📚 Cours ID: ${course.id}`);
                console.log(`     Niveau: ${course.level_id}, Matière: ${course.matiere_id}`);
                console.log(`     Status: ${course.status}`);
                currentCourseId = course.id;
            }
            if (course.title) {
                console.log(`     Titre (${course.locale}): "${course.title}"`);
            }
        });
        
        // 3. Rechercher spécifiquement "الضرب في رقمين"
        console.log('\n🎯 RECHERCHE SPÉCIFIQUE: "الضرب في رقمين"');
        const [targetCourses] = await connection.execute(`
            SELECT 
                w.id, 
                w.level_id, 
                w.matiere_id, 
                w.status,
                w.deleted_at,
                wt.title,
                wt.locale
            FROM webinars w
            LEFT JOIN webinar_translations wt ON w.id = wt.webinar_id
            WHERE wt.title LIKE '%الضرب في رقمين%'
            OR wt.title LIKE '%ضرب%'
        `);
        
        if (targetCourses.length > 0) {
            console.log(`✅ ${targetCourses.length} cours trouvé(s):`);
            targetCourses.forEach(course => {
                console.log(`  📚 ID: ${course.id}`);
                console.log(`     Niveau: ${course.level_id}, Matière: ${course.matiere_id}`);
                console.log(`     Status: ${course.status}, Supprimé: ${course.deleted_at ? 'Oui' : 'Non'}`);
                console.log(`     Titre (${course.locale}): "${course.title}"`);
                console.log('');
            });
        } else {
            console.log('❌ Aucun cours trouvé avec "الضرب في رقمين" ou "ضرب"');
        }
        
        // 4. Vérifier le mapping niveau-matière
        console.log('\n🗺️ ANALYSE DU MAPPING NIVEAU-MATIÈRE:');
        const [levelMatiereStats] = await connection.execute(`
            SELECT level_id, matiere_id, COUNT(*) as count
            FROM webinars 
            WHERE status = 'active' AND deleted_at IS NULL
            GROUP BY level_id, matiere_id
            ORDER BY level_id, matiere_id
        `);
        
        console.log('📊 Répartition actuelle:');
        levelMatiereStats.forEach(stat => {
            console.log(`  Niveau ${stat.level_id} + Matière ${stat.matiere_id}: ${stat.count} cours`);
        });
        
        // 5. Vérifier les matières "Mathématiques"
        console.log('\n🔍 MATIÈRES "MATHÉMATIQUES":');
        const [mathMaterials] = await connection.execute(`
            SELECT id, name 
            FROM materials 
            WHERE name LIKE '%math%' OR name LIKE '%Math%' OR name LIKE '%رياض%'
        `);
        
        if (mathMaterials.length > 0) {
            console.log(`✅ ${mathMaterials.length} matière(s) mathématiques trouvée(s):`);
            mathMaterials.forEach(material => {
                console.log(`  ${material.id}: "${material.name}"`);
            });
            
            // Vérifier les cours pour ces matières
            const mathIds = mathMaterials.map(m => m.id);
            const [mathCourses] = await connection.execute(`
                SELECT 
                    w.id, 
                    w.level_id, 
                    w.matiere_id, 
                    w.status,
                    wt.title,
                    wt.locale
                FROM webinars w
                LEFT JOIN webinar_translations wt ON w.id = wt.webinar_id
                WHERE w.matiere_id IN (${mathIds.join(',')}) 
                AND w.status = 'active' 
                AND w.deleted_at IS NULL
                ORDER BY w.level_id, w.id
            `);
            
            console.log(`\n📚 ${mathCourses.length} cours de mathématiques actifs:`);
            let currentId = null;
            mathCourses.forEach(course => {
                if (course.id !== currentId) {
                    console.log(`\n  📚 Cours ID: ${course.id} (Niveau: ${course.level_id}, Matière: ${course.matiere_id})`);
                    currentId = course.id;
                }
                if (course.title) {
                    console.log(`     "${course.title}" (${course.locale})`);
                }
            });
        } else {
            console.log('❌ Aucune matière mathématiques trouvée');
        }
        
        // 6. Recommandations
        console.log('\n💡 RECOMMANDATIONS:');
        console.log('-'.repeat(30));
        
        if (targetCourses.length === 0) {
            console.log('❌ Le cours "الضرب في رقمين" n\'existe pas dans la base');
            console.log('💡 Solutions possibles:');
            console.log('   1. Vérifier l\'orthographe exacte du titre');
            console.log('   2. Créer le cours s\'il n\'existe pas');
            console.log('   3. Vérifier les traductions dans webinar_translations');
        } else {
            const course = targetCourses[0];
            console.log(`✅ Le cours existe (ID: ${course.id})`);
            console.log(`📊 Niveau: ${course.level_id}, Matière: ${course.matiere_id}`);
            
            // Vérifier si la matière est dans le mapping
            const manualLevelMapping = {
                6: [1, 1, 2],
                7: [3, 4, 4],
                8: [5, 5, 6, 7, 7, 8],
                9: [9, 9, 10, 11, 11, 12, 12],
                10: [13, 14, 14, 15, 16, 16, 17, 17],
                11: [18, 18, 19, 20, 21, 21, 22, 22, 23]
            };
            
            const expectedMatiereIds = manualLevelMapping[course.level_id] || [];
            if (expectedMatiereIds.includes(course.matiere_id)) {
                console.log('✅ La matière est dans le mapping pour ce niveau');
            } else {
                console.log(`❌ La matière ${course.matiere_id} n'est PAS dans le mapping pour le niveau ${course.level_id}`);
                console.log(`💡 Mapping actuel pour niveau ${course.level_id}: [${expectedMatiereIds.join(', ')}]`);
                console.log('💡 Solution: Ajouter la matière au mapping ou corriger le niveau du cours');
            }
        }
        
    } catch (error) {
        console.error('❌ Erreur:', error.message);
    } finally {
        if (connection) {
            await connection.end();
            console.log('\n🔌 Connexion fermée');
        }
    }
}

// Exécuter si appelé directement
if (require.main === module) {
    checkCourseData().catch(console.error);
}

module.exports = { checkCourseData };
