{"cells": [{"cell_type": "markdown", "metadata": {"id": "23e5c72f-bc39-4361-a5a0-92235529daaa"}, "source": ["# <a id='toc1_'></a>[Netflix-like recommendation system with Sklearn](#toc0_)\n"]}, {"cell_type": "markdown", "metadata": {"id": "732fb4ff-ba48-4569-afce-eabee1807a51"}, "source": ["In this guided project, we explore an easy way of building recommendation systems. We begin by looking at popular-based recommendations and then ease into a simple application of how content-based and collaborative filtering recommendation works.\n"]}, {"cell_type": "markdown", "metadata": {"id": "10aaff5a-0dc3-4c01-85a8-c87942c3455a"}, "source": ["**Table of contents**<a id='toc0_'></a>    \n", "\n", "  - [Background on recommendation systems](#toc1_1_)    \n", "    - [Types of recommendation systems](#toc1_1_1_)    \n", "  - [Objectives](#toc1_2_)    \n", "  - [Setup](#toc1_3_)    \n", "    - [Installing required libraries](#toc1_3_1_)    \n", "    - [Importing required libraries](#toc1_3_2_)    \n", "  - [Exploratory data analysis (EDA)](#toc1_4_)    \n", "  - [Popularity-based recommendation](#toc1_5_)    \n", "    - [Exercise 1 - Get the top 5 suggestions sorting by score in descending order](#toc1_5_1_)    \n", "  - [Content-based recommendation](#toc1_6_)    \n", "    - [Exercise 2 - Check the recommendations for the movie 'Toy Story 2 (1999)'](#toc1_6_1_)    \n", "  - [Collaborative filtering](#toc1_7_)    \n", "    - [Exercise 3 - Check the recommendations for the movie 'Jurassic Park (1993)'](#toc1_7_1_)   \n"]}, {"cell_type": "markdown", "metadata": {"id": "bd9ad428-c595-4907-a960-05e2dea4c577"}, "source": ["## <a id='toc1_1_'></a>[Background on recommendation systems](#toc0_)\n", "\n", "Recommendation systems have become an integral part of our digital lives, subtly shaping the content we consume and the products we buy. From suggesting movies on Netflix to recommending products on Amazon, these systems help users navigate vast amounts of information by providing personalized suggestions based on their preferences and behaviors.\n", "\n", "### <a id='toc1_1_1_'></a>[Types of recommendation systems](#toc0_)\n", "\n", "There are several types of recommendation systems, each with its unique approach to generating recommendations:\n", "\n", "1. **Popularity-based recommendation**: Popular-based recommendation systems are straightforward to implement because they don’t require complex algorithms or user-specific data. They often rely on basic statistics like item frequency and offer the same suggestions to all users, focusing on what is popular among the majority.\n", "\n", "2. **Content-based filtering**: This approach focuses on the characteristics of the items themselves. It recommends items that are similar to those the user has shown interest in, based on item features.\n", "\n", "3. **Collaborative filtering**: This method relies on the collective preferences of users. It can be user-based, where recommendations are made based on the preferences of similar users, or item-based, where recommendations are made based on items that are similar to what the user has liked in the past.\n"]}, {"cell_type": "markdown", "metadata": {"id": "fbe09aee-fbd3-442c-8fb6-af2c6a84d362"}, "source": ["## <a id='toc1_2_'></a>[Objectives](#toc0_)\n", "\n", "\n", "\n", "After completing this lab, you are able to:\n", "\n", "\n", "\n", "- Understand the basic concepts and types of recommendation systems.\n", "\n", "- Implement a simple popularity-based recommendation system.\n", "\n", "- Implement a content-based recommendation system.\n", "\n", "- Implement a item-based recommendation system.\n", "\n"]}, {"cell_type": "markdown", "metadata": {"id": "40aea37d-ab5e-4bdd-9121-fe66d5560dbd"}, "source": ["----\n"]}, {"cell_type": "markdown", "metadata": {"id": "bcbe7958-35cb-4a3b-ae8b-36f77c9b458f"}, "source": ["## <a id='toc1_3_'></a>[Setup](#toc0_)\n", "\n", "For this lab, you use the following libraries:\n", "\n", "*   [`pandas`](https://pandas.pydata.org/?utm_medium=Exinfluencer&utm_source=Exinfluencer&utm_content=000026UJ&utm_term=10006555&utm_id=NA-SkillsNetwork-Channel-SkillsNetworkCoursesIBMML0187ENSkillsNetwork31430127-2021-01-01) for managing the data.\n", "*   [`sklearn`](https://scikit-learn.org/stable/?utm_medium=Exinfluencer&utm_source=Exinfluencer&utm_content=000026UJ&utm_term=10006555&utm_id=NA-SkillsNetwork-Channel-SkillsNetworkCoursesIBMML0187ENSkillsNetwork31430127-2021-01-01) for machine learning and machine-learning-pipeline related functions.\n"]}, {"cell_type": "markdown", "metadata": {"id": "888344ae-f370-4fb4-aed2-b5c88ef4e5f3"}, "source": ["### <a id='toc1_3_1_'></a>[Installing required libraries](#toc0_)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "e53a3e50-2f59-4ad5-9a8e-66975bd18d8a", "outputId": "a4f9d897-3dbd-47b9-8533-58c7822faa73"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Requirement already satisfied: tqdm==4.66.4 in /usr/local/lib/python3.11/dist-packages (4.66.4)\n", "\u001b[31mERROR: pip's dependency resolver does not currently take into account all the packages that are installed. This behaviour is the source of the following dependency conflicts.\n", "google-colab 1.0.0 requires pandas==2.2.2, but you have pandas 2.1.4 which is incompatible.\n", "plotnine 0.14.5 requires pandas>=2.2.0, but you have pandas 2.1.4 which is incompatible.\n", "mizani 0.13.2 requires pandas>=2.2.0, but you have pandas 2.1.4 which is incompatible.\u001b[0m\u001b[31m\n", "\u001b[0mSuccessfully installed pandas-2.1.4\n", "Requirement already satisfied: threadpoolctl>=3.1.0 in /usr/local/lib/python3.11/dist-packages (from scikit-learn==1.5.1) (3.6.0)\n"]}], "source": ["%pip install tqdm==4.66.4  | tail -n 1\n", "%pip install pandas==2.1.4  | tail -n 1\n", "%pip install scikit-learn==1.5.1  | tail -n 1"]}, {"cell_type": "markdown", "metadata": {"id": "6164848a-d426-4516-bb78-d047b3a37a4c"}, "source": ["### <a id='toc1_3_2_'></a>[Importing required libraries](#toc0_)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "a935b2ec-8789-483d-8173-bc2dcc9e386b"}, "outputs": [], "source": ["import pandas as pd\n", "from sklearn.feature_extraction.text import TfidfVectorizer\n", "from sklearn.metrics.pairwise import cosine_similarity\n", "from sklearn.neighbors import NearestNeighbors\n", "import statistics\n", "\n", "\n", "# You can also use this section to suppress warnings generated by your code:\n", "def warn(*args, **kwargs):\n", "    pass\n", "\n", "import warnings\n", "\n", "warnings.warn = warn\n", "warnings.filterwarnings('ignore')\n"]}, {"cell_type": "markdown", "metadata": {"id": "9666a3dd-83f1-4807-a66e-fb67744e341f"}, "source": ["The dataset is taken from [Ka<PERSON>](https://www.kaggle.com/datasets/shubhammehta21/movie-lens-small-latest-dataset/data).\n", "This dataset describes 5-star rating and free-text tagging activity from MovieLens, a movie recommendation service. Users were selected at random for inclusion. No demographic information is included. Each user is represented by an ID, and no other information is provided.\n", "\n", "The data are contained in the files movies.csv, ratings.csv and tags.csv.\n", "\n", "In the `movies.csv` file:\n", "- `movieId`: ID of the movie/show (unique)\n", "- `title`: Title of the movie/show\n", "- `genres`: Genre of the show\n", "  \n", "In the `ratings.csv` file:\n", "- `userId`: ID of the user who gave a rating\n", "- `movieId`: ID of the movie/show rated\n", "- `rating`: Rating given to the show\n", "- `timestamp`: Time when the rating was specified\n", "  \n", "In the `tags.csv` file:\n", "- `userId`: ID of the user who gave a rating\n", "- `movieId`: ID of the movie/show rated\n", "- `tag`: Tags given to the show\n", "- `timestamp`: Time when the rating was specified\n", "\n", "Now, let's load these datasets into a pandas DataFrame.\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "3702ffb3-328f-4b9a-a0f3-2192624b29ab"}, "outputs": [], "source": ["movie_df = pd.read_csv('https://cf-courses-data.s3.us.cloud-object-storage.appdomain.cloud/BxZuF3FrO7Bdw6McwsBaBw/movies.csv')\n", "rating_df = pd.read_csv('https://cf-courses-data.s3.us.cloud-object-storage.appdomain.cloud/R-bYYyyf7s3IUE5rsssmMw/ratings.csv')\n", "tag_df = pd.read_csv('https://cf-courses-data.s3.us.cloud-object-storage.appdomain.cloud/UZKHhXSl7Ft7t9mfUFZJPQ/tags.csv')"]}, {"cell_type": "markdown", "metadata": {"id": "c5f622ad-d5ac-433f-8b7c-ed1df6832a68"}, "source": ["Let's look at some samples rows from the dataset we loaded:\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 206}, "id": "1b5fc2fa-33c5-4fa3-b7b5-785bd59d7bab", "outputId": "61e3f760-c1c5-460d-caed-324c85cc75a2"}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["      movieId                                      title          genres\n", "7434    80864  You Will Meet a Tall Dark Stranger (2010)  Comedy|Romance\n", "248       287                  Nina Takes a Lover (1994)  Comedy|Romance\n", "4882     7315                   Against the Ropes (2004)    Comedy|Drama\n", "87         99       <PERSON>: Hollywood Madam (1995)     Documentary\n", "3477     4744                    Jeepers Creepers (2001)          Horror"], "text/html": ["\n", "  <div id=\"df-dd2ace26-efb3-4dc5-b963-0340496bd645\" class=\"colab-df-container\">\n", "    <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>movieId</th>\n", "      <th>title</th>\n", "      <th>genres</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>7434</th>\n", "      <td>80864</td>\n", "      <td>You Will Meet a Tall Dark Stranger (2010)</td>\n", "      <td>Comedy|Romance</td>\n", "    </tr>\n", "    <tr>\n", "      <th>248</th>\n", "      <td>287</td>\n", "      <td><PERSON> Takes a Lover (1994)</td>\n", "      <td>Comedy|Romance</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4882</th>\n", "      <td>7315</td>\n", "      <td>Against the Ropes (2004)</td>\n", "      <td>Comedy|Drama</td>\n", "    </tr>\n", "    <tr>\n", "      <th>87</th>\n", "      <td>99</td>\n", "      <td><PERSON>: <PERSON> Madam (1995)</td>\n", "      <td>Documentary</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3477</th>\n", "      <td>4744</td>\n", "      <td><PERSON><PERSON> Creepers (2001)</td>\n", "      <td>Horror</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>\n", "    <div class=\"colab-df-buttons\">\n", "\n", "  <div class=\"colab-df-container\">\n", "    <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-dd2ace26-efb3-4dc5-b963-0340496bd645')\"\n", "            title=\"Convert this dataframe to an interactive table.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\" viewBox=\"0 -960 960 960\">\n", "    <path d=\"M120-120v-720h720v720H120Zm60-500h600v-160H180v160Zm220 220h160v-160H400v160Zm0 220h160v-160H400v160ZM180-400h160v-160H180v160Zm440 0h160v-160H620v160ZM180-180h160v-160H180v160Zm440 0h160v-160H620v160Z\"/>\n", "  </svg>\n", "    </button>\n", "\n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    .colab-df-buttons div {\n", "      margin-bottom: 4px;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "    <script>\n", "      const buttonEl =\n", "        document.querySelector('#df-dd2ace26-efb3-4dc5-b963-0340496bd645 button.colab-df-convert');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      async function convertToInteractive(key) {\n", "        const element = document.querySelector('#df-dd2ace26-efb3-4dc5-b963-0340496bd645');\n", "        const dataTable =\n", "          await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                    [key], {});\n", "        if (!dataTable) return;\n", "\n", "        const docLinkHtml = 'Like what you see? Visit the ' +\n", "          '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "          + ' to learn more about interactive tables.';\n", "        element.innerHTML = '';\n", "        dataTable['output_type'] = 'display_data';\n", "        await google.colab.output.renderOutput(dataTable, element);\n", "        const docLink = document.createElement('div');\n", "        docLink.innerHTML = docLinkHtml;\n", "        element.appendChild(docLink);\n", "      }\n", "    </script>\n", "  </div>\n", "\n", "\n", "<div id=\"df-e3fdcd09-d3d5-4404-aa7e-49169c9262f6\">\n", "  <button class=\"colab-df-quickchart\" onclick=\"quickchart('df-e3fdcd09-d3d5-4404-aa7e-49169c9262f6')\"\n", "            title=\"Suggest charts\"\n", "            style=\"display:none;\">\n", "\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "     width=\"24px\">\n", "    <g>\n", "        <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n", "    </g>\n", "</svg>\n", "  </button>\n", "\n", "<style>\n", "  .colab-df-quickchart {\n", "      --bg-color: #E8F0FE;\n", "      --fill-color: #1967D2;\n", "      --hover-bg-color: #E2EBFA;\n", "      --hover-fill-color: #174EA6;\n", "      --disabled-fill-color: #AAA;\n", "      --disabled-bg-color: #DDD;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart {\n", "      --bg-color: #3B4455;\n", "      --fill-color: #D2E3FC;\n", "      --hover-bg-color: #434B5C;\n", "      --hover-fill-color: #FFFFFF;\n", "      --disabled-bg-color: #3B4455;\n", "      --disabled-fill-color: #666;\n", "  }\n", "\n", "  .colab-df-quickchart {\n", "    background-color: var(--bg-color);\n", "    border: none;\n", "    border-radius: 50%;\n", "    cursor: pointer;\n", "    display: none;\n", "    fill: var(--fill-color);\n", "    height: 32px;\n", "    padding: 0;\n", "    width: 32px;\n", "  }\n", "\n", "  .colab-df-quickchart:hover {\n", "    background-color: var(--hover-bg-color);\n", "    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "    fill: var(--button-hover-fill-color);\n", "  }\n", "\n", "  .colab-df-quickchart-complete:disabled,\n", "  .colab-df-quickchart-complete:disabled:hover {\n", "    background-color: var(--disabled-bg-color);\n", "    fill: var(--disabled-fill-color);\n", "    box-shadow: none;\n", "  }\n", "\n", "  .colab-df-spinner {\n", "    border: 2px solid var(--fill-color);\n", "    border-color: transparent;\n", "    border-bottom-color: var(--fill-color);\n", "    animation:\n", "      spin 1s steps(1) infinite;\n", "  }\n", "\n", "  @keyframes spin {\n", "    0% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "      border-left-color: var(--fill-color);\n", "    }\n", "    20% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    30% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    40% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    60% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    80% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "    90% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "  }\n", "</style>\n", "\n", "  <script>\n", "    async function quickchart(key) {\n", "      const quickchartButtonEl =\n", "        document.querySelector('#' + key + ' button');\n", "      quickchartButtonEl.disabled = true;  // To prevent multiple clicks.\n", "      quickchartButtonEl.classList.add('colab-df-spinner');\n", "      try {\n", "        const charts = await google.colab.kernel.invokeFunction(\n", "            'suggest<PERSON><PERSON>s', [key], {});\n", "      } catch (error) {\n", "        console.error('Error during call to suggest<PERSON>harts:', error);\n", "      }\n", "      quickchartButtonEl.classList.remove('colab-df-spinner');\n", "      quickchartButtonEl.classList.add('colab-df-quickchart-complete');\n", "    }\n", "    (() => {\n", "      let quickchartButtonEl =\n", "        document.querySelector('#df-e3fdcd09-d3d5-4404-aa7e-49169c9262f6 button');\n", "      quickchartButtonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "    })();\n", "  </script>\n", "</div>\n", "\n", "    </div>\n", "  </div>\n"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "dataframe", "summary": "{\n  \"name\": \"movie_df\",\n  \"rows\": 5,\n  \"fields\": [\n    {\n      \"column\": \"movieId\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 34906,\n        \"min\": 99,\n        \"max\": 80864,\n        \"num_unique_values\": 5,\n        \"samples\": [\n          287,\n          4744,\n          7315\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"title\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 5,\n        \"samples\": [\n          \"Nina Takes a Lover (1994)\",\n          \"Jeepers Creepers (2001)\",\n          \"Against the Ropes (2004)\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"genres\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 4,\n        \"samples\": [\n          \"Comedy|Drama\",\n          \"Horror\",\n          \"Comedy|Romance\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    }\n  ]\n}"}}, "metadata": {}, "execution_count": 3}], "source": ["movie_df.sample(5)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 206}, "id": "39c7b060-fa1f-4ace-af16-82f86ae4f22d", "outputId": "61d97b8a-6570-45c4-9af6-87c47d445ae0"}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["      userId  movieId             tag   timestamp\n", "6          2   106782           drugs  1445715054\n", "185       62    49530         justice  1536874709\n", "56        62     2124    black comedy  1525636847\n", "2714     477    68358     time travel  1262795929\n", "2822     537    72998  graphic design  1424140242"], "text/html": ["\n", "  <div id=\"df-331c7dff-9e79-49f8-94b4-d4fa9ce11bc2\" class=\"colab-df-container\">\n", "    <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>userId</th>\n", "      <th>movieId</th>\n", "      <th>tag</th>\n", "      <th>timestamp</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>2</td>\n", "      <td>106782</td>\n", "      <td>drugs</td>\n", "      <td>1445715054</td>\n", "    </tr>\n", "    <tr>\n", "      <th>185</th>\n", "      <td>62</td>\n", "      <td>49530</td>\n", "      <td>justice</td>\n", "      <td>1536874709</td>\n", "    </tr>\n", "    <tr>\n", "      <th>56</th>\n", "      <td>62</td>\n", "      <td>2124</td>\n", "      <td>black comedy</td>\n", "      <td>1525636847</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2714</th>\n", "      <td>477</td>\n", "      <td>68358</td>\n", "      <td>time travel</td>\n", "      <td>1262795929</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2822</th>\n", "      <td>537</td>\n", "      <td>72998</td>\n", "      <td>graphic design</td>\n", "      <td>1424140242</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>\n", "    <div class=\"colab-df-buttons\">\n", "\n", "  <div class=\"colab-df-container\">\n", "    <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-331c7dff-9e79-49f8-94b4-d4fa9ce11bc2')\"\n", "            title=\"Convert this dataframe to an interactive table.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\" viewBox=\"0 -960 960 960\">\n", "    <path d=\"M120-120v-720h720v720H120Zm60-500h600v-160H180v160Zm220 220h160v-160H400v160Zm0 220h160v-160H400v160ZM180-400h160v-160H180v160Zm440 0h160v-160H620v160ZM180-180h160v-160H180v160Zm440 0h160v-160H620v160Z\"/>\n", "  </svg>\n", "    </button>\n", "\n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    .colab-df-buttons div {\n", "      margin-bottom: 4px;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "    <script>\n", "      const buttonEl =\n", "        document.querySelector('#df-331c7dff-9e79-49f8-94b4-d4fa9ce11bc2 button.colab-df-convert');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      async function convertToInteractive(key) {\n", "        const element = document.querySelector('#df-331c7dff-9e79-49f8-94b4-d4fa9ce11bc2');\n", "        const dataTable =\n", "          await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                    [key], {});\n", "        if (!dataTable) return;\n", "\n", "        const docLinkHtml = 'Like what you see? Visit the ' +\n", "          '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "          + ' to learn more about interactive tables.';\n", "        element.innerHTML = '';\n", "        dataTable['output_type'] = 'display_data';\n", "        await google.colab.output.renderOutput(dataTable, element);\n", "        const docLink = document.createElement('div');\n", "        docLink.innerHTML = docLinkHtml;\n", "        element.appendChild(docLink);\n", "      }\n", "    </script>\n", "  </div>\n", "\n", "\n", "<div id=\"df-dffb229e-0155-4256-8c55-01125032abd1\">\n", "  <button class=\"colab-df-quickchart\" onclick=\"quickchart('df-dffb229e-0155-4256-8c55-01125032abd1')\"\n", "            title=\"Suggest charts\"\n", "            style=\"display:none;\">\n", "\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "     width=\"24px\">\n", "    <g>\n", "        <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n", "    </g>\n", "</svg>\n", "  </button>\n", "\n", "<style>\n", "  .colab-df-quickchart {\n", "      --bg-color: #E8F0FE;\n", "      --fill-color: #1967D2;\n", "      --hover-bg-color: #E2EBFA;\n", "      --hover-fill-color: #174EA6;\n", "      --disabled-fill-color: #AAA;\n", "      --disabled-bg-color: #DDD;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart {\n", "      --bg-color: #3B4455;\n", "      --fill-color: #D2E3FC;\n", "      --hover-bg-color: #434B5C;\n", "      --hover-fill-color: #FFFFFF;\n", "      --disabled-bg-color: #3B4455;\n", "      --disabled-fill-color: #666;\n", "  }\n", "\n", "  .colab-df-quickchart {\n", "    background-color: var(--bg-color);\n", "    border: none;\n", "    border-radius: 50%;\n", "    cursor: pointer;\n", "    display: none;\n", "    fill: var(--fill-color);\n", "    height: 32px;\n", "    padding: 0;\n", "    width: 32px;\n", "  }\n", "\n", "  .colab-df-quickchart:hover {\n", "    background-color: var(--hover-bg-color);\n", "    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "    fill: var(--button-hover-fill-color);\n", "  }\n", "\n", "  .colab-df-quickchart-complete:disabled,\n", "  .colab-df-quickchart-complete:disabled:hover {\n", "    background-color: var(--disabled-bg-color);\n", "    fill: var(--disabled-fill-color);\n", "    box-shadow: none;\n", "  }\n", "\n", "  .colab-df-spinner {\n", "    border: 2px solid var(--fill-color);\n", "    border-color: transparent;\n", "    border-bottom-color: var(--fill-color);\n", "    animation:\n", "      spin 1s steps(1) infinite;\n", "  }\n", "\n", "  @keyframes spin {\n", "    0% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "      border-left-color: var(--fill-color);\n", "    }\n", "    20% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    30% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    40% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    60% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    80% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "    90% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "  }\n", "</style>\n", "\n", "  <script>\n", "    async function quickchart(key) {\n", "      const quickchartButtonEl =\n", "        document.querySelector('#' + key + ' button');\n", "      quickchartButtonEl.disabled = true;  // To prevent multiple clicks.\n", "      quickchartButtonEl.classList.add('colab-df-spinner');\n", "      try {\n", "        const charts = await google.colab.kernel.invokeFunction(\n", "            'suggest<PERSON><PERSON>s', [key], {});\n", "      } catch (error) {\n", "        console.error('Error during call to suggest<PERSON>harts:', error);\n", "      }\n", "      quickchartButtonEl.classList.remove('colab-df-spinner');\n", "      quickchartButtonEl.classList.add('colab-df-quickchart-complete');\n", "    }\n", "    (() => {\n", "      let quickchartButtonEl =\n", "        document.querySelector('#df-dffb229e-0155-4256-8c55-01125032abd1 button');\n", "      quickchartButtonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "    })();\n", "  </script>\n", "</div>\n", "\n", "    </div>\n", "  </div>\n"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "dataframe", "summary": "{\n  \"name\": \"tag_df\",\n  \"rows\": 5,\n  \"fields\": [\n    {\n      \"column\": \"userId\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 256,\n        \"min\": 2,\n        \"max\": 537,\n        \"num_unique_values\": 4,\n        \"samples\": [\n          62,\n          537,\n          2\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"movieId\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 38362,\n        \"min\": 2124,\n        \"max\": 106782,\n        \"num_unique_values\": 5,\n        \"samples\": [\n          49530,\n          72998,\n          2124\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"tag\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 5,\n        \"samples\": [\n          \"justice\",\n          \"graphic design\",\n          \"black comedy\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"timestamp\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 109999063,\n        \"min\": 1262795929,\n        \"max\": 1536874709,\n        \"num_unique_values\": 5,\n        \"samples\": [\n          1536874709,\n          1424140242,\n          1525636847\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    }\n  ]\n}"}}, "metadata": {}, "execution_count": 4}], "source": ["tag_df.sample(5)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 206}, "id": "f1f91d03-cab2-455c-bdec-0ec29cfe9976", "outputId": "d976b7eb-d1de-4ca4-e36b-4491aa224dc5"}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["       userId  movieId  rating   timestamp\n", "94456     599    31664     3.5  1519127793\n", "20638     136      168     4.0   832449475\n", "13597      89     1682     0.5  1520408338\n", "22695     156       52     3.5  1106855391\n", "53370     352    56171     2.5  1493674788"], "text/html": ["\n", "  <div id=\"df-79d1925d-94eb-45d4-a2dc-719eef36c152\" class=\"colab-df-container\">\n", "    <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>userId</th>\n", "      <th>movieId</th>\n", "      <th>rating</th>\n", "      <th>timestamp</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>94456</th>\n", "      <td>599</td>\n", "      <td>31664</td>\n", "      <td>3.5</td>\n", "      <td>1519127793</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20638</th>\n", "      <td>136</td>\n", "      <td>168</td>\n", "      <td>4.0</td>\n", "      <td>832449475</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13597</th>\n", "      <td>89</td>\n", "      <td>1682</td>\n", "      <td>0.5</td>\n", "      <td>1520408338</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22695</th>\n", "      <td>156</td>\n", "      <td>52</td>\n", "      <td>3.5</td>\n", "      <td>1106855391</td>\n", "    </tr>\n", "    <tr>\n", "      <th>53370</th>\n", "      <td>352</td>\n", "      <td>56171</td>\n", "      <td>2.5</td>\n", "      <td>1493674788</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>\n", "    <div class=\"colab-df-buttons\">\n", "\n", "  <div class=\"colab-df-container\">\n", "    <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-79d1925d-94eb-45d4-a2dc-719eef36c152')\"\n", "            title=\"Convert this dataframe to an interactive table.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\" viewBox=\"0 -960 960 960\">\n", "    <path d=\"M120-120v-720h720v720H120Zm60-500h600v-160H180v160Zm220 220h160v-160H400v160Zm0 220h160v-160H400v160ZM180-400h160v-160H180v160Zm440 0h160v-160H620v160ZM180-180h160v-160H180v160Zm440 0h160v-160H620v160Z\"/>\n", "  </svg>\n", "    </button>\n", "\n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    .colab-df-buttons div {\n", "      margin-bottom: 4px;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "    <script>\n", "      const buttonEl =\n", "        document.querySelector('#df-79d1925d-94eb-45d4-a2dc-719eef36c152 button.colab-df-convert');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      async function convertToInteractive(key) {\n", "        const element = document.querySelector('#df-79d1925d-94eb-45d4-a2dc-719eef36c152');\n", "        const dataTable =\n", "          await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                    [key], {});\n", "        if (!dataTable) return;\n", "\n", "        const docLinkHtml = 'Like what you see? Visit the ' +\n", "          '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "          + ' to learn more about interactive tables.';\n", "        element.innerHTML = '';\n", "        dataTable['output_type'] = 'display_data';\n", "        await google.colab.output.renderOutput(dataTable, element);\n", "        const docLink = document.createElement('div');\n", "        docLink.innerHTML = docLinkHtml;\n", "        element.appendChild(docLink);\n", "      }\n", "    </script>\n", "  </div>\n", "\n", "\n", "<div id=\"df-ba646cfb-6649-4888-92c9-39d421ebaf51\">\n", "  <button class=\"colab-df-quickchart\" onclick=\"quickchart('df-ba646cfb-6649-4888-92c9-39d421ebaf51')\"\n", "            title=\"Suggest charts\"\n", "            style=\"display:none;\">\n", "\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "     width=\"24px\">\n", "    <g>\n", "        <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n", "    </g>\n", "</svg>\n", "  </button>\n", "\n", "<style>\n", "  .colab-df-quickchart {\n", "      --bg-color: #E8F0FE;\n", "      --fill-color: #1967D2;\n", "      --hover-bg-color: #E2EBFA;\n", "      --hover-fill-color: #174EA6;\n", "      --disabled-fill-color: #AAA;\n", "      --disabled-bg-color: #DDD;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart {\n", "      --bg-color: #3B4455;\n", "      --fill-color: #D2E3FC;\n", "      --hover-bg-color: #434B5C;\n", "      --hover-fill-color: #FFFFFF;\n", "      --disabled-bg-color: #3B4455;\n", "      --disabled-fill-color: #666;\n", "  }\n", "\n", "  .colab-df-quickchart {\n", "    background-color: var(--bg-color);\n", "    border: none;\n", "    border-radius: 50%;\n", "    cursor: pointer;\n", "    display: none;\n", "    fill: var(--fill-color);\n", "    height: 32px;\n", "    padding: 0;\n", "    width: 32px;\n", "  }\n", "\n", "  .colab-df-quickchart:hover {\n", "    background-color: var(--hover-bg-color);\n", "    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "    fill: var(--button-hover-fill-color);\n", "  }\n", "\n", "  .colab-df-quickchart-complete:disabled,\n", "  .colab-df-quickchart-complete:disabled:hover {\n", "    background-color: var(--disabled-bg-color);\n", "    fill: var(--disabled-fill-color);\n", "    box-shadow: none;\n", "  }\n", "\n", "  .colab-df-spinner {\n", "    border: 2px solid var(--fill-color);\n", "    border-color: transparent;\n", "    border-bottom-color: var(--fill-color);\n", "    animation:\n", "      spin 1s steps(1) infinite;\n", "  }\n", "\n", "  @keyframes spin {\n", "    0% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "      border-left-color: var(--fill-color);\n", "    }\n", "    20% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    30% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    40% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    60% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    80% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "    90% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "  }\n", "</style>\n", "\n", "  <script>\n", "    async function quickchart(key) {\n", "      const quickchartButtonEl =\n", "        document.querySelector('#' + key + ' button');\n", "      quickchartButtonEl.disabled = true;  // To prevent multiple clicks.\n", "      quickchartButtonEl.classList.add('colab-df-spinner');\n", "      try {\n", "        const charts = await google.colab.kernel.invokeFunction(\n", "            'suggest<PERSON><PERSON>s', [key], {});\n", "      } catch (error) {\n", "        console.error('Error during call to suggest<PERSON>harts:', error);\n", "      }\n", "      quickchartButtonEl.classList.remove('colab-df-spinner');\n", "      quickchartButtonEl.classList.add('colab-df-quickchart-complete');\n", "    }\n", "    (() => {\n", "      let quickchartButtonEl =\n", "        document.querySelector('#df-ba646cfb-6649-4888-92c9-39d421ebaf51 button');\n", "      quickchartButtonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "    })();\n", "  </script>\n", "</div>\n", "\n", "    </div>\n", "  </div>\n"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "dataframe", "summary": "{\n  \"name\": \"rating_df\",\n  \"rows\": 5,\n  \"fields\": [\n    {\n      \"column\": \"userId\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 211,\n        \"min\": 89,\n        \"max\": 599,\n        \"num_unique_values\": 5,\n        \"samples\": [\n          136,\n          352,\n          89\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"movieId\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 25249,\n        \"min\": 52,\n        \"max\": 56171,\n        \"num_unique_values\": 5,\n        \"samples\": [\n          168,\n          56171,\n          1682\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"rating\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 1.396424004376894,\n        \"min\": 0.5,\n        \"max\": 4.0,\n        \"num_unique_values\": 4,\n        \"samples\": [\n          4.0,\n          2.5,\n          3.5\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"timestamp\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 312195453,\n        \"min\": 832449475,\n        \"max\": 1520408338,\n        \"num_unique_values\": 5,\n        \"samples\": [\n          832449475,\n          1493674788,\n          1520408338\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    }\n  ]\n}"}}, "metadata": {}, "execution_count": 5}], "source": ["rating_df.sample(5)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 424}, "id": "65c9c9b8-665f-434f-b3dc-fb02afd8e5a4", "outputId": "bd7252ed-6875-4710-c38c-b89dd63e17ea"}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["      movieId                           title  \\\n", "0           1                Toy Story (1995)   \n", "1           1                Toy Story (1995)   \n", "2           1                Toy Story (1995)   \n", "3           2                  <PERSON><PERSON><PERSON> (1995)   \n", "4           2                  <PERSON><PERSON><PERSON> (1995)   \n", "...       ...                             ...   \n", "3471   187595  Solo: A Star Wars Story (2018)   \n", "3472   193565       <PERSON><PERSON><PERSON>: The Movie (2010)   \n", "3473   193565       <PERSON><PERSON><PERSON>: The Movie (2010)   \n", "3474   193565       <PERSON><PERSON><PERSON>: The Movie (2010)   \n", "3475   193565       <PERSON>int<PERSON>: The Movie (2010)   \n", "\n", "                                           genres  userId  rating  \\\n", "0     Adventure|Animation|Children|Comedy|Fantasy     336     4.0   \n", "1     Adventure|Animation|Children|Comedy|Fantasy     474     4.0   \n", "2     Adventure|Animation|Children|Comedy|Fantasy     567     3.5   \n", "3                      Adventure|Children|Fantasy      62     4.0   \n", "4                      Adventure|Children|Fantasy      62     4.0   \n", "...                                           ...     ...     ...   \n", "3471             Action|Adventure|Children|Sci-Fi      62     4.0   \n", "3472               Action|Animation|Comedy|Sci-Fi     184     3.5   \n", "3473               Action|Animation|Comedy|Sci-Fi     184     3.5   \n", "3474               Action|Animation|Comedy|Sci-Fi     184     3.5   \n", "3475               Action|Animation|Comedy|Sci-Fi     184     3.5   \n", "\n", "      timestamp_x               tag  timestamp_y  \n", "0      1122227329             pixar   1139045764  \n", "1       978575760             pixar   1137206825  \n", "2      1525286001               fun   1525286013  \n", "3      1528843890           fantasy   1528843929  \n", "4      1528843890  magic board game   1528843932  \n", "...           ...               ...          ...  \n", "3471   1528934550         star wars   1528934552  \n", "3472   1537098554             anime   1537098582  \n", "3473   1537098554            comedy   1537098587  \n", "3474   1537098554           gintama   1537098603  \n", "3475   1537098554          remaster   1537098592  \n", "\n", "[3476 rows x 8 columns]"], "text/html": ["\n", "  <div id=\"df-fec0c70a-1918-493b-b8e7-b2a987cc57f2\" class=\"colab-df-container\">\n", "    <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>movieId</th>\n", "      <th>title</th>\n", "      <th>genres</th>\n", "      <th>userId</th>\n", "      <th>rating</th>\n", "      <th>timestamp_x</th>\n", "      <th>tag</th>\n", "      <th>timestamp_y</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>Toy Story (1995)</td>\n", "      <td>Adventure|Animation|Children|Comedy|Fantasy</td>\n", "      <td>336</td>\n", "      <td>4.0</td>\n", "      <td>1122227329</td>\n", "      <td>pixar</td>\n", "      <td>1139045764</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1</td>\n", "      <td>Toy Story (1995)</td>\n", "      <td>Adventure|Animation|Children|Comedy|Fantasy</td>\n", "      <td>474</td>\n", "      <td>4.0</td>\n", "      <td>978575760</td>\n", "      <td>pixar</td>\n", "      <td>1137206825</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1</td>\n", "      <td>Toy Story (1995)</td>\n", "      <td>Adventure|Animation|Children|Comedy|Fantasy</td>\n", "      <td>567</td>\n", "      <td>3.5</td>\n", "      <td>1525286001</td>\n", "      <td>fun</td>\n", "      <td>1525286013</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2</td>\n", "      <td><PERSON><PERSON><PERSON> (1995)</td>\n", "      <td>Adventure|Children|Fantasy</td>\n", "      <td>62</td>\n", "      <td>4.0</td>\n", "      <td>1528843890</td>\n", "      <td>fantasy</td>\n", "      <td>1528843929</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2</td>\n", "      <td><PERSON><PERSON><PERSON> (1995)</td>\n", "      <td>Adventure|Children|Fantasy</td>\n", "      <td>62</td>\n", "      <td>4.0</td>\n", "      <td>1528843890</td>\n", "      <td>magic board game</td>\n", "      <td>1528843932</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3471</th>\n", "      <td>187595</td>\n", "      <td>Solo: A Star Wars Story (2018)</td>\n", "      <td>Action|Adventure|Children|Sci-Fi</td>\n", "      <td>62</td>\n", "      <td>4.0</td>\n", "      <td>1528934550</td>\n", "      <td>star wars</td>\n", "      <td>1528934552</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3472</th>\n", "      <td>193565</td>\n", "      <td><PERSON><PERSON><PERSON>: The Movie (2010)</td>\n", "      <td>Action|Animation|Comedy|Sci-Fi</td>\n", "      <td>184</td>\n", "      <td>3.5</td>\n", "      <td>1537098554</td>\n", "      <td>anime</td>\n", "      <td>1537098582</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3473</th>\n", "      <td>193565</td>\n", "      <td><PERSON><PERSON><PERSON>: The Movie (2010)</td>\n", "      <td>Action|Animation|Comedy|Sci-Fi</td>\n", "      <td>184</td>\n", "      <td>3.5</td>\n", "      <td>1537098554</td>\n", "      <td>comedy</td>\n", "      <td>1537098587</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3474</th>\n", "      <td>193565</td>\n", "      <td><PERSON><PERSON><PERSON>: The Movie (2010)</td>\n", "      <td>Action|Animation|Comedy|Sci-Fi</td>\n", "      <td>184</td>\n", "      <td>3.5</td>\n", "      <td>1537098554</td>\n", "      <td>gintama</td>\n", "      <td>1537098603</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3475</th>\n", "      <td>193565</td>\n", "      <td><PERSON><PERSON><PERSON>: The Movie (2010)</td>\n", "      <td>Action|Animation|Comedy|Sci-Fi</td>\n", "      <td>184</td>\n", "      <td>3.5</td>\n", "      <td>1537098554</td>\n", "      <td>remaster</td>\n", "      <td>1537098592</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>3476 rows × 8 columns</p>\n", "</div>\n", "    <div class=\"colab-df-buttons\">\n", "\n", "  <div class=\"colab-df-container\">\n", "    <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-fec0c70a-1918-493b-b8e7-b2a987cc57f2')\"\n", "            title=\"Convert this dataframe to an interactive table.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\" viewBox=\"0 -960 960 960\">\n", "    <path d=\"M120-120v-720h720v720H120Zm60-500h600v-160H180v160Zm220 220h160v-160H400v160Zm0 220h160v-160H400v160ZM180-400h160v-160H180v160Zm440 0h160v-160H620v160ZM180-180h160v-160H180v160Zm440 0h160v-160H620v160Z\"/>\n", "  </svg>\n", "    </button>\n", "\n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    .colab-df-buttons div {\n", "      margin-bottom: 4px;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "    <script>\n", "      const buttonEl =\n", "        document.querySelector('#df-fec0c70a-1918-493b-b8e7-b2a987cc57f2 button.colab-df-convert');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      async function convertToInteractive(key) {\n", "        const element = document.querySelector('#df-fec0c70a-1918-493b-b8e7-b2a987cc57f2');\n", "        const dataTable =\n", "          await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                    [key], {});\n", "        if (!dataTable) return;\n", "\n", "        const docLinkHtml = 'Like what you see? Visit the ' +\n", "          '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "          + ' to learn more about interactive tables.';\n", "        element.innerHTML = '';\n", "        dataTable['output_type'] = 'display_data';\n", "        await google.colab.output.renderOutput(dataTable, element);\n", "        const docLink = document.createElement('div');\n", "        docLink.innerHTML = docLinkHtml;\n", "        element.appendChild(docLink);\n", "      }\n", "    </script>\n", "  </div>\n", "\n", "\n", "<div id=\"df-cd84dc37-7149-4683-b2af-1bf7f040ad6b\">\n", "  <button class=\"colab-df-quickchart\" onclick=\"quickchart('df-cd84dc37-7149-4683-b2af-1bf7f040ad6b')\"\n", "            title=\"Suggest charts\"\n", "            style=\"display:none;\">\n", "\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "     width=\"24px\">\n", "    <g>\n", "        <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n", "    </g>\n", "</svg>\n", "  </button>\n", "\n", "<style>\n", "  .colab-df-quickchart {\n", "      --bg-color: #E8F0FE;\n", "      --fill-color: #1967D2;\n", "      --hover-bg-color: #E2EBFA;\n", "      --hover-fill-color: #174EA6;\n", "      --disabled-fill-color: #AAA;\n", "      --disabled-bg-color: #DDD;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart {\n", "      --bg-color: #3B4455;\n", "      --fill-color: #D2E3FC;\n", "      --hover-bg-color: #434B5C;\n", "      --hover-fill-color: #FFFFFF;\n", "      --disabled-bg-color: #3B4455;\n", "      --disabled-fill-color: #666;\n", "  }\n", "\n", "  .colab-df-quickchart {\n", "    background-color: var(--bg-color);\n", "    border: none;\n", "    border-radius: 50%;\n", "    cursor: pointer;\n", "    display: none;\n", "    fill: var(--fill-color);\n", "    height: 32px;\n", "    padding: 0;\n", "    width: 32px;\n", "  }\n", "\n", "  .colab-df-quickchart:hover {\n", "    background-color: var(--hover-bg-color);\n", "    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "    fill: var(--button-hover-fill-color);\n", "  }\n", "\n", "  .colab-df-quickchart-complete:disabled,\n", "  .colab-df-quickchart-complete:disabled:hover {\n", "    background-color: var(--disabled-bg-color);\n", "    fill: var(--disabled-fill-color);\n", "    box-shadow: none;\n", "  }\n", "\n", "  .colab-df-spinner {\n", "    border: 2px solid var(--fill-color);\n", "    border-color: transparent;\n", "    border-bottom-color: var(--fill-color);\n", "    animation:\n", "      spin 1s steps(1) infinite;\n", "  }\n", "\n", "  @keyframes spin {\n", "    0% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "      border-left-color: var(--fill-color);\n", "    }\n", "    20% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    30% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    40% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    60% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    80% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "    90% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "  }\n", "</style>\n", "\n", "  <script>\n", "    async function quickchart(key) {\n", "      const quickchartButtonEl =\n", "        document.querySelector('#' + key + ' button');\n", "      quickchartButtonEl.disabled = true;  // To prevent multiple clicks.\n", "      quickchartButtonEl.classList.add('colab-df-spinner');\n", "      try {\n", "        const charts = await google.colab.kernel.invokeFunction(\n", "            'suggest<PERSON><PERSON>s', [key], {});\n", "      } catch (error) {\n", "        console.error('Error during call to suggest<PERSON>harts:', error);\n", "      }\n", "      quickchartButtonEl.classList.remove('colab-df-spinner');\n", "      quickchartButtonEl.classList.add('colab-df-quickchart-complete');\n", "    }\n", "    (() => {\n", "      let quickchartButtonEl =\n", "        document.querySelector('#df-cd84dc37-7149-4683-b2af-1bf7f040ad6b button');\n", "      quickchartButtonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "    })();\n", "  </script>\n", "</div>\n", "\n", "  <div id=\"id_7d0e9360-a88b-4f78-81fb-5ed59b14bb60\">\n", "    <style>\n", "      .colab-df-generate {\n", "        background-color: #E8F0FE;\n", "        border: none;\n", "        border-radius: 50%;\n", "        cursor: pointer;\n", "        display: none;\n", "        fill: #1967D2;\n", "        height: 32px;\n", "        padding: 0 0 0 0;\n", "        width: 32px;\n", "      }\n", "\n", "      .colab-df-generate:hover {\n", "        background-color: #E2EBFA;\n", "        box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "        fill: #174EA6;\n", "      }\n", "\n", "      [theme=dark] .colab-df-generate {\n", "        background-color: #3B4455;\n", "        fill: #D2E3FC;\n", "      }\n", "\n", "      [theme=dark] .colab-df-generate:hover {\n", "        background-color: #434B5C;\n", "        box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "        filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "        fill: #FFFFFF;\n", "      }\n", "    </style>\n", "    <button class=\"colab-df-generate\" onclick=\"generateWithVariable('df')\"\n", "            title=\"Generate code using this dataframe.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "       width=\"24px\">\n", "    <path d=\"M7,19H8.4L18.45,9,17,7.55,7,17.6ZM5,21V16.75L18.45,3.32a2,2,0,0,1,2.83,0l1.4,1.43a1.91,1.91,0,0,1,.58,1.4,1.91,1.91,0,0,1-.58,1.4L9.25,21ZM18.45,9,17,7.55Zm-12,3A5.31,5.31,0,0,0,4.9,8.1,5.31,5.31,0,0,0,1,6.5,5.31,5.31,0,0,0,4.9,4.9,5.31,5.31,0,0,0,6.5,1,5.31,5.31,0,0,0,8.1,4.9,5.31,5.31,0,0,0,12,6.5,5.46,5.46,0,0,0,6.5,12Z\"/>\n", "  </svg>\n", "    </button>\n", "    <script>\n", "      (() => {\n", "      const buttonEl =\n", "        document.querySelector('#id_7d0e9360-a88b-4f78-81fb-5ed59b14bb60 button.colab-df-generate');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      buttonEl.onclick = () => {\n", "        google.colab.notebook.generateWithVariable('df');\n", "      }\n", "      })();\n", "    </script>\n", "  </div>\n", "\n", "    </div>\n", "  </div>\n"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "dataframe", "variable_name": "df", "summary": "{\n  \"name\": \"df\",\n  \"rows\": 3476,\n  \"fields\": [\n    {\n      \"column\": \"movieId\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 44138,\n        \"min\": 1,\n        \"max\": 193565,\n        \"num_unique_values\": 1464,\n        \"samples\": [\n          58295,\n          904,\n          1196\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"title\",\n      \"properties\": {\n        \"dtype\": \"category\",\n        \"num_unique_values\": 1464,\n        \"samples\": [\n          \"Bank Job, The (2008)\",\n          \"Rear Window (1954)\",\n          \"Star Wars: Episode V - The Empire Strikes Back (1980)\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"genres\",\n      \"properties\": {\n        \"dtype\": \"category\",\n        \"num_unique_values\": 364,\n        \"samples\": [\n          \"Action|Drama|Sci-Fi\",\n          \"Action|Crime|Drama|Thriller\",\n          \"Comedy|Drama|Thriller\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"userId\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 161,\n        \"min\": 2,\n        \"max\": 610,\n        \"num_unique_values\": 54,\n        \"samples\": [\n          18,\n          506,\n          487\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"rating\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0.8569253156474246,\n        \"min\": 0.5,\n        \"max\": 5.0,\n        \"num_unique_values\": 10,\n        \"samples\": [\n          2.0,\n          3.5,\n          5.0\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"timestamp_x\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 203807978,\n        \"min\": 974666694,\n        \"max\": 1537098554,\n        \"num_unique_values\": 1587,\n        \"samples\": [\n          1089386393,\n          1453051531,\n          1241762530\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"tag\",\n      \"properties\": {\n        \"dtype\": \"category\",\n        \"num_unique_values\": 1543,\n        \"samples\": [\n          \"Gal Gadot\",\n          \"trippy\",\n          \"kung fu\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"timestamp_y\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 173155421,\n        \"min\": 1137179352,\n        \"max\": 1537098603,\n        \"num_unique_values\": 3219,\n        \"samples\": [\n          1498456343,\n          1457844066,\n          1138307096\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    }\n  ]\n}"}}, "metadata": {}, "execution_count": 6}], "source": ["# We will merge the three dataframes to create a single dataframe that contains all the information we need.\n", "user_movie_df = movie_df.merge(rating_df, on = 'movieId', how = 'inner')\n", "df = user_movie_df.merge(tag_df, on = ['movieId', 'userId'], how = 'inner')\n", "df"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 424}, "id": "77b9e5bb-bd36-482d-9b76-1031b72a28cf", "outputId": "cd1194e4-af89-4097-8def-a3ee61e2fd5b"}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["      movieId                           title  \\\n", "0           1                Toy Story (1995)   \n", "1           1                Toy Story (1995)   \n", "2           1                Toy Story (1995)   \n", "3           2                  <PERSON><PERSON><PERSON> (1995)   \n", "4           2                  <PERSON><PERSON><PERSON> (1995)   \n", "...       ...                             ...   \n", "3471   187595  Solo: A Star Wars Story (2018)   \n", "3472   193565       <PERSON><PERSON><PERSON>: The Movie (2010)   \n", "3473   193565       <PERSON><PERSON><PERSON>: The Movie (2010)   \n", "3474   193565       <PERSON><PERSON><PERSON>: The Movie (2010)   \n", "3475   193565       <PERSON>int<PERSON>: The Movie (2010)   \n", "\n", "                                           genres  userId  rating  \\\n", "0     Adventure|Animation|Children|Comedy|Fantasy     336     4.0   \n", "1     Adventure|Animation|Children|Comedy|Fantasy     474     4.0   \n", "2     Adventure|Animation|Children|Comedy|Fantasy     567     3.5   \n", "3                      Adventure|Children|Fantasy      62     4.0   \n", "4                      Adventure|Children|Fantasy      62     4.0   \n", "...                                           ...     ...     ...   \n", "3471             Action|Adventure|Children|Sci-Fi      62     4.0   \n", "3472               Action|Animation|Comedy|Sci-Fi     184     3.5   \n", "3473               Action|Animation|Comedy|Sci-Fi     184     3.5   \n", "3474               Action|Animation|Comedy|Sci-Fi     184     3.5   \n", "3475               Action|Animation|Comedy|Sci-Fi     184     3.5   \n", "\n", "                   tag  \n", "0                pixar  \n", "1                pixar  \n", "2                  fun  \n", "3              fantasy  \n", "4     magic board game  \n", "...                ...  \n", "3471         star wars  \n", "3472             anime  \n", "3473            comedy  \n", "3474           gintama  \n", "3475          remaster  \n", "\n", "[3476 rows x 6 columns]"], "text/html": ["\n", "  <div id=\"df-bf8f0845-3554-4c10-9e19-c49c415cb546\" class=\"colab-df-container\">\n", "    <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>movieId</th>\n", "      <th>title</th>\n", "      <th>genres</th>\n", "      <th>userId</th>\n", "      <th>rating</th>\n", "      <th>tag</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>Toy Story (1995)</td>\n", "      <td>Adventure|Animation|Children|Comedy|Fantasy</td>\n", "      <td>336</td>\n", "      <td>4.0</td>\n", "      <td>pixar</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1</td>\n", "      <td>Toy Story (1995)</td>\n", "      <td>Adventure|Animation|Children|Comedy|Fantasy</td>\n", "      <td>474</td>\n", "      <td>4.0</td>\n", "      <td>pixar</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1</td>\n", "      <td>Toy Story (1995)</td>\n", "      <td>Adventure|Animation|Children|Comedy|Fantasy</td>\n", "      <td>567</td>\n", "      <td>3.5</td>\n", "      <td>fun</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2</td>\n", "      <td><PERSON><PERSON><PERSON> (1995)</td>\n", "      <td>Adventure|Children|Fantasy</td>\n", "      <td>62</td>\n", "      <td>4.0</td>\n", "      <td>fantasy</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2</td>\n", "      <td><PERSON><PERSON><PERSON> (1995)</td>\n", "      <td>Adventure|Children|Fantasy</td>\n", "      <td>62</td>\n", "      <td>4.0</td>\n", "      <td>magic board game</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3471</th>\n", "      <td>187595</td>\n", "      <td>Solo: A Star Wars Story (2018)</td>\n", "      <td>Action|Adventure|Children|Sci-Fi</td>\n", "      <td>62</td>\n", "      <td>4.0</td>\n", "      <td>star wars</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3472</th>\n", "      <td>193565</td>\n", "      <td><PERSON><PERSON><PERSON>: The Movie (2010)</td>\n", "      <td>Action|Animation|Comedy|Sci-Fi</td>\n", "      <td>184</td>\n", "      <td>3.5</td>\n", "      <td>anime</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3473</th>\n", "      <td>193565</td>\n", "      <td><PERSON><PERSON><PERSON>: The Movie (2010)</td>\n", "      <td>Action|Animation|Comedy|Sci-Fi</td>\n", "      <td>184</td>\n", "      <td>3.5</td>\n", "      <td>comedy</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3474</th>\n", "      <td>193565</td>\n", "      <td><PERSON><PERSON><PERSON>: The Movie (2010)</td>\n", "      <td>Action|Animation|Comedy|Sci-Fi</td>\n", "      <td>184</td>\n", "      <td>3.5</td>\n", "      <td>gintama</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3475</th>\n", "      <td>193565</td>\n", "      <td><PERSON><PERSON><PERSON>: The Movie (2010)</td>\n", "      <td>Action|Animation|Comedy|Sci-Fi</td>\n", "      <td>184</td>\n", "      <td>3.5</td>\n", "      <td>remaster</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>3476 rows × 6 columns</p>\n", "</div>\n", "    <div class=\"colab-df-buttons\">\n", "\n", "  <div class=\"colab-df-container\">\n", "    <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-bf8f0845-3554-4c10-9e19-c49c415cb546')\"\n", "            title=\"Convert this dataframe to an interactive table.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\" viewBox=\"0 -960 960 960\">\n", "    <path d=\"M120-120v-720h720v720H120Zm60-500h600v-160H180v160Zm220 220h160v-160H400v160Zm0 220h160v-160H400v160ZM180-400h160v-160H180v160Zm440 0h160v-160H620v160ZM180-180h160v-160H180v160Zm440 0h160v-160H620v160Z\"/>\n", "  </svg>\n", "    </button>\n", "\n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    .colab-df-buttons div {\n", "      margin-bottom: 4px;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "    <script>\n", "      const buttonEl =\n", "        document.querySelector('#df-bf8f0845-3554-4c10-9e19-c49c415cb546 button.colab-df-convert');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      async function convertToInteractive(key) {\n", "        const element = document.querySelector('#df-bf8f0845-3554-4c10-9e19-c49c415cb546');\n", "        const dataTable =\n", "          await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                    [key], {});\n", "        if (!dataTable) return;\n", "\n", "        const docLinkHtml = 'Like what you see? Visit the ' +\n", "          '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "          + ' to learn more about interactive tables.';\n", "        element.innerHTML = '';\n", "        dataTable['output_type'] = 'display_data';\n", "        await google.colab.output.renderOutput(dataTable, element);\n", "        const docLink = document.createElement('div');\n", "        docLink.innerHTML = docLinkHtml;\n", "        element.appendChild(docLink);\n", "      }\n", "    </script>\n", "  </div>\n", "\n", "\n", "<div id=\"df-eb571e9d-7b89-47ec-97c2-e729858af4be\">\n", "  <button class=\"colab-df-quickchart\" onclick=\"quickchart('df-eb571e9d-7b89-47ec-97c2-e729858af4be')\"\n", "            title=\"Suggest charts\"\n", "            style=\"display:none;\">\n", "\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "     width=\"24px\">\n", "    <g>\n", "        <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n", "    </g>\n", "</svg>\n", "  </button>\n", "\n", "<style>\n", "  .colab-df-quickchart {\n", "      --bg-color: #E8F0FE;\n", "      --fill-color: #1967D2;\n", "      --hover-bg-color: #E2EBFA;\n", "      --hover-fill-color: #174EA6;\n", "      --disabled-fill-color: #AAA;\n", "      --disabled-bg-color: #DDD;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart {\n", "      --bg-color: #3B4455;\n", "      --fill-color: #D2E3FC;\n", "      --hover-bg-color: #434B5C;\n", "      --hover-fill-color: #FFFFFF;\n", "      --disabled-bg-color: #3B4455;\n", "      --disabled-fill-color: #666;\n", "  }\n", "\n", "  .colab-df-quickchart {\n", "    background-color: var(--bg-color);\n", "    border: none;\n", "    border-radius: 50%;\n", "    cursor: pointer;\n", "    display: none;\n", "    fill: var(--fill-color);\n", "    height: 32px;\n", "    padding: 0;\n", "    width: 32px;\n", "  }\n", "\n", "  .colab-df-quickchart:hover {\n", "    background-color: var(--hover-bg-color);\n", "    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "    fill: var(--button-hover-fill-color);\n", "  }\n", "\n", "  .colab-df-quickchart-complete:disabled,\n", "  .colab-df-quickchart-complete:disabled:hover {\n", "    background-color: var(--disabled-bg-color);\n", "    fill: var(--disabled-fill-color);\n", "    box-shadow: none;\n", "  }\n", "\n", "  .colab-df-spinner {\n", "    border: 2px solid var(--fill-color);\n", "    border-color: transparent;\n", "    border-bottom-color: var(--fill-color);\n", "    animation:\n", "      spin 1s steps(1) infinite;\n", "  }\n", "\n", "  @keyframes spin {\n", "    0% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "      border-left-color: var(--fill-color);\n", "    }\n", "    20% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    30% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    40% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    60% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    80% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "    90% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "  }\n", "</style>\n", "\n", "  <script>\n", "    async function quickchart(key) {\n", "      const quickchartButtonEl =\n", "        document.querySelector('#' + key + ' button');\n", "      quickchartButtonEl.disabled = true;  // To prevent multiple clicks.\n", "      quickchartButtonEl.classList.add('colab-df-spinner');\n", "      try {\n", "        const charts = await google.colab.kernel.invokeFunction(\n", "            'suggest<PERSON><PERSON>s', [key], {});\n", "      } catch (error) {\n", "        console.error('Error during call to suggest<PERSON>harts:', error);\n", "      }\n", "      quickchartButtonEl.classList.remove('colab-df-spinner');\n", "      quickchartButtonEl.classList.add('colab-df-quickchart-complete');\n", "    }\n", "    (() => {\n", "      let quickchartButtonEl =\n", "        document.querySelector('#df-eb571e9d-7b89-47ec-97c2-e729858af4be button');\n", "      quickchartButtonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "    })();\n", "  </script>\n", "</div>\n", "\n", "  <div id=\"id_361ec02d-d899-426a-9559-c77b62811424\">\n", "    <style>\n", "      .colab-df-generate {\n", "        background-color: #E8F0FE;\n", "        border: none;\n", "        border-radius: 50%;\n", "        cursor: pointer;\n", "        display: none;\n", "        fill: #1967D2;\n", "        height: 32px;\n", "        padding: 0 0 0 0;\n", "        width: 32px;\n", "      }\n", "\n", "      .colab-df-generate:hover {\n", "        background-color: #E2EBFA;\n", "        box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "        fill: #174EA6;\n", "      }\n", "\n", "      [theme=dark] .colab-df-generate {\n", "        background-color: #3B4455;\n", "        fill: #D2E3FC;\n", "      }\n", "\n", "      [theme=dark] .colab-df-generate:hover {\n", "        background-color: #434B5C;\n", "        box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "        filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "        fill: #FFFFFF;\n", "      }\n", "    </style>\n", "    <button class=\"colab-df-generate\" onclick=\"generateWithVariable('df')\"\n", "            title=\"Generate code using this dataframe.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "       width=\"24px\">\n", "    <path d=\"M7,19H8.4L18.45,9,17,7.55,7,17.6ZM5,21V16.75L18.45,3.32a2,2,0,0,1,2.83,0l1.4,1.43a1.91,1.91,0,0,1,.58,1.4,1.91,1.91,0,0,1-.58,1.4L9.25,21ZM18.45,9,17,7.55Zm-12,3A5.31,5.31,0,0,0,4.9,8.1,5.31,5.31,0,0,0,1,6.5,5.31,5.31,0,0,0,4.9,4.9,5.31,5.31,0,0,0,6.5,1,5.31,5.31,0,0,0,8.1,4.9,5.31,5.31,0,0,0,12,6.5,5.46,5.46,0,0,0,6.5,12Z\"/>\n", "  </svg>\n", "    </button>\n", "    <script>\n", "      (() => {\n", "      const buttonEl =\n", "        document.querySelector('#id_361ec02d-d899-426a-9559-c77b62811424 button.colab-df-generate');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      buttonEl.onclick = () => {\n", "        google.colab.notebook.generateWithVariable('df');\n", "      }\n", "      })();\n", "    </script>\n", "  </div>\n", "\n", "    </div>\n", "  </div>\n"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "dataframe", "variable_name": "df", "summary": "{\n  \"name\": \"df\",\n  \"rows\": 3476,\n  \"fields\": [\n    {\n      \"column\": \"movieId\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 44138,\n        \"min\": 1,\n        \"max\": 193565,\n        \"num_unique_values\": 1464,\n        \"samples\": [\n          58295,\n          904,\n          1196\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"title\",\n      \"properties\": {\n        \"dtype\": \"category\",\n        \"num_unique_values\": 1464,\n        \"samples\": [\n          \"Bank Job, The (2008)\",\n          \"Rear Window (1954)\",\n          \"Star Wars: Episode V - The Empire Strikes Back (1980)\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"genres\",\n      \"properties\": {\n        \"dtype\": \"category\",\n        \"num_unique_values\": 364,\n        \"samples\": [\n          \"Action|Drama|Sci-Fi\",\n          \"Action|Crime|Drama|Thriller\",\n          \"Comedy|Drama|Thriller\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"userId\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 161,\n        \"min\": 2,\n        \"max\": 610,\n        \"num_unique_values\": 54,\n        \"samples\": [\n          18,\n          506,\n          487\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"rating\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0.8569253156474246,\n        \"min\": 0.5,\n        \"max\": 5.0,\n        \"num_unique_values\": 10,\n        \"samples\": [\n          2.0,\n          3.5,\n          5.0\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"tag\",\n      \"properties\": {\n        \"dtype\": \"category\",\n        \"num_unique_values\": 1543,\n        \"samples\": [\n          \"Gal Gadot\",\n          \"trippy\",\n          \"kung fu\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    }\n  ]\n}"}}, "metadata": {}, "execution_count": 7}], "source": ["# Here, we will drop the timestamp columns as they are not needed for our analysis.\n", "df.drop(columns = ['timestamp_x', 'timestamp_y'], inplace = True)\n", "df"]}, {"cell_type": "markdown", "metadata": {"id": "b2138026-78b9-437a-b53c-f339bd9172e9"}, "source": ["---\n", "## <a id='toc1_4_'></a>[Exploratory data analysis (EDA)](#toc0_)\n"]}, {"cell_type": "markdown", "metadata": {"id": "197e0064-c98a-47d0-adaf-8bab646946bf"}, "source": ["Before doing any preprocessing, we will be performing some simple exploratory data analysis (EDA) to know about our dataset. This includes looking at the number of unique values/number of duplicate values, the distributions, etc.\n", "\n", "First, looking at the shape of the `pd.DataFrame`\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "61b07e9f-70ec-4262-97e2-848f6200465e", "outputId": "92881802-614e-4518-aac7-6f32ac5b3db1"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Number of rows:  3476\n", "Number of columns:  6\n"]}], "source": ["print('Number of rows: ' , df.shape[0])\n", "print('Number of columns: ' , df.shape[1])"]}, {"cell_type": "markdown", "metadata": {"id": "34c34e76-1476-4795-b942-253b0a091cc0"}, "source": ["Looking at the data type of each columns:\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 272}, "id": "0cf80999-e0d2-48fc-bfd1-26ac32cd77ba", "outputId": "53d2f268-e718-46f6-b42e-ba7df6a41249"}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["movieId      int64\n", "title       object\n", "genres      object\n", "userId       int64\n", "rating     float64\n", "tag         object\n", "dtype: object"], "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>0</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>movieId</th>\n", "      <td>int64</td>\n", "    </tr>\n", "    <tr>\n", "      <th>title</th>\n", "      <td>object</td>\n", "    </tr>\n", "    <tr>\n", "      <th>genres</th>\n", "      <td>object</td>\n", "    </tr>\n", "    <tr>\n", "      <th>userId</th>\n", "      <td>int64</td>\n", "    </tr>\n", "    <tr>\n", "      <th>rating</th>\n", "      <td>float64</td>\n", "    </tr>\n", "    <tr>\n", "      <th>tag</th>\n", "      <td>object</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div><br><label><b>dtype:</b> object</label>"]}, "metadata": {}, "execution_count": 9}], "source": ["df.dtypes"]}, {"cell_type": "markdown", "metadata": {"id": "6604a967-f4de-4e99-8832-a58591e8992c"}, "source": ["Next, let's see if we have any null values:\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 272}, "id": "a7fdf80c-26d5-487e-a02e-82e23077d75f", "outputId": "1490f371-1ea8-45b8-c89b-4d24885f4bb0"}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["movieId    False\n", "title      False\n", "genres     False\n", "userId     False\n", "rating     False\n", "tag        False\n", "dtype: bool"], "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>0</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>movieId</th>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>title</th>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>genres</th>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>userId</th>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>rating</th>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>tag</th>\n", "      <td>False</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div><br><label><b>dtype:</b> bool</label>"]}, "metadata": {}, "execution_count": 10}], "source": ["# Deal with null values\n", "df.isnull().any()"]}, {"cell_type": "markdown", "metadata": {"id": "2ac9437f-53ba-4c34-b8d7-3af652f8e348"}, "source": ["## <a id='toc1_5_'></a>[Popularity-based recommendation](#toc0_)\n", "\n", "The popularity based recommendation recommends items, in this case, movies, based on what is popular accross the site. It is the most basic recommendation system. The system identifies popular items by considering metrics such as the number of views, ratings, or purchases and suggests these items to all users. For this type of recommendation system, all users get the same recommendations. The system can suggest items based on what's popular in your country.\n", "\n", "This approach ensures that users are aware of current popular content, which can be useful for new users who have not yet developed a viewing history on the platform. However, this is also a limitation because everyone receives the same suggestions, which may not always be relevant or interesting to them. This lack of specificity can result in a less engaging user experience compared to more personalized recommendation systems.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 424}, "id": "244799de-793e-46d0-a8e2-721ccec85187", "outputId": "1cc0b59c-ca13-4c5a-be04-aca5f80406e9"}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["      movieId                           title  \\\n", "0           1                Toy Story (1995)   \n", "1           1                Toy Story (1995)   \n", "2           1                Toy Story (1995)   \n", "3           2                  <PERSON><PERSON><PERSON> (1995)   \n", "4           2                  <PERSON><PERSON><PERSON> (1995)   \n", "...       ...                             ...   \n", "3471   187595  Solo: A Star Wars Story (2018)   \n", "3472   193565       <PERSON><PERSON><PERSON>: The Movie (2010)   \n", "3473   193565       <PERSON><PERSON><PERSON>: The Movie (2010)   \n", "3474   193565       <PERSON><PERSON><PERSON>: The Movie (2010)   \n", "3475   193565       <PERSON>int<PERSON>: The Movie (2010)   \n", "\n", "                                           genres  userId  rating  \\\n", "0     Adventure|Animation|Children|Comedy|Fantasy     336     4.0   \n", "1     Adventure|Animation|Children|Comedy|Fantasy     474     4.0   \n", "2     Adventure|Animation|Children|Comedy|Fantasy     567     3.5   \n", "3                      Adventure|Children|Fantasy      62     4.0   \n", "4                      Adventure|Children|Fantasy      62     4.0   \n", "...                                           ...     ...     ...   \n", "3471             Action|Adventure|Children|Sci-Fi      62     4.0   \n", "3472               Action|Animation|Comedy|Sci-Fi     184     3.5   \n", "3473               Action|Animation|Comedy|Sci-Fi     184     3.5   \n", "3474               Action|Animation|Comedy|Sci-Fi     184     3.5   \n", "3475               Action|Animation|Comedy|Sci-Fi     184     3.5   \n", "\n", "                   tag  \n", "0                pixar  \n", "1                pixar  \n", "2                  fun  \n", "3              fantasy  \n", "4     magic board game  \n", "...                ...  \n", "3471         star wars  \n", "3472             anime  \n", "3473            comedy  \n", "3474           gintama  \n", "3475          remaster  \n", "\n", "[3476 rows x 6 columns]"], "text/html": ["\n", "  <div id=\"df-a230a50d-0b4b-4bea-9c3e-b48337eb620a\" class=\"colab-df-container\">\n", "    <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>movieId</th>\n", "      <th>title</th>\n", "      <th>genres</th>\n", "      <th>userId</th>\n", "      <th>rating</th>\n", "      <th>tag</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>Toy Story (1995)</td>\n", "      <td>Adventure|Animation|Children|Comedy|Fantasy</td>\n", "      <td>336</td>\n", "      <td>4.0</td>\n", "      <td>pixar</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1</td>\n", "      <td>Toy Story (1995)</td>\n", "      <td>Adventure|Animation|Children|Comedy|Fantasy</td>\n", "      <td>474</td>\n", "      <td>4.0</td>\n", "      <td>pixar</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1</td>\n", "      <td>Toy Story (1995)</td>\n", "      <td>Adventure|Animation|Children|Comedy|Fantasy</td>\n", "      <td>567</td>\n", "      <td>3.5</td>\n", "      <td>fun</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2</td>\n", "      <td><PERSON><PERSON><PERSON> (1995)</td>\n", "      <td>Adventure|Children|Fantasy</td>\n", "      <td>62</td>\n", "      <td>4.0</td>\n", "      <td>fantasy</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2</td>\n", "      <td><PERSON><PERSON><PERSON> (1995)</td>\n", "      <td>Adventure|Children|Fantasy</td>\n", "      <td>62</td>\n", "      <td>4.0</td>\n", "      <td>magic board game</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3471</th>\n", "      <td>187595</td>\n", "      <td>Solo: A Star Wars Story (2018)</td>\n", "      <td>Action|Adventure|Children|Sci-Fi</td>\n", "      <td>62</td>\n", "      <td>4.0</td>\n", "      <td>star wars</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3472</th>\n", "      <td>193565</td>\n", "      <td><PERSON><PERSON><PERSON>: The Movie (2010)</td>\n", "      <td>Action|Animation|Comedy|Sci-Fi</td>\n", "      <td>184</td>\n", "      <td>3.5</td>\n", "      <td>anime</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3473</th>\n", "      <td>193565</td>\n", "      <td><PERSON><PERSON><PERSON>: The Movie (2010)</td>\n", "      <td>Action|Animation|Comedy|Sci-Fi</td>\n", "      <td>184</td>\n", "      <td>3.5</td>\n", "      <td>comedy</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3474</th>\n", "      <td>193565</td>\n", "      <td><PERSON><PERSON><PERSON>: The Movie (2010)</td>\n", "      <td>Action|Animation|Comedy|Sci-Fi</td>\n", "      <td>184</td>\n", "      <td>3.5</td>\n", "      <td>gintama</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3475</th>\n", "      <td>193565</td>\n", "      <td><PERSON><PERSON><PERSON>: The Movie (2010)</td>\n", "      <td>Action|Animation|Comedy|Sci-Fi</td>\n", "      <td>184</td>\n", "      <td>3.5</td>\n", "      <td>remaster</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>3476 rows × 6 columns</p>\n", "</div>\n", "    <div class=\"colab-df-buttons\">\n", "\n", "  <div class=\"colab-df-container\">\n", "    <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-a230a50d-0b4b-4bea-9c3e-b48337eb620a')\"\n", "            title=\"Convert this dataframe to an interactive table.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\" viewBox=\"0 -960 960 960\">\n", "    <path d=\"M120-120v-720h720v720H120Zm60-500h600v-160H180v160Zm220 220h160v-160H400v160Zm0 220h160v-160H400v160ZM180-400h160v-160H180v160Zm440 0h160v-160H620v160ZM180-180h160v-160H180v160Zm440 0h160v-160H620v160Z\"/>\n", "  </svg>\n", "    </button>\n", "\n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    .colab-df-buttons div {\n", "      margin-bottom: 4px;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "    <script>\n", "      const buttonEl =\n", "        document.querySelector('#df-a230a50d-0b4b-4bea-9c3e-b48337eb620a button.colab-df-convert');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      async function convertToInteractive(key) {\n", "        const element = document.querySelector('#df-a230a50d-0b4b-4bea-9c3e-b48337eb620a');\n", "        const dataTable =\n", "          await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                    [key], {});\n", "        if (!dataTable) return;\n", "\n", "        const docLinkHtml = 'Like what you see? Visit the ' +\n", "          '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "          + ' to learn more about interactive tables.';\n", "        element.innerHTML = '';\n", "        dataTable['output_type'] = 'display_data';\n", "        await google.colab.output.renderOutput(dataTable, element);\n", "        const docLink = document.createElement('div');\n", "        docLink.innerHTML = docLinkHtml;\n", "        element.appendChild(docLink);\n", "      }\n", "    </script>\n", "  </div>\n", "\n", "\n", "<div id=\"df-1699844c-de4f-4ace-83c5-c2829bfd5c08\">\n", "  <button class=\"colab-df-quickchart\" onclick=\"quickchart('df-1699844c-de4f-4ace-83c5-c2829bfd5c08')\"\n", "            title=\"Suggest charts\"\n", "            style=\"display:none;\">\n", "\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "     width=\"24px\">\n", "    <g>\n", "        <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n", "    </g>\n", "</svg>\n", "  </button>\n", "\n", "<style>\n", "  .colab-df-quickchart {\n", "      --bg-color: #E8F0FE;\n", "      --fill-color: #1967D2;\n", "      --hover-bg-color: #E2EBFA;\n", "      --hover-fill-color: #174EA6;\n", "      --disabled-fill-color: #AAA;\n", "      --disabled-bg-color: #DDD;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart {\n", "      --bg-color: #3B4455;\n", "      --fill-color: #D2E3FC;\n", "      --hover-bg-color: #434B5C;\n", "      --hover-fill-color: #FFFFFF;\n", "      --disabled-bg-color: #3B4455;\n", "      --disabled-fill-color: #666;\n", "  }\n", "\n", "  .colab-df-quickchart {\n", "    background-color: var(--bg-color);\n", "    border: none;\n", "    border-radius: 50%;\n", "    cursor: pointer;\n", "    display: none;\n", "    fill: var(--fill-color);\n", "    height: 32px;\n", "    padding: 0;\n", "    width: 32px;\n", "  }\n", "\n", "  .colab-df-quickchart:hover {\n", "    background-color: var(--hover-bg-color);\n", "    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "    fill: var(--button-hover-fill-color);\n", "  }\n", "\n", "  .colab-df-quickchart-complete:disabled,\n", "  .colab-df-quickchart-complete:disabled:hover {\n", "    background-color: var(--disabled-bg-color);\n", "    fill: var(--disabled-fill-color);\n", "    box-shadow: none;\n", "  }\n", "\n", "  .colab-df-spinner {\n", "    border: 2px solid var(--fill-color);\n", "    border-color: transparent;\n", "    border-bottom-color: var(--fill-color);\n", "    animation:\n", "      spin 1s steps(1) infinite;\n", "  }\n", "\n", "  @keyframes spin {\n", "    0% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "      border-left-color: var(--fill-color);\n", "    }\n", "    20% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    30% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    40% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    60% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    80% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "    90% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "  }\n", "</style>\n", "\n", "  <script>\n", "    async function quickchart(key) {\n", "      const quickchartButtonEl =\n", "        document.querySelector('#' + key + ' button');\n", "      quickchartButtonEl.disabled = true;  // To prevent multiple clicks.\n", "      quickchartButtonEl.classList.add('colab-df-spinner');\n", "      try {\n", "        const charts = await google.colab.kernel.invokeFunction(\n", "            'suggest<PERSON><PERSON>s', [key], {});\n", "      } catch (error) {\n", "        console.error('Error during call to suggest<PERSON>harts:', error);\n", "      }\n", "      quickchartButtonEl.classList.remove('colab-df-spinner');\n", "      quickchartButtonEl.classList.add('colab-df-quickchart-complete');\n", "    }\n", "    (() => {\n", "      let quickchartButtonEl =\n", "        document.querySelector('#df-1699844c-de4f-4ace-83c5-c2829bfd5c08 button');\n", "      quickchartButtonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "    })();\n", "  </script>\n", "</div>\n", "\n", "  <div id=\"id_94a9beb2-b406-4165-a1e7-778932252260\">\n", "    <style>\n", "      .colab-df-generate {\n", "        background-color: #E8F0FE;\n", "        border: none;\n", "        border-radius: 50%;\n", "        cursor: pointer;\n", "        display: none;\n", "        fill: #1967D2;\n", "        height: 32px;\n", "        padding: 0 0 0 0;\n", "        width: 32px;\n", "      }\n", "\n", "      .colab-df-generate:hover {\n", "        background-color: #E2EBFA;\n", "        box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "        fill: #174EA6;\n", "      }\n", "\n", "      [theme=dark] .colab-df-generate {\n", "        background-color: #3B4455;\n", "        fill: #D2E3FC;\n", "      }\n", "\n", "      [theme=dark] .colab-df-generate:hover {\n", "        background-color: #434B5C;\n", "        box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "        filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "        fill: #FFFFFF;\n", "      }\n", "    </style>\n", "    <button class=\"colab-df-generate\" onclick=\"generateWithVariable('df')\"\n", "            title=\"Generate code using this dataframe.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "       width=\"24px\">\n", "    <path d=\"M7,19H8.4L18.45,9,17,7.55,7,17.6ZM5,21V16.75L18.45,3.32a2,2,0,0,1,2.83,0l1.4,1.43a1.91,1.91,0,0,1,.58,1.4,1.91,1.91,0,0,1-.58,1.4L9.25,21ZM18.45,9,17,7.55Zm-12,3A5.31,5.31,0,0,0,4.9,8.1,5.31,5.31,0,0,0,1,6.5,5.31,5.31,0,0,0,4.9,4.9,5.31,5.31,0,0,0,6.5,1,5.31,5.31,0,0,0,8.1,4.9,5.31,5.31,0,0,0,12,6.5,5.46,5.46,0,0,0,6.5,12Z\"/>\n", "  </svg>\n", "    </button>\n", "    <script>\n", "      (() => {\n", "      const buttonEl =\n", "        document.querySelector('#id_94a9beb2-b406-4165-a1e7-778932252260 button.colab-df-generate');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      buttonEl.onclick = () => {\n", "        google.colab.notebook.generateWithVariable('df');\n", "      }\n", "      })();\n", "    </script>\n", "  </div>\n", "\n", "    </div>\n", "  </div>\n"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "dataframe", "variable_name": "df", "summary": "{\n  \"name\": \"df\",\n  \"rows\": 3476,\n  \"fields\": [\n    {\n      \"column\": \"movieId\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 44138,\n        \"min\": 1,\n        \"max\": 193565,\n        \"num_unique_values\": 1464,\n        \"samples\": [\n          58295,\n          904,\n          1196\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"title\",\n      \"properties\": {\n        \"dtype\": \"category\",\n        \"num_unique_values\": 1464,\n        \"samples\": [\n          \"Bank Job, The (2008)\",\n          \"Rear Window (1954)\",\n          \"Star Wars: Episode V - The Empire Strikes Back (1980)\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"genres\",\n      \"properties\": {\n        \"dtype\": \"category\",\n        \"num_unique_values\": 364,\n        \"samples\": [\n          \"Action|Drama|Sci-Fi\",\n          \"Action|Crime|Drama|Thriller\",\n          \"Comedy|Drama|Thriller\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"userId\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 161,\n        \"min\": 2,\n        \"max\": 610,\n        \"num_unique_values\": 54,\n        \"samples\": [\n          18,\n          506,\n          487\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"rating\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0.8569253156474246,\n        \"min\": 0.5,\n        \"max\": 5.0,\n        \"num_unique_values\": 10,\n        \"samples\": [\n          2.0,\n          3.5,\n          5.0\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"tag\",\n      \"properties\": {\n        \"dtype\": \"category\",\n        \"num_unique_values\": 1543,\n        \"samples\": [\n          \"Gal Gadot\",\n          \"trippy\",\n          \"kung fu\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    }\n  ]\n}"}}, "metadata": {}, "execution_count": 11}], "source": ["df_1 = df\n", "df_1"]}, {"cell_type": "markdown", "metadata": {"id": "e488e327-1e0d-4498-821e-f9c9f4ab32a6"}, "source": ["Next, we will be calculating the number of votes and the average rating for each movie.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 424}, "id": "397e5125-65d8-4214-afa3-4caaedf48c88", "outputId": "45ca4d7a-ef29-4e6d-f1fd-c04e24a7803a"}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["      movieId                           title  \\\n", "0           1                Toy Story (1995)   \n", "1           1                Toy Story (1995)   \n", "2           1                Toy Story (1995)   \n", "3           2                  <PERSON><PERSON><PERSON> (1995)   \n", "4           2                  <PERSON><PERSON><PERSON> (1995)   \n", "...       ...                             ...   \n", "3471   187595  Solo: A Star Wars Story (2018)   \n", "3472   193565       <PERSON><PERSON><PERSON>: The Movie (2010)   \n", "3473   193565       <PERSON><PERSON><PERSON>: The Movie (2010)   \n", "3474   193565       <PERSON><PERSON><PERSON>: The Movie (2010)   \n", "3475   193565       <PERSON>int<PERSON>: The Movie (2010)   \n", "\n", "                                           genres  userId  rating  \\\n", "0     Adventure|Animation|Children|Comedy|Fantasy     336     4.0   \n", "1     Adventure|Animation|Children|Comedy|Fantasy     474     4.0   \n", "2     Adventure|Animation|Children|Comedy|Fantasy     567     3.5   \n", "3                      Adventure|Children|Fantasy      62     4.0   \n", "4                      Adventure|Children|Fantasy      62     4.0   \n", "...                                           ...     ...     ...   \n", "3471             Action|Adventure|Children|Sci-Fi      62     4.0   \n", "3472               Action|Animation|Comedy|Sci-Fi     184     3.5   \n", "3473               Action|Animation|Comedy|Sci-Fi     184     3.5   \n", "3474               Action|Animation|Comedy|Sci-Fi     184     3.5   \n", "3475               Action|Animation|Comedy|Sci-Fi     184     3.5   \n", "\n", "                   tag  numVotes  \n", "0                pixar         3  \n", "1                pixar         3  \n", "2                  fun         3  \n", "3              fantasy         4  \n", "4     magic board game         4  \n", "...                ...       ...  \n", "3471         star wars         2  \n", "3472             anime         4  \n", "3473            comedy         4  \n", "3474           gintama         4  \n", "3475          remaster         4  \n", "\n", "[3476 rows x 7 columns]"], "text/html": ["\n", "  <div id=\"df-0e9b425f-6d1f-4f0e-8f1e-4ff6b53db437\" class=\"colab-df-container\">\n", "    <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>movieId</th>\n", "      <th>title</th>\n", "      <th>genres</th>\n", "      <th>userId</th>\n", "      <th>rating</th>\n", "      <th>tag</th>\n", "      <th>numVotes</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>Toy Story (1995)</td>\n", "      <td>Adventure|Animation|Children|Comedy|Fantasy</td>\n", "      <td>336</td>\n", "      <td>4.0</td>\n", "      <td>pixar</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1</td>\n", "      <td>Toy Story (1995)</td>\n", "      <td>Adventure|Animation|Children|Comedy|Fantasy</td>\n", "      <td>474</td>\n", "      <td>4.0</td>\n", "      <td>pixar</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1</td>\n", "      <td>Toy Story (1995)</td>\n", "      <td>Adventure|Animation|Children|Comedy|Fantasy</td>\n", "      <td>567</td>\n", "      <td>3.5</td>\n", "      <td>fun</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2</td>\n", "      <td><PERSON><PERSON><PERSON> (1995)</td>\n", "      <td>Adventure|Children|Fantasy</td>\n", "      <td>62</td>\n", "      <td>4.0</td>\n", "      <td>fantasy</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2</td>\n", "      <td><PERSON><PERSON><PERSON> (1995)</td>\n", "      <td>Adventure|Children|Fantasy</td>\n", "      <td>62</td>\n", "      <td>4.0</td>\n", "      <td>magic board game</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3471</th>\n", "      <td>187595</td>\n", "      <td>Solo: A Star Wars Story (2018)</td>\n", "      <td>Action|Adventure|Children|Sci-Fi</td>\n", "      <td>62</td>\n", "      <td>4.0</td>\n", "      <td>star wars</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3472</th>\n", "      <td>193565</td>\n", "      <td><PERSON><PERSON><PERSON>: The Movie (2010)</td>\n", "      <td>Action|Animation|Comedy|Sci-Fi</td>\n", "      <td>184</td>\n", "      <td>3.5</td>\n", "      <td>anime</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3473</th>\n", "      <td>193565</td>\n", "      <td><PERSON><PERSON><PERSON>: The Movie (2010)</td>\n", "      <td>Action|Animation|Comedy|Sci-Fi</td>\n", "      <td>184</td>\n", "      <td>3.5</td>\n", "      <td>comedy</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3474</th>\n", "      <td>193565</td>\n", "      <td><PERSON><PERSON><PERSON>: The Movie (2010)</td>\n", "      <td>Action|Animation|Comedy|Sci-Fi</td>\n", "      <td>184</td>\n", "      <td>3.5</td>\n", "      <td>gintama</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3475</th>\n", "      <td>193565</td>\n", "      <td><PERSON><PERSON><PERSON>: The Movie (2010)</td>\n", "      <td>Action|Animation|Comedy|Sci-Fi</td>\n", "      <td>184</td>\n", "      <td>3.5</td>\n", "      <td>remaster</td>\n", "      <td>4</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>3476 rows × 7 columns</p>\n", "</div>\n", "    <div class=\"colab-df-buttons\">\n", "\n", "  <div class=\"colab-df-container\">\n", "    <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-0e9b425f-6d1f-4f0e-8f1e-4ff6b53db437')\"\n", "            title=\"Convert this dataframe to an interactive table.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\" viewBox=\"0 -960 960 960\">\n", "    <path d=\"M120-120v-720h720v720H120Zm60-500h600v-160H180v160Zm220 220h160v-160H400v160Zm0 220h160v-160H400v160ZM180-400h160v-160H180v160Zm440 0h160v-160H620v160ZM180-180h160v-160H180v160Zm440 0h160v-160H620v160Z\"/>\n", "  </svg>\n", "    </button>\n", "\n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    .colab-df-buttons div {\n", "      margin-bottom: 4px;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "    <script>\n", "      const buttonEl =\n", "        document.querySelector('#df-0e9b425f-6d1f-4f0e-8f1e-4ff6b53db437 button.colab-df-convert');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      async function convertToInteractive(key) {\n", "        const element = document.querySelector('#df-0e9b425f-6d1f-4f0e-8f1e-4ff6b53db437');\n", "        const dataTable =\n", "          await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                    [key], {});\n", "        if (!dataTable) return;\n", "\n", "        const docLinkHtml = 'Like what you see? Visit the ' +\n", "          '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "          + ' to learn more about interactive tables.';\n", "        element.innerHTML = '';\n", "        dataTable['output_type'] = 'display_data';\n", "        await google.colab.output.renderOutput(dataTable, element);\n", "        const docLink = document.createElement('div');\n", "        docLink.innerHTML = docLinkHtml;\n", "        element.appendChild(docLink);\n", "      }\n", "    </script>\n", "  </div>\n", "\n", "\n", "<div id=\"df-52ca3643-c513-4a4d-b4fc-711c49b765b7\">\n", "  <button class=\"colab-df-quickchart\" onclick=\"quickchart('df-52ca3643-c513-4a4d-b4fc-711c49b765b7')\"\n", "            title=\"Suggest charts\"\n", "            style=\"display:none;\">\n", "\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "     width=\"24px\">\n", "    <g>\n", "        <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n", "    </g>\n", "</svg>\n", "  </button>\n", "\n", "<style>\n", "  .colab-df-quickchart {\n", "      --bg-color: #E8F0FE;\n", "      --fill-color: #1967D2;\n", "      --hover-bg-color: #E2EBFA;\n", "      --hover-fill-color: #174EA6;\n", "      --disabled-fill-color: #AAA;\n", "      --disabled-bg-color: #DDD;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart {\n", "      --bg-color: #3B4455;\n", "      --fill-color: #D2E3FC;\n", "      --hover-bg-color: #434B5C;\n", "      --hover-fill-color: #FFFFFF;\n", "      --disabled-bg-color: #3B4455;\n", "      --disabled-fill-color: #666;\n", "  }\n", "\n", "  .colab-df-quickchart {\n", "    background-color: var(--bg-color);\n", "    border: none;\n", "    border-radius: 50%;\n", "    cursor: pointer;\n", "    display: none;\n", "    fill: var(--fill-color);\n", "    height: 32px;\n", "    padding: 0;\n", "    width: 32px;\n", "  }\n", "\n", "  .colab-df-quickchart:hover {\n", "    background-color: var(--hover-bg-color);\n", "    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "    fill: var(--button-hover-fill-color);\n", "  }\n", "\n", "  .colab-df-quickchart-complete:disabled,\n", "  .colab-df-quickchart-complete:disabled:hover {\n", "    background-color: var(--disabled-bg-color);\n", "    fill: var(--disabled-fill-color);\n", "    box-shadow: none;\n", "  }\n", "\n", "  .colab-df-spinner {\n", "    border: 2px solid var(--fill-color);\n", "    border-color: transparent;\n", "    border-bottom-color: var(--fill-color);\n", "    animation:\n", "      spin 1s steps(1) infinite;\n", "  }\n", "\n", "  @keyframes spin {\n", "    0% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "      border-left-color: var(--fill-color);\n", "    }\n", "    20% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    30% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    40% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    60% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    80% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "    90% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "  }\n", "</style>\n", "\n", "  <script>\n", "    async function quickchart(key) {\n", "      const quickchartButtonEl =\n", "        document.querySelector('#' + key + ' button');\n", "      quickchartButtonEl.disabled = true;  // To prevent multiple clicks.\n", "      quickchartButtonEl.classList.add('colab-df-spinner');\n", "      try {\n", "        const charts = await google.colab.kernel.invokeFunction(\n", "            'suggest<PERSON><PERSON>s', [key], {});\n", "      } catch (error) {\n", "        console.error('Error during call to suggest<PERSON>harts:', error);\n", "      }\n", "      quickchartButtonEl.classList.remove('colab-df-spinner');\n", "      quickchartButtonEl.classList.add('colab-df-quickchart-complete');\n", "    }\n", "    (() => {\n", "      let quickchartButtonEl =\n", "        document.querySelector('#df-52ca3643-c513-4a4d-b4fc-711c49b765b7 button');\n", "      quickchartButtonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "    })();\n", "  </script>\n", "</div>\n", "\n", "  <div id=\"id_932ce45b-edaa-4a5f-a46a-e82d87673f46\">\n", "    <style>\n", "      .colab-df-generate {\n", "        background-color: #E8F0FE;\n", "        border: none;\n", "        border-radius: 50%;\n", "        cursor: pointer;\n", "        display: none;\n", "        fill: #1967D2;\n", "        height: 32px;\n", "        padding: 0 0 0 0;\n", "        width: 32px;\n", "      }\n", "\n", "      .colab-df-generate:hover {\n", "        background-color: #E2EBFA;\n", "        box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "        fill: #174EA6;\n", "      }\n", "\n", "      [theme=dark] .colab-df-generate {\n", "        background-color: #3B4455;\n", "        fill: #D2E3FC;\n", "      }\n", "\n", "      [theme=dark] .colab-df-generate:hover {\n", "        background-color: #434B5C;\n", "        box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "        filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "        fill: #FFFFFF;\n", "      }\n", "    </style>\n", "    <button class=\"colab-df-generate\" onclick=\"generateWithVariable('df_1')\"\n", "            title=\"Generate code using this dataframe.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "       width=\"24px\">\n", "    <path d=\"M7,19H8.4L18.45,9,17,7.55,7,17.6ZM5,21V16.75L18.45,3.32a2,2,0,0,1,2.83,0l1.4,1.43a1.91,1.91,0,0,1,.58,1.4,1.91,1.91,0,0,1-.58,1.4L9.25,21ZM18.45,9,17,7.55Zm-12,3A5.31,5.31,0,0,0,4.9,8.1,5.31,5.31,0,0,0,1,6.5,5.31,5.31,0,0,0,4.9,4.9,5.31,5.31,0,0,0,6.5,1,5.31,5.31,0,0,0,8.1,4.9,5.31,5.31,0,0,0,12,6.5,5.46,5.46,0,0,0,6.5,12Z\"/>\n", "  </svg>\n", "    </button>\n", "    <script>\n", "      (() => {\n", "      const buttonEl =\n", "        document.querySelector('#id_932ce45b-edaa-4a5f-a46a-e82d87673f46 button.colab-df-generate');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      buttonEl.onclick = () => {\n", "        google.colab.notebook.generateWithVariable('df_1');\n", "      }\n", "      })();\n", "    </script>\n", "  </div>\n", "\n", "    </div>\n", "  </div>\n"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "dataframe", "variable_name": "df_1", "summary": "{\n  \"name\": \"df_1\",\n  \"rows\": 3476,\n  \"fields\": [\n    {\n      \"column\": \"movieId\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 44138,\n        \"min\": 1,\n        \"max\": 193565,\n        \"num_unique_values\": 1464,\n        \"samples\": [\n          58295,\n          904,\n          1196\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"title\",\n      \"properties\": {\n        \"dtype\": \"category\",\n        \"num_unique_values\": 1464,\n        \"samples\": [\n          \"Bank Job, The (2008)\",\n          \"Rear Window (1954)\",\n          \"Star Wars: Episode V - The Empire Strikes Back (1980)\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"genres\",\n      \"properties\": {\n        \"dtype\": \"category\",\n        \"num_unique_values\": 364,\n        \"samples\": [\n          \"Action|Drama|Sci-Fi\",\n          \"Action|Crime|Drama|Thriller\",\n          \"Comedy|Drama|Thriller\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"userId\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 161,\n        \"min\": 2,\n        \"max\": 610,\n        \"num_unique_values\": 54,\n        \"samples\": [\n          18,\n          506,\n          487\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"rating\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0.8569253156474246,\n        \"min\": 0.5,\n        \"max\": 5.0,\n        \"num_unique_values\": 10,\n        \"samples\": [\n          2.0,\n          3.5,\n          5.0\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"tag\",\n      \"properties\": {\n        \"dtype\": \"category\",\n        \"num_unique_values\": 1543,\n        \"samples\": [\n          \"Gal Gadot\",\n          \"trippy\",\n          \"kung fu\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"numVotes\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 39,\n        \"min\": 1,\n        \"max\": 181,\n        \"num_unique_values\": 26,\n        \"samples\": [\n          35,\n          32,\n          3\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    }\n  ]\n}"}}, "metadata": {}, "execution_count": 12}], "source": ["num_votes = df_1.groupby('movieId').size().reset_index(name='numVotes')\n", "\n", "# Merge the numVotes back into the original DataFrame\n", "df_1 = pd.merge(df_1, num_votes, on='movieId')\n", "\n", "df_1"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "a928cbc3-7aa9-4ba0-94d7-114bff846684"}, "outputs": [], "source": ["avg_ratings = df_1.groupby('movieId')['rating'].mean().reset_index(name='avgRating')\n", "\n", "# Merge the avgRating back into the original DataFrame\n", "df_1 = pd.merge(df_1, avg_ratings, on='movieId')"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 424}, "id": "591bf976-3471-4250-8249-d181771d240b", "outputId": "cac47b67-e4f1-467a-d76f-43464af77d2e"}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["      movieId                               title  \\\n", "0           1                    Toy Story (1995)   \n", "3           2                      <PERSON><PERSON><PERSON> (1995)   \n", "7           3             Grumpier Old Men (1995)   \n", "9           5  Father of the Bride Part II (1995)   \n", "11          7                      <PERSON> (1995)   \n", "...       ...                                 ...   \n", "3461   183611                   Game Night (2018)   \n", "3464   184471                  Tomb Raider (2018)   \n", "3467   187593                   Deadpool 2 (2018)   \n", "3470   187595      Solo: A Star Wars Story (2018)   \n", "3472   193565           <PERSON><PERSON><PERSON>: The Movie (2010)   \n", "\n", "                                           genres  userId  rating  \\\n", "0     Adventure|Animation|Children|Comedy|Fantasy     336     4.0   \n", "3                      Adventure|Children|Fantasy      62     4.0   \n", "7                                  Comedy|Romance     289     2.5   \n", "9                                          Comedy     474     1.5   \n", "11                                 Comedy|Romance     474     3.0   \n", "...                                           ...     ...     ...   \n", "3461                   Action|Comedy|Crime|Horror      62     4.0   \n", "3464                     Action|Adventure|Fantasy      62     3.5   \n", "3467                         Action|Comedy|Sci-Fi      62     4.0   \n", "3470             Action|Adventure|Children|Sci-Fi      62     4.0   \n", "3472               Action|Animation|Comedy|Sci-Fi     184     3.5   \n", "\n", "                tag  numVotes  avgRating  \n", "0             pixar         3   3.833333  \n", "3           fantasy         4   3.750000  \n", "7             moldy         2   2.500000  \n", "9         pregnancy         2   1.500000  \n", "11           remake         1   3.000000  \n", "...             ...       ...        ...  \n", "3461         Comedy         3   4.000000  \n", "3464      adventure         3   3.500000  \n", "3467    <PERSON>         3   4.000000  \n", "3470  <PERSON>         2   4.000000  \n", "3472          anime         4   3.500000  \n", "\n", "[1464 rows x 8 columns]"], "text/html": ["\n", "  <div id=\"df-3457ce73-2d9a-4691-b350-956306febc03\" class=\"colab-df-container\">\n", "    <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>movieId</th>\n", "      <th>title</th>\n", "      <th>genres</th>\n", "      <th>userId</th>\n", "      <th>rating</th>\n", "      <th>tag</th>\n", "      <th>numVotes</th>\n", "      <th>avgRating</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>Toy Story (1995)</td>\n", "      <td>Adventure|Animation|Children|Comedy|Fantasy</td>\n", "      <td>336</td>\n", "      <td>4.0</td>\n", "      <td>pixar</td>\n", "      <td>3</td>\n", "      <td>3.833333</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2</td>\n", "      <td><PERSON><PERSON><PERSON> (1995)</td>\n", "      <td>Adventure|Children|Fantasy</td>\n", "      <td>62</td>\n", "      <td>4.0</td>\n", "      <td>fantasy</td>\n", "      <td>4</td>\n", "      <td>3.750000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>3</td>\n", "      <td>Grumpier Old Men (1995)</td>\n", "      <td>Comedy|Romance</td>\n", "      <td>289</td>\n", "      <td>2.5</td>\n", "      <td>moldy</td>\n", "      <td>2</td>\n", "      <td>2.500000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>5</td>\n", "      <td>Father of the Bride Part II (1995)</td>\n", "      <td>Comedy</td>\n", "      <td>474</td>\n", "      <td>1.5</td>\n", "      <td>pregnancy</td>\n", "      <td>2</td>\n", "      <td>1.500000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>7</td>\n", "      <td><PERSON> (1995)</td>\n", "      <td>Comedy|Romance</td>\n", "      <td>474</td>\n", "      <td>3.0</td>\n", "      <td>remake</td>\n", "      <td>1</td>\n", "      <td>3.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3461</th>\n", "      <td>183611</td>\n", "      <td>Game Night (2018)</td>\n", "      <td>Action|Comedy|Crime|Horror</td>\n", "      <td>62</td>\n", "      <td>4.0</td>\n", "      <td>Comedy</td>\n", "      <td>3</td>\n", "      <td>4.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3464</th>\n", "      <td>184471</td>\n", "      <td><PERSON> Raider (2018)</td>\n", "      <td>Action|Adventure|Fantasy</td>\n", "      <td>62</td>\n", "      <td>3.5</td>\n", "      <td>adventure</td>\n", "      <td>3</td>\n", "      <td>3.500000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3467</th>\n", "      <td>187593</td>\n", "      <td>Deadpool 2 (2018)</td>\n", "      <td>Action|Comedy|Sci-Fi</td>\n", "      <td>62</td>\n", "      <td>4.0</td>\n", "      <td><PERSON></td>\n", "      <td>3</td>\n", "      <td>4.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3470</th>\n", "      <td>187595</td>\n", "      <td>Solo: A Star Wars Story (2018)</td>\n", "      <td>Action|Adventure|Children|Sci-Fi</td>\n", "      <td>62</td>\n", "      <td>4.0</td>\n", "      <td><PERSON></td>\n", "      <td>2</td>\n", "      <td>4.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3472</th>\n", "      <td>193565</td>\n", "      <td><PERSON><PERSON><PERSON>: The Movie (2010)</td>\n", "      <td>Action|Animation|Comedy|Sci-Fi</td>\n", "      <td>184</td>\n", "      <td>3.5</td>\n", "      <td>anime</td>\n", "      <td>4</td>\n", "      <td>3.500000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>1464 rows × 8 columns</p>\n", "</div>\n", "    <div class=\"colab-df-buttons\">\n", "\n", "  <div class=\"colab-df-container\">\n", "    <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-3457ce73-2d9a-4691-b350-956306febc03')\"\n", "            title=\"Convert this dataframe to an interactive table.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\" viewBox=\"0 -960 960 960\">\n", "    <path d=\"M120-120v-720h720v720H120Zm60-500h600v-160H180v160Zm220 220h160v-160H400v160Zm0 220h160v-160H400v160ZM180-400h160v-160H180v160Zm440 0h160v-160H620v160ZM180-180h160v-160H180v160Zm440 0h160v-160H620v160Z\"/>\n", "  </svg>\n", "    </button>\n", "\n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    .colab-df-buttons div {\n", "      margin-bottom: 4px;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "    <script>\n", "      const buttonEl =\n", "        document.querySelector('#df-3457ce73-2d9a-4691-b350-956306febc03 button.colab-df-convert');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      async function convertToInteractive(key) {\n", "        const element = document.querySelector('#df-3457ce73-2d9a-4691-b350-956306febc03');\n", "        const dataTable =\n", "          await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                    [key], {});\n", "        if (!dataTable) return;\n", "\n", "        const docLinkHtml = 'Like what you see? Visit the ' +\n", "          '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "          + ' to learn more about interactive tables.';\n", "        element.innerHTML = '';\n", "        dataTable['output_type'] = 'display_data';\n", "        await google.colab.output.renderOutput(dataTable, element);\n", "        const docLink = document.createElement('div');\n", "        docLink.innerHTML = docLinkHtml;\n", "        element.appendChild(docLink);\n", "      }\n", "    </script>\n", "  </div>\n", "\n", "\n", "<div id=\"df-a7fbf082-6960-4782-b9ea-6e6a78cd90f2\">\n", "  <button class=\"colab-df-quickchart\" onclick=\"quickchart('df-a7fbf082-6960-4782-b9ea-6e6a78cd90f2')\"\n", "            title=\"Suggest charts\"\n", "            style=\"display:none;\">\n", "\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "     width=\"24px\">\n", "    <g>\n", "        <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n", "    </g>\n", "</svg>\n", "  </button>\n", "\n", "<style>\n", "  .colab-df-quickchart {\n", "      --bg-color: #E8F0FE;\n", "      --fill-color: #1967D2;\n", "      --hover-bg-color: #E2EBFA;\n", "      --hover-fill-color: #174EA6;\n", "      --disabled-fill-color: #AAA;\n", "      --disabled-bg-color: #DDD;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart {\n", "      --bg-color: #3B4455;\n", "      --fill-color: #D2E3FC;\n", "      --hover-bg-color: #434B5C;\n", "      --hover-fill-color: #FFFFFF;\n", "      --disabled-bg-color: #3B4455;\n", "      --disabled-fill-color: #666;\n", "  }\n", "\n", "  .colab-df-quickchart {\n", "    background-color: var(--bg-color);\n", "    border: none;\n", "    border-radius: 50%;\n", "    cursor: pointer;\n", "    display: none;\n", "    fill: var(--fill-color);\n", "    height: 32px;\n", "    padding: 0;\n", "    width: 32px;\n", "  }\n", "\n", "  .colab-df-quickchart:hover {\n", "    background-color: var(--hover-bg-color);\n", "    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "    fill: var(--button-hover-fill-color);\n", "  }\n", "\n", "  .colab-df-quickchart-complete:disabled,\n", "  .colab-df-quickchart-complete:disabled:hover {\n", "    background-color: var(--disabled-bg-color);\n", "    fill: var(--disabled-fill-color);\n", "    box-shadow: none;\n", "  }\n", "\n", "  .colab-df-spinner {\n", "    border: 2px solid var(--fill-color);\n", "    border-color: transparent;\n", "    border-bottom-color: var(--fill-color);\n", "    animation:\n", "      spin 1s steps(1) infinite;\n", "  }\n", "\n", "  @keyframes spin {\n", "    0% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "      border-left-color: var(--fill-color);\n", "    }\n", "    20% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    30% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    40% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    60% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    80% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "    90% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "  }\n", "</style>\n", "\n", "  <script>\n", "    async function quickchart(key) {\n", "      const quickchartButtonEl =\n", "        document.querySelector('#' + key + ' button');\n", "      quickchartButtonEl.disabled = true;  // To prevent multiple clicks.\n", "      quickchartButtonEl.classList.add('colab-df-spinner');\n", "      try {\n", "        const charts = await google.colab.kernel.invokeFunction(\n", "            'suggest<PERSON><PERSON>s', [key], {});\n", "      } catch (error) {\n", "        console.error('Error during call to suggest<PERSON>harts:', error);\n", "      }\n", "      quickchartButtonEl.classList.remove('colab-df-spinner');\n", "      quickchartButtonEl.classList.add('colab-df-quickchart-complete');\n", "    }\n", "    (() => {\n", "      let quickchartButtonEl =\n", "        document.querySelector('#df-a7fbf082-6960-4782-b9ea-6e6a78cd90f2 button');\n", "      quickchartButtonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "    })();\n", "  </script>\n", "</div>\n", "\n", "  <div id=\"id_e4fd206f-a8bd-4f1e-9763-314ad43bd491\">\n", "    <style>\n", "      .colab-df-generate {\n", "        background-color: #E8F0FE;\n", "        border: none;\n", "        border-radius: 50%;\n", "        cursor: pointer;\n", "        display: none;\n", "        fill: #1967D2;\n", "        height: 32px;\n", "        padding: 0 0 0 0;\n", "        width: 32px;\n", "      }\n", "\n", "      .colab-df-generate:hover {\n", "        background-color: #E2EBFA;\n", "        box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "        fill: #174EA6;\n", "      }\n", "\n", "      [theme=dark] .colab-df-generate {\n", "        background-color: #3B4455;\n", "        fill: #D2E3FC;\n", "      }\n", "\n", "      [theme=dark] .colab-df-generate:hover {\n", "        background-color: #434B5C;\n", "        box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "        filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "        fill: #FFFFFF;\n", "      }\n", "    </style>\n", "    <button class=\"colab-df-generate\" onclick=\"generateWithVariable('df_1')\"\n", "            title=\"Generate code using this dataframe.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "       width=\"24px\">\n", "    <path d=\"M7,19H8.4L18.45,9,17,7.55,7,17.6ZM5,21V16.75L18.45,3.32a2,2,0,0,1,2.83,0l1.4,1.43a1.91,1.91,0,0,1,.58,1.4,1.91,1.91,0,0,1-.58,1.4L9.25,21ZM18.45,9,17,7.55Zm-12,3A5.31,5.31,0,0,0,4.9,8.1,5.31,5.31,0,0,0,1,6.5,5.31,5.31,0,0,0,4.9,4.9,5.31,5.31,0,0,0,6.5,1,5.31,5.31,0,0,0,8.1,4.9,5.31,5.31,0,0,0,12,6.5,5.46,5.46,0,0,0,6.5,12Z\"/>\n", "  </svg>\n", "    </button>\n", "    <script>\n", "      (() => {\n", "      const buttonEl =\n", "        document.querySelector('#id_e4fd206f-a8bd-4f1e-9763-314ad43bd491 button.colab-df-generate');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      buttonEl.onclick = () => {\n", "        google.colab.notebook.generateWithVariable('df_1');\n", "      }\n", "      })();\n", "    </script>\n", "  </div>\n", "\n", "    </div>\n", "  </div>\n"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "dataframe", "variable_name": "df_1", "summary": "{\n  \"name\": \"df_1\",\n  \"rows\": 1464,\n  \"fields\": [\n    {\n      \"column\": \"movieId\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 36036,\n        \"min\": 1,\n        \"max\": 193565,\n        \"num_unique_values\": 1464,\n        \"samples\": [\n          58295,\n          904,\n          1196\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"title\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 1464,\n        \"samples\": [\n          \"Bank Job, The (2008)\",\n          \"Rear Window (1954)\",\n          \"Star Wars: Episode V - The Empire Strikes Back (1980)\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"genres\",\n      \"properties\": {\n        \"dtype\": \"category\",\n        \"num_unique_values\": 364,\n        \"samples\": [\n          \"Action|Drama|Sci-Fi\",\n          \"Action|Crime|Drama|Thriller\",\n          \"Comedy|Drama|Thriller\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"userId\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 114,\n        \"min\": 2,\n        \"max\": 610,\n        \"num_unique_values\": 46,\n        \"samples\": [\n          419,\n          106,\n          439\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"rating\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0.869803121647186,\n        \"min\": 0.5,\n        \"max\": 5.0,\n        \"num_unique_values\": 10,\n        \"samples\": [\n          2.0,\n          2.5,\n          3.5\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"tag\",\n      \"properties\": {\n        \"dtype\": \"category\",\n        \"num_unique_values\": 727,\n        \"samples\": [\n          \"matchmaker\",\n          \"bloody\",\n          \"big top\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"numVotes\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 5,\n        \"min\": 1,\n        \"max\": 181,\n        \"num_unique_values\": 26,\n        \"samples\": [\n          35,\n          32,\n          3\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"avgRating\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0.****************,\n        \"min\": 0.5,\n        \"max\": 5.0,\n        \"num_unique_values\": 75,\n        \"samples\": [\n          3.0,\n          4.6,\n          4.***************\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    }\n  ]\n}"}}, "metadata": {}, "execution_count": 14}], "source": ["df_1.drop_duplicates(subset = ['movieId', 'title', 'avgRating', 'numVotes'], inplace = True)\n", "df_1"]}, {"cell_type": "markdown", "metadata": {"id": "546de87d-0fa6-4285-9735-cbe41a12cb4e"}, "source": ["We will be calculating the weighted score for each type. Usually, we would think that a good score results when the rating is high and the number of votes is also high. For instance, suppose you were browsing to choose a restaurant to dine at on your trip. If restaurant A had score 8.5 with 100,000 votes and restaurant B had score 8.5 but with 10 votes, we would be more convinced that restaurant A is more enjoyable and popular. Similarly, if restaurant C had score 5.0 with 1000 votes and restaurant D had score 5.0 with 1 vote, we may not automatically think that restaurant D was not enjoyable (but we do know that it is not popular), since only one person submitted a rating, if another person gave it score 10, this would immediately bump the score of restaurant D to 7.5.\n", "\n", "The code below creates a new column `df['score']` that calculates the weighted average score for each movie.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 242}, "id": "c33c170e-3860-47ae-b5d2-c279442f4db4", "outputId": "64fc0ece-c5c7-49a2-cd56-005b1468cc8c"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["The average rating across all movies is: 3.7323364168313313\n", "The average number of votes is: 2.3743169398907105\n"]}, {"output_type": "execute_result", "data": {"text/plain": ["    movieId                               title  avgRating  numVotes     score\n", "0         1                    <PERSON> Story (1995)   3.833333         3  3.788714\n", "3         2                      <PERSON><PERSON><PERSON> (1995)   3.750000         4  3.743421\n", "7         3             Grumpier Old Men (1995)   2.500000         2  3.168895\n", "9         5  Father of the Bride Part II (1995)   1.500000         2  2.711680\n", "11        7                      <PERSON> (1995)   3.000000         1  3.515304"], "text/html": ["\n", "  <div id=\"df-63deb2cd-a03f-45c0-98a0-4ef41e6441dc\" class=\"colab-df-container\">\n", "    <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>movieId</th>\n", "      <th>title</th>\n", "      <th>avgRating</th>\n", "      <th>numVotes</th>\n", "      <th>score</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>Toy Story (1995)</td>\n", "      <td>3.833333</td>\n", "      <td>3</td>\n", "      <td>3.788714</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2</td>\n", "      <td><PERSON><PERSON><PERSON> (1995)</td>\n", "      <td>3.750000</td>\n", "      <td>4</td>\n", "      <td>3.743421</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>3</td>\n", "      <td>Grumpier Old Men (1995)</td>\n", "      <td>2.500000</td>\n", "      <td>2</td>\n", "      <td>3.168895</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>5</td>\n", "      <td>Father of the Bride Part II (1995)</td>\n", "      <td>1.500000</td>\n", "      <td>2</td>\n", "      <td>2.711680</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>7</td>\n", "      <td><PERSON> (1995)</td>\n", "      <td>3.000000</td>\n", "      <td>1</td>\n", "      <td>3.515304</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>\n", "    <div class=\"colab-df-buttons\">\n", "\n", "  <div class=\"colab-df-container\">\n", "    <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-63deb2cd-a03f-45c0-98a0-4ef41e6441dc')\"\n", "            title=\"Convert this dataframe to an interactive table.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\" viewBox=\"0 -960 960 960\">\n", "    <path d=\"M120-120v-720h720v720H120Zm60-500h600v-160H180v160Zm220 220h160v-160H400v160Zm0 220h160v-160H400v160ZM180-400h160v-160H180v160Zm440 0h160v-160H620v160ZM180-180h160v-160H180v160Zm440 0h160v-160H620v160Z\"/>\n", "  </svg>\n", "    </button>\n", "\n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    .colab-df-buttons div {\n", "      margin-bottom: 4px;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "    <script>\n", "      const buttonEl =\n", "        document.querySelector('#df-63deb2cd-a03f-45c0-98a0-4ef41e6441dc button.colab-df-convert');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      async function convertToInteractive(key) {\n", "        const element = document.querySelector('#df-63deb2cd-a03f-45c0-98a0-4ef41e6441dc');\n", "        const dataTable =\n", "          await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                    [key], {});\n", "        if (!dataTable) return;\n", "\n", "        const docLinkHtml = 'Like what you see? Visit the ' +\n", "          '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "          + ' to learn more about interactive tables.';\n", "        element.innerHTML = '';\n", "        dataTable['output_type'] = 'display_data';\n", "        await google.colab.output.renderOutput(dataTable, element);\n", "        const docLink = document.createElement('div');\n", "        docLink.innerHTML = docLinkHtml;\n", "        element.appendChild(docLink);\n", "      }\n", "    </script>\n", "  </div>\n", "\n", "\n", "<div id=\"df-35cec4a9-86b3-41f3-acdb-8f576dbe4ab5\">\n", "  <button class=\"colab-df-quickchart\" onclick=\"quickchart('df-35cec4a9-86b3-41f3-acdb-8f576dbe4ab5')\"\n", "            title=\"Suggest charts\"\n", "            style=\"display:none;\">\n", "\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "     width=\"24px\">\n", "    <g>\n", "        <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n", "    </g>\n", "</svg>\n", "  </button>\n", "\n", "<style>\n", "  .colab-df-quickchart {\n", "      --bg-color: #E8F0FE;\n", "      --fill-color: #1967D2;\n", "      --hover-bg-color: #E2EBFA;\n", "      --hover-fill-color: #174EA6;\n", "      --disabled-fill-color: #AAA;\n", "      --disabled-bg-color: #DDD;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart {\n", "      --bg-color: #3B4455;\n", "      --fill-color: #D2E3FC;\n", "      --hover-bg-color: #434B5C;\n", "      --hover-fill-color: #FFFFFF;\n", "      --disabled-bg-color: #3B4455;\n", "      --disabled-fill-color: #666;\n", "  }\n", "\n", "  .colab-df-quickchart {\n", "    background-color: var(--bg-color);\n", "    border: none;\n", "    border-radius: 50%;\n", "    cursor: pointer;\n", "    display: none;\n", "    fill: var(--fill-color);\n", "    height: 32px;\n", "    padding: 0;\n", "    width: 32px;\n", "  }\n", "\n", "  .colab-df-quickchart:hover {\n", "    background-color: var(--hover-bg-color);\n", "    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "    fill: var(--button-hover-fill-color);\n", "  }\n", "\n", "  .colab-df-quickchart-complete:disabled,\n", "  .colab-df-quickchart-complete:disabled:hover {\n", "    background-color: var(--disabled-bg-color);\n", "    fill: var(--disabled-fill-color);\n", "    box-shadow: none;\n", "  }\n", "\n", "  .colab-df-spinner {\n", "    border: 2px solid var(--fill-color);\n", "    border-color: transparent;\n", "    border-bottom-color: var(--fill-color);\n", "    animation:\n", "      spin 1s steps(1) infinite;\n", "  }\n", "\n", "  @keyframes spin {\n", "    0% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "      border-left-color: var(--fill-color);\n", "    }\n", "    20% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    30% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    40% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    60% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    80% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "    90% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "  }\n", "</style>\n", "\n", "  <script>\n", "    async function quickchart(key) {\n", "      const quickchartButtonEl =\n", "        document.querySelector('#' + key + ' button');\n", "      quickchartButtonEl.disabled = true;  // To prevent multiple clicks.\n", "      quickchartButtonEl.classList.add('colab-df-spinner');\n", "      try {\n", "        const charts = await google.colab.kernel.invokeFunction(\n", "            'suggest<PERSON><PERSON>s', [key], {});\n", "      } catch (error) {\n", "        console.error('Error during call to suggest<PERSON>harts:', error);\n", "      }\n", "      quickchartButtonEl.classList.remove('colab-df-spinner');\n", "      quickchartButtonEl.classList.add('colab-df-quickchart-complete');\n", "    }\n", "    (() => {\n", "      let quickchartButtonEl =\n", "        document.querySelector('#df-35cec4a9-86b3-41f3-acdb-8f576dbe4ab5 button');\n", "      quickchartButtonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "    })();\n", "  </script>\n", "</div>\n", "\n", "    </div>\n", "  </div>\n"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "dataframe", "summary": "{\n  \"name\": \"df_1[['movieId', 'title', 'avgRating', 'numVotes', 'score']]\",\n  \"rows\": 5,\n  \"fields\": [\n    {\n      \"column\": \"movieId\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 2,\n        \"min\": 1,\n        \"max\": 7,\n        \"num_unique_values\": 5,\n        \"samples\": [\n          2,\n          7,\n          3\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"title\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 5,\n        \"samples\": [\n          \"<PERSON><PERSON><PERSON> (1995)\",\n          \"<PERSON> (1995)\",\n          \"Grumpier Old Men (1995)\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"avgRating\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0.9646530752325188,\n        \"min\": 1.5,\n        \"max\": 3.8333333333333335,\n        \"num_unique_values\": 5,\n        \"samples\": [\n          3.75,\n          3.0,\n          2.5\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"numVotes\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 1,\n        \"min\": 1,\n        \"max\": 4,\n        \"num_unique_values\": 4,\n        \"samples\": [\n          4,\n          1,\n          3\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"score\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0.4495799113239209,\n        \"min\": 2.711680416131435,\n        \"max\": 3.7887139533433793,\n        \"num_unique_values\": 5,\n        \"samples\": [\n          3.7434206370451895,\n          3.5153039240699813,\n          3.1688946572307475\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    }\n  ]\n}"}}, "metadata": {}, "execution_count": 15}], "source": ["import statistics\n", "\n", "# Define the function to calculate the weighted score\n", "def calculate_weighted_score(avgRating, num_votes, C, m):\n", "    return (num_votes * avgRating + m * C) / (num_votes + m)\n", "\n", "# Calculate the global average rating (C)\n", "average_rating = statistics.mean(df_1['avgRating'])\n", "print('The average rating across all movies is:', average_rating)\n", "\n", "# Calculate the average number of votes (m)\n", "avg_num_votes = statistics.mean(df_1['numVotes'])  # Use the average number of votes for threshold\n", "print('The average number of votes is:', avg_num_votes)\n", "\n", "# Create a new column 'score' for the weighted average rating using 'avgRating' and 'numVotes'\n", "df_1['score'] = df_1.apply(lambda row: calculate_weighted_score(row['avgRating'], row['numVotes'], average_rating, avg_num_votes), axis=1)\n", "\n", "# Display the DataFrame with the calculated weighted score\n", "df_1[['movieId', 'title', 'avgRating', 'numVotes', 'score']].head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 424}, "id": "53cd311e-caac-45c7-b551-672aa6e4b7e5", "outputId": "114cfeb1-f3e0-4fff-d3d2-0329c9110fe7"}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["      movieId                               title  \\\n", "0           1                    Toy Story (1995)   \n", "3           2                      <PERSON><PERSON><PERSON> (1995)   \n", "7           3             Grumpier Old Men (1995)   \n", "9           5  Father of the Bride Part II (1995)   \n", "11          7                      <PERSON> (1995)   \n", "...       ...                                 ...   \n", "3461   183611                   Game Night (2018)   \n", "3464   184471                  Tomb Raider (2018)   \n", "3467   187593                   Deadpool 2 (2018)   \n", "3470   187595      Solo: A Star Wars Story (2018)   \n", "3472   193565           <PERSON><PERSON><PERSON>: The Movie (2010)   \n", "\n", "                                           genres  userId  rating  \\\n", "0     Adventure|Animation|Children|Comedy|Fantasy     336     4.0   \n", "3                      Adventure|Children|Fantasy      62     4.0   \n", "7                                  Comedy|Romance     289     2.5   \n", "9                                          Comedy     474     1.5   \n", "11                                 Comedy|Romance     474     3.0   \n", "...                                           ...     ...     ...   \n", "3461                   Action|Comedy|Crime|Horror      62     4.0   \n", "3464                     Action|Adventure|Fantasy      62     3.5   \n", "3467                         Action|Comedy|Sci-Fi      62     4.0   \n", "3470             Action|Adventure|Children|Sci-Fi      62     4.0   \n", "3472               Action|Animation|Comedy|Sci-Fi     184     3.5   \n", "\n", "                tag  numVotes  avgRating     score  \n", "0             pixar         3   3.833333  3.788714  \n", "3           fantasy         4   3.750000  3.743421  \n", "7             moldy         2   2.500000  3.168895  \n", "9         pregnancy         2   1.500000  2.711680  \n", "11           remake         1   3.000000  3.515304  \n", "...             ...       ...        ...       ...  \n", "3461         Comedy         3   4.000000  3.881749  \n", "3464      adventure         3   3.500000  3.602644  \n", "3467    <PERSON>         3   4.000000  3.881749  \n", "3470  <PERSON>         2   4.000000  3.854716  \n", "3472          anime         4   3.500000  3.586541  \n", "\n", "[1464 rows x 9 columns]"], "text/html": ["\n", "  <div id=\"df-4fd995cf-6f1e-47c1-af5f-b12c7dd232c2\" class=\"colab-df-container\">\n", "    <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>movieId</th>\n", "      <th>title</th>\n", "      <th>genres</th>\n", "      <th>userId</th>\n", "      <th>rating</th>\n", "      <th>tag</th>\n", "      <th>numVotes</th>\n", "      <th>avgRating</th>\n", "      <th>score</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>Toy Story (1995)</td>\n", "      <td>Adventure|Animation|Children|Comedy|Fantasy</td>\n", "      <td>336</td>\n", "      <td>4.0</td>\n", "      <td>pixar</td>\n", "      <td>3</td>\n", "      <td>3.833333</td>\n", "      <td>3.788714</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2</td>\n", "      <td><PERSON><PERSON><PERSON> (1995)</td>\n", "      <td>Adventure|Children|Fantasy</td>\n", "      <td>62</td>\n", "      <td>4.0</td>\n", "      <td>fantasy</td>\n", "      <td>4</td>\n", "      <td>3.750000</td>\n", "      <td>3.743421</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>3</td>\n", "      <td>Grumpier Old Men (1995)</td>\n", "      <td>Comedy|Romance</td>\n", "      <td>289</td>\n", "      <td>2.5</td>\n", "      <td>moldy</td>\n", "      <td>2</td>\n", "      <td>2.500000</td>\n", "      <td>3.168895</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>5</td>\n", "      <td>Father of the Bride Part II (1995)</td>\n", "      <td>Comedy</td>\n", "      <td>474</td>\n", "      <td>1.5</td>\n", "      <td>pregnancy</td>\n", "      <td>2</td>\n", "      <td>1.500000</td>\n", "      <td>2.711680</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>7</td>\n", "      <td><PERSON> (1995)</td>\n", "      <td>Comedy|Romance</td>\n", "      <td>474</td>\n", "      <td>3.0</td>\n", "      <td>remake</td>\n", "      <td>1</td>\n", "      <td>3.000000</td>\n", "      <td>3.515304</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3461</th>\n", "      <td>183611</td>\n", "      <td>Game Night (2018)</td>\n", "      <td>Action|Comedy|Crime|Horror</td>\n", "      <td>62</td>\n", "      <td>4.0</td>\n", "      <td>Comedy</td>\n", "      <td>3</td>\n", "      <td>4.000000</td>\n", "      <td>3.881749</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3464</th>\n", "      <td>184471</td>\n", "      <td><PERSON> Raider (2018)</td>\n", "      <td>Action|Adventure|Fantasy</td>\n", "      <td>62</td>\n", "      <td>3.5</td>\n", "      <td>adventure</td>\n", "      <td>3</td>\n", "      <td>3.500000</td>\n", "      <td>3.602644</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3467</th>\n", "      <td>187593</td>\n", "      <td>Deadpool 2 (2018)</td>\n", "      <td>Action|Comedy|Sci-Fi</td>\n", "      <td>62</td>\n", "      <td>4.0</td>\n", "      <td><PERSON></td>\n", "      <td>3</td>\n", "      <td>4.000000</td>\n", "      <td>3.881749</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3470</th>\n", "      <td>187595</td>\n", "      <td>Solo: A Star Wars Story (2018)</td>\n", "      <td>Action|Adventure|Children|Sci-Fi</td>\n", "      <td>62</td>\n", "      <td>4.0</td>\n", "      <td><PERSON></td>\n", "      <td>2</td>\n", "      <td>4.000000</td>\n", "      <td>3.854716</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3472</th>\n", "      <td>193565</td>\n", "      <td><PERSON><PERSON><PERSON>: The Movie (2010)</td>\n", "      <td>Action|Animation|Comedy|Sci-Fi</td>\n", "      <td>184</td>\n", "      <td>3.5</td>\n", "      <td>anime</td>\n", "      <td>4</td>\n", "      <td>3.500000</td>\n", "      <td>3.586541</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>1464 rows × 9 columns</p>\n", "</div>\n", "    <div class=\"colab-df-buttons\">\n", "\n", "  <div class=\"colab-df-container\">\n", "    <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-4fd995cf-6f1e-47c1-af5f-b12c7dd232c2')\"\n", "            title=\"Convert this dataframe to an interactive table.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\" viewBox=\"0 -960 960 960\">\n", "    <path d=\"M120-120v-720h720v720H120Zm60-500h600v-160H180v160Zm220 220h160v-160H400v160Zm0 220h160v-160H400v160ZM180-400h160v-160H180v160Zm440 0h160v-160H620v160ZM180-180h160v-160H180v160Zm440 0h160v-160H620v160Z\"/>\n", "  </svg>\n", "    </button>\n", "\n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    .colab-df-buttons div {\n", "      margin-bottom: 4px;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "    <script>\n", "      const buttonEl =\n", "        document.querySelector('#df-4fd995cf-6f1e-47c1-af5f-b12c7dd232c2 button.colab-df-convert');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      async function convertToInteractive(key) {\n", "        const element = document.querySelector('#df-4fd995cf-6f1e-47c1-af5f-b12c7dd232c2');\n", "        const dataTable =\n", "          await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                    [key], {});\n", "        if (!dataTable) return;\n", "\n", "        const docLinkHtml = 'Like what you see? Visit the ' +\n", "          '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "          + ' to learn more about interactive tables.';\n", "        element.innerHTML = '';\n", "        dataTable['output_type'] = 'display_data';\n", "        await google.colab.output.renderOutput(dataTable, element);\n", "        const docLink = document.createElement('div');\n", "        docLink.innerHTML = docLinkHtml;\n", "        element.appendChild(docLink);\n", "      }\n", "    </script>\n", "  </div>\n", "\n", "\n", "<div id=\"df-66ec9942-7f23-43ca-939d-95af5956aece\">\n", "  <button class=\"colab-df-quickchart\" onclick=\"quickchart('df-66ec9942-7f23-43ca-939d-95af5956aece')\"\n", "            title=\"Suggest charts\"\n", "            style=\"display:none;\">\n", "\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "     width=\"24px\">\n", "    <g>\n", "        <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n", "    </g>\n", "</svg>\n", "  </button>\n", "\n", "<style>\n", "  .colab-df-quickchart {\n", "      --bg-color: #E8F0FE;\n", "      --fill-color: #1967D2;\n", "      --hover-bg-color: #E2EBFA;\n", "      --hover-fill-color: #174EA6;\n", "      --disabled-fill-color: #AAA;\n", "      --disabled-bg-color: #DDD;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart {\n", "      --bg-color: #3B4455;\n", "      --fill-color: #D2E3FC;\n", "      --hover-bg-color: #434B5C;\n", "      --hover-fill-color: #FFFFFF;\n", "      --disabled-bg-color: #3B4455;\n", "      --disabled-fill-color: #666;\n", "  }\n", "\n", "  .colab-df-quickchart {\n", "    background-color: var(--bg-color);\n", "    border: none;\n", "    border-radius: 50%;\n", "    cursor: pointer;\n", "    display: none;\n", "    fill: var(--fill-color);\n", "    height: 32px;\n", "    padding: 0;\n", "    width: 32px;\n", "  }\n", "\n", "  .colab-df-quickchart:hover {\n", "    background-color: var(--hover-bg-color);\n", "    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "    fill: var(--button-hover-fill-color);\n", "  }\n", "\n", "  .colab-df-quickchart-complete:disabled,\n", "  .colab-df-quickchart-complete:disabled:hover {\n", "    background-color: var(--disabled-bg-color);\n", "    fill: var(--disabled-fill-color);\n", "    box-shadow: none;\n", "  }\n", "\n", "  .colab-df-spinner {\n", "    border: 2px solid var(--fill-color);\n", "    border-color: transparent;\n", "    border-bottom-color: var(--fill-color);\n", "    animation:\n", "      spin 1s steps(1) infinite;\n", "  }\n", "\n", "  @keyframes spin {\n", "    0% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "      border-left-color: var(--fill-color);\n", "    }\n", "    20% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    30% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    40% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    60% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    80% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "    90% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "  }\n", "</style>\n", "\n", "  <script>\n", "    async function quickchart(key) {\n", "      const quickchartButtonEl =\n", "        document.querySelector('#' + key + ' button');\n", "      quickchartButtonEl.disabled = true;  // To prevent multiple clicks.\n", "      quickchartButtonEl.classList.add('colab-df-spinner');\n", "      try {\n", "        const charts = await google.colab.kernel.invokeFunction(\n", "            'suggest<PERSON><PERSON>s', [key], {});\n", "      } catch (error) {\n", "        console.error('Error during call to suggest<PERSON>harts:', error);\n", "      }\n", "      quickchartButtonEl.classList.remove('colab-df-spinner');\n", "      quickchartButtonEl.classList.add('colab-df-quickchart-complete');\n", "    }\n", "    (() => {\n", "      let quickchartButtonEl =\n", "        document.querySelector('#df-66ec9942-7f23-43ca-939d-95af5956aece button');\n", "      quickchartButtonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "    })();\n", "  </script>\n", "</div>\n", "\n", "  <div id=\"id_6bf8c7c3-f4d9-4f44-882e-5e0629151bf8\">\n", "    <style>\n", "      .colab-df-generate {\n", "        background-color: #E8F0FE;\n", "        border: none;\n", "        border-radius: 50%;\n", "        cursor: pointer;\n", "        display: none;\n", "        fill: #1967D2;\n", "        height: 32px;\n", "        padding: 0 0 0 0;\n", "        width: 32px;\n", "      }\n", "\n", "      .colab-df-generate:hover {\n", "        background-color: #E2EBFA;\n", "        box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "        fill: #174EA6;\n", "      }\n", "\n", "      [theme=dark] .colab-df-generate {\n", "        background-color: #3B4455;\n", "        fill: #D2E3FC;\n", "      }\n", "\n", "      [theme=dark] .colab-df-generate:hover {\n", "        background-color: #434B5C;\n", "        box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "        filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "        fill: #FFFFFF;\n", "      }\n", "    </style>\n", "    <button class=\"colab-df-generate\" onclick=\"generateWithVariable('df_1')\"\n", "            title=\"Generate code using this dataframe.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "       width=\"24px\">\n", "    <path d=\"M7,19H8.4L18.45,9,17,7.55,7,17.6ZM5,21V16.75L18.45,3.32a2,2,0,0,1,2.83,0l1.4,1.43a1.91,1.91,0,0,1,.58,1.4,1.91,1.91,0,0,1-.58,1.4L9.25,21ZM18.45,9,17,7.55Zm-12,3A5.31,5.31,0,0,0,4.9,8.1,5.31,5.31,0,0,0,1,6.5,5.31,5.31,0,0,0,4.9,4.9,5.31,5.31,0,0,0,6.5,1,5.31,5.31,0,0,0,8.1,4.9,5.31,5.31,0,0,0,12,6.5,5.46,5.46,0,0,0,6.5,12Z\"/>\n", "  </svg>\n", "    </button>\n", "    <script>\n", "      (() => {\n", "      const buttonEl =\n", "        document.querySelector('#id_6bf8c7c3-f4d9-4f44-882e-5e0629151bf8 button.colab-df-generate');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      buttonEl.onclick = () => {\n", "        google.colab.notebook.generateWithVariable('df_1');\n", "      }\n", "      })();\n", "    </script>\n", "  </div>\n", "\n", "    </div>\n", "  </div>\n"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "dataframe", "variable_name": "df_1", "summary": "{\n  \"name\": \"df_1\",\n  \"rows\": 1464,\n  \"fields\": [\n    {\n      \"column\": \"movieId\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 36036,\n        \"min\": 1,\n        \"max\": 193565,\n        \"num_unique_values\": 1464,\n        \"samples\": [\n          58295,\n          904,\n          1196\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"title\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 1464,\n        \"samples\": [\n          \"Bank Job, The (2008)\",\n          \"Rear Window (1954)\",\n          \"Star Wars: Episode V - The Empire Strikes Back (1980)\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"genres\",\n      \"properties\": {\n        \"dtype\": \"category\",\n        \"num_unique_values\": 364,\n        \"samples\": [\n          \"Action|Drama|Sci-Fi\",\n          \"Action|Crime|Drama|Thriller\",\n          \"Comedy|Drama|Thriller\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"userId\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 114,\n        \"min\": 2,\n        \"max\": 610,\n        \"num_unique_values\": 46,\n        \"samples\": [\n          419,\n          106,\n          439\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"rating\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0.869803121647186,\n        \"min\": 0.5,\n        \"max\": 5.0,\n        \"num_unique_values\": 10,\n        \"samples\": [\n          2.0,\n          2.5,\n          3.5\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"tag\",\n      \"properties\": {\n        \"dtype\": \"category\",\n        \"num_unique_values\": 727,\n        \"samples\": [\n          \"matchmaker\",\n          \"bloody\",\n          \"big top\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"numVotes\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 5,\n        \"min\": 1,\n        \"max\": 181,\n        \"num_unique_values\": 26,\n        \"samples\": [\n          35,\n          32,\n          3\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"avgRating\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0.****************,\n        \"min\": 0.5,\n        \"max\": 5.0,\n        \"num_unique_values\": 75,\n        \"samples\": [\n          3.0,\n          4.6,\n          4.***************\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"score\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0.3540052464502227,\n        \"min\": 1.8797333628108288,\n        \"max\": 4.967226407602271,\n        \"num_unique_values\": 144,\n        \"samples\": [\n          4.083329035087286,\n          3.416573638142566,\n          3.9387412025879445\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    }\n  ]\n}"}}, "metadata": {}, "execution_count": 16}], "source": ["df_1"]}, {"cell_type": "markdown", "metadata": {"id": "5d9a95d8-1702-4e22-aac3-8e1b31e43f35"}, "source": ["### <a id='toc1_5_1_'></a>[Exercise 1 - Get the top 5 suggestions sorting by score in descending order](#toc0_)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "35ffc068-2e03-4f95-bcdb-6b80c7db526b", "colab": {"base_uri": "https://localhost:8080/", "height": 206}, "outputId": "b720b36b-11b6-46b8-cb3d-ddb4376659fd"}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["      movieId                                              title  \\\n", "199       296                                Pulp Fiction (1994)   \n", "1337     2959                                  Fight Club (1999)   \n", "604       924                       2001: A Space Odyssey (1968)   \n", "998      1732                           <PERSON>, The (1998)   \n", "164       293  <PERSON>: The Professional (a.k.a. The Professiona...   \n", "\n", "                           genres  userId  rating            tag  numVotes  \\\n", "199   Comedy|Crime|Drama|Thriller     103     5.0  good dialogue       181   \n", "1337  Action|Crime|Drama|Thriller     424     4.5    dark comedy        54   \n", "604        Adventure|Drama|Sci-Fi     474     4.0            Hal        41   \n", "998                  Comedy|Crime     474     3.5  Coen Brothers        32   \n", "164   Action|Crime|Drama|Thriller     166     4.5       assassin        35   \n", "\n", "      avgRating     score  \n", "199    4.983425  4.967226  \n", "1337   4.944444  4.893394  \n", "604    4.951220  4.884498  \n", "998    4.953125  4.868802  \n", "164    4.928571  4.852577  "], "text/html": ["\n", "  <div id=\"df-a7ba42b4-fcd6-47e9-bedb-1dd490e489ac\" class=\"colab-df-container\">\n", "    <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>movieId</th>\n", "      <th>title</th>\n", "      <th>genres</th>\n", "      <th>userId</th>\n", "      <th>rating</th>\n", "      <th>tag</th>\n", "      <th>numVotes</th>\n", "      <th>avgRating</th>\n", "      <th>score</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>199</th>\n", "      <td>296</td>\n", "      <td>Pulp Fiction (1994)</td>\n", "      <td>Comedy|Crime|Drama|Thriller</td>\n", "      <td>103</td>\n", "      <td>5.0</td>\n", "      <td>good dialogue</td>\n", "      <td>181</td>\n", "      <td>4.983425</td>\n", "      <td>4.967226</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1337</th>\n", "      <td>2959</td>\n", "      <td>Fight Club (1999)</td>\n", "      <td>Action|Crime|Drama|Thriller</td>\n", "      <td>424</td>\n", "      <td>4.5</td>\n", "      <td>dark comedy</td>\n", "      <td>54</td>\n", "      <td>4.944444</td>\n", "      <td>4.893394</td>\n", "    </tr>\n", "    <tr>\n", "      <th>604</th>\n", "      <td>924</td>\n", "      <td>2001: A Space Odyssey (1968)</td>\n", "      <td>Adventure|Drama|Sci-Fi</td>\n", "      <td>474</td>\n", "      <td>4.0</td>\n", "      <td>Hal</td>\n", "      <td>41</td>\n", "      <td>4.951220</td>\n", "      <td>4.884498</td>\n", "    </tr>\n", "    <tr>\n", "      <th>998</th>\n", "      <td>1732</td>\n", "      <td><PERSON>, The (1998)</td>\n", "      <td>Comedy|Crime</td>\n", "      <td>474</td>\n", "      <td>3.5</td>\n", "      <td>Coen Brothers</td>\n", "      <td>32</td>\n", "      <td>4.953125</td>\n", "      <td>4.868802</td>\n", "    </tr>\n", "    <tr>\n", "      <th>164</th>\n", "      <td>293</td>\n", "      <td>Léon: The Professional (a.k.a. The Professiona...</td>\n", "      <td>Action|Crime|Drama|Thriller</td>\n", "      <td>166</td>\n", "      <td>4.5</td>\n", "      <td>assassin</td>\n", "      <td>35</td>\n", "      <td>4.928571</td>\n", "      <td>4.852577</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>\n", "    <div class=\"colab-df-buttons\">\n", "\n", "  <div class=\"colab-df-container\">\n", "    <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-a7ba42b4-fcd6-47e9-bedb-1dd490e489ac')\"\n", "            title=\"Convert this dataframe to an interactive table.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\" viewBox=\"0 -960 960 960\">\n", "    <path d=\"M120-120v-720h720v720H120Zm60-500h600v-160H180v160Zm220 220h160v-160H400v160Zm0 220h160v-160H400v160ZM180-400h160v-160H180v160Zm440 0h160v-160H620v160ZM180-180h160v-160H180v160Zm440 0h160v-160H620v160Z\"/>\n", "  </svg>\n", "    </button>\n", "\n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    .colab-df-buttons div {\n", "      margin-bottom: 4px;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "    <script>\n", "      const buttonEl =\n", "        document.querySelector('#df-a7ba42b4-fcd6-47e9-bedb-1dd490e489ac button.colab-df-convert');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      async function convertToInteractive(key) {\n", "        const element = document.querySelector('#df-a7ba42b4-fcd6-47e9-bedb-1dd490e489ac');\n", "        const dataTable =\n", "          await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                    [key], {});\n", "        if (!dataTable) return;\n", "\n", "        const docLinkHtml = 'Like what you see? Visit the ' +\n", "          '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "          + ' to learn more about interactive tables.';\n", "        element.innerHTML = '';\n", "        dataTable['output_type'] = 'display_data';\n", "        await google.colab.output.renderOutput(dataTable, element);\n", "        const docLink = document.createElement('div');\n", "        docLink.innerHTML = docLinkHtml;\n", "        element.appendChild(docLink);\n", "      }\n", "    </script>\n", "  </div>\n", "\n", "\n", "<div id=\"df-9e7d49bf-fb52-4a2f-991a-5426ba264d25\">\n", "  <button class=\"colab-df-quickchart\" onclick=\"quickchart('df-9e7d49bf-fb52-4a2f-991a-5426ba264d25')\"\n", "            title=\"Suggest charts\"\n", "            style=\"display:none;\">\n", "\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "     width=\"24px\">\n", "    <g>\n", "        <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n", "    </g>\n", "</svg>\n", "  </button>\n", "\n", "<style>\n", "  .colab-df-quickchart {\n", "      --bg-color: #E8F0FE;\n", "      --fill-color: #1967D2;\n", "      --hover-bg-color: #E2EBFA;\n", "      --hover-fill-color: #174EA6;\n", "      --disabled-fill-color: #AAA;\n", "      --disabled-bg-color: #DDD;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart {\n", "      --bg-color: #3B4455;\n", "      --fill-color: #D2E3FC;\n", "      --hover-bg-color: #434B5C;\n", "      --hover-fill-color: #FFFFFF;\n", "      --disabled-bg-color: #3B4455;\n", "      --disabled-fill-color: #666;\n", "  }\n", "\n", "  .colab-df-quickchart {\n", "    background-color: var(--bg-color);\n", "    border: none;\n", "    border-radius: 50%;\n", "    cursor: pointer;\n", "    display: none;\n", "    fill: var(--fill-color);\n", "    height: 32px;\n", "    padding: 0;\n", "    width: 32px;\n", "  }\n", "\n", "  .colab-df-quickchart:hover {\n", "    background-color: var(--hover-bg-color);\n", "    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "    fill: var(--button-hover-fill-color);\n", "  }\n", "\n", "  .colab-df-quickchart-complete:disabled,\n", "  .colab-df-quickchart-complete:disabled:hover {\n", "    background-color: var(--disabled-bg-color);\n", "    fill: var(--disabled-fill-color);\n", "    box-shadow: none;\n", "  }\n", "\n", "  .colab-df-spinner {\n", "    border: 2px solid var(--fill-color);\n", "    border-color: transparent;\n", "    border-bottom-color: var(--fill-color);\n", "    animation:\n", "      spin 1s steps(1) infinite;\n", "  }\n", "\n", "  @keyframes spin {\n", "    0% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "      border-left-color: var(--fill-color);\n", "    }\n", "    20% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    30% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    40% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    60% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    80% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "    90% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "  }\n", "</style>\n", "\n", "  <script>\n", "    async function quickchart(key) {\n", "      const quickchartButtonEl =\n", "        document.querySelector('#' + key + ' button');\n", "      quickchartButtonEl.disabled = true;  // To prevent multiple clicks.\n", "      quickchartButtonEl.classList.add('colab-df-spinner');\n", "      try {\n", "        const charts = await google.colab.kernel.invokeFunction(\n", "            'suggest<PERSON><PERSON>s', [key], {});\n", "      } catch (error) {\n", "        console.error('Error during call to suggest<PERSON>harts:', error);\n", "      }\n", "      quickchartButtonEl.classList.remove('colab-df-spinner');\n", "      quickchartButtonEl.classList.add('colab-df-quickchart-complete');\n", "    }\n", "    (() => {\n", "      let quickchartButtonEl =\n", "        document.querySelector('#df-9e7d49bf-fb52-4a2f-991a-5426ba264d25 button');\n", "      quickchartButtonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "    })();\n", "  </script>\n", "</div>\n", "\n", "  <div id=\"id_d19f73ce-b5f1-449d-96f5-b5962269b4bb\">\n", "    <style>\n", "      .colab-df-generate {\n", "        background-color: #E8F0FE;\n", "        border: none;\n", "        border-radius: 50%;\n", "        cursor: pointer;\n", "        display: none;\n", "        fill: #1967D2;\n", "        height: 32px;\n", "        padding: 0 0 0 0;\n", "        width: 32px;\n", "      }\n", "\n", "      .colab-df-generate:hover {\n", "        background-color: #E2EBFA;\n", "        box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "        fill: #174EA6;\n", "      }\n", "\n", "      [theme=dark] .colab-df-generate {\n", "        background-color: #3B4455;\n", "        fill: #D2E3FC;\n", "      }\n", "\n", "      [theme=dark] .colab-df-generate:hover {\n", "        background-color: #434B5C;\n", "        box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "        filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "        fill: #FFFFFF;\n", "      }\n", "    </style>\n", "    <button class=\"colab-df-generate\" onclick=\"generateWithVariable('top_recommendations')\"\n", "            title=\"Generate code using this dataframe.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "       width=\"24px\">\n", "    <path d=\"M7,19H8.4L18.45,9,17,7.55,7,17.6ZM5,21V16.75L18.45,3.32a2,2,0,0,1,2.83,0l1.4,1.43a1.91,1.91,0,0,1,.58,1.4,1.91,1.91,0,0,1-.58,1.4L9.25,21ZM18.45,9,17,7.55Zm-12,3A5.31,5.31,0,0,0,4.9,8.1,5.31,5.31,0,0,0,1,6.5,5.31,5.31,0,0,0,4.9,4.9,5.31,5.31,0,0,0,6.5,1,5.31,5.31,0,0,0,8.1,4.9,5.31,5.31,0,0,0,12,6.5,5.46,5.46,0,0,0,6.5,12Z\"/>\n", "  </svg>\n", "    </button>\n", "    <script>\n", "      (() => {\n", "      const buttonEl =\n", "        document.querySelector('#id_d19f73ce-b5f1-449d-96f5-b5962269b4bb button.colab-df-generate');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      buttonEl.onclick = () => {\n", "        google.colab.notebook.generateWithVariable('top_recommendations');\n", "      }\n", "      })();\n", "    </script>\n", "  </div>\n", "\n", "    </div>\n", "  </div>\n"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "dataframe", "variable_name": "top_recommendations", "summary": "{\n  \"name\": \"top_recommendations\",\n  \"rows\": 5,\n  \"fields\": [\n    {\n      \"column\": \"movieId\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 1127,\n        \"min\": 293,\n        \"max\": 2959,\n        \"num_unique_values\": 5,\n        \"samples\": [\n          2959,\n          293,\n          924\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"title\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 5,\n        \"samples\": [\n          \"Fight Club (1999)\",\n          \"L\\u00e9on: The Professional (a.k.a. The Professional) (L\\u00e9on) (1994)\",\n          \"2001: A Space Odyssey (1968)\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"genres\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 4,\n        \"samples\": [\n          \"Action|Crime|Drama|Thriller\",\n          \"Comedy|Crime\",\n          \"Comedy|Crime|Drama|Thriller\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"userId\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 179,\n        \"min\": 103,\n        \"max\": 474,\n        \"num_unique_values\": 4,\n        \"samples\": [\n          424,\n          166,\n          103\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"rating\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0.5700877125495689,\n        \"min\": 3.5,\n        \"max\": 5.0,\n        \"num_unique_values\": 4,\n        \"samples\": [\n          4.5,\n          3.5,\n          5.0\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"tag\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 5,\n        \"samples\": [\n          \"dark comedy\",\n          \"assassin\",\n          \"Hal\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"numVotes\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 63,\n        \"min\": 32,\n        \"max\": 181,\n        \"num_unique_values\": 5,\n        \"samples\": [\n          54,\n          35,\n          41\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"avgRating\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0.019970555431341085,\n        \"min\": 4.928571428571429,\n        \"max\": 4.983425414364641,\n        \"num_unique_values\": 5,\n        \"samples\": [\n          4.944444444444445,\n          4.928571428571429,\n          4.951219512195122\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"score\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0.04416195149131791,\n        \"min\": 4.852576968069774,\n        \"max\": 4.967226407602271,\n        \"num_unique_values\": 5,\n        \"samples\": [\n          4.893394094228974,\n          4.852576968069774,\n          4.884497659604815\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    }\n  ]\n}"}}, "metadata": {}, "execution_count": 18}], "source": ["# TODO: filtering out the top 5 suggestions\n", "# You can use `sort_values` to sort the DataFrame by the 'score' column in descending order\n", "\n", "top_recommendations = df_1.sort_values(by='score', ascending=False).head(5)\n", "top_recommendations\n"]}, {"cell_type": "markdown", "metadata": {"id": "717be405-8b31-45fb-9d6e-19b298a7fd5c"}, "source": ["## <a id='toc1_6_'></a>[Content-based recommendation](#toc0_)\n", "\n", "Content-based filtering focuses on the attributes of items and the user's profile. It recommends movies to users based on features that closely match the user's profile. Movie A could be recommended because it matches the user's preferred genre, cast, and keywords. However, we might get limited diversity as it may not recommend items outside the user's known preferences, potentially limiting discovery of new types of items.\n", "\n", "We want to compute the cosine similarity based on a number of features. Next, we will be creating a column `features` to gather the columns that we want to recommend to users. Calculation will be based on the type, genres, origin country, language, plot, summary, and cast.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 424}, "id": "0c59c602-5c80-41af-ad97-ca3370cea091", "outputId": "3bb17393-ad53-4f34-9db0-fcd93bda3739"}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["      movieId                               title  userId  avgRating  \\\n", "0           1                    Toy Story (1995)     336   3.833333   \n", "1           2                      <PERSON><PERSON><PERSON> (1995)      62   3.750000   \n", "2           3             Grumpier Old Men (1995)     289   2.500000   \n", "3           5  Father of the Bride Part II (1995)     474   1.500000   \n", "4           7                      <PERSON> (1995)     474   3.000000   \n", "...       ...                                 ...     ...        ...   \n", "1459   183611                   Game Night (2018)      62   4.000000   \n", "1460   184471                  Tomb Raider (2018)      62   3.500000   \n", "1461   187593                   Deadpool 2 (2018)      62   4.000000   \n", "1462   187595      Solo: A Star Wars Story (2018)      62   4.000000   \n", "1463   193565           <PERSON><PERSON><PERSON>: The Movie (2010)     184   3.500000   \n", "\n", "      numVotes     score                                       genres  \\\n", "0            3  3.788714  Adventure|Animation|Children|Comedy|Fantasy   \n", "1            4  3.743421                   Adventure|Children|Fantasy   \n", "2            2  3.168895                               Comedy|Romance   \n", "3            2  2.711680                                       Comedy   \n", "4            1  3.515304                               Comedy|Romance   \n", "...        ...       ...                                          ...   \n", "1459         3  3.881749                   Action|Comedy|Crime|Horror   \n", "1460         3  3.602644                     Action|Adventure|Fantasy   \n", "1461         3  3.881749                         Action|Comedy|Sci-Fi   \n", "1462         2  3.854716             Action|Adventure|Children|Sci-Fi   \n", "1463         4  3.586541               Action|Animation|Comedy|Sci-Fi   \n", "\n", "                tag  \n", "0             pixar  \n", "1           fantasy  \n", "2             moldy  \n", "3         pregnancy  \n", "4            remake  \n", "...             ...  \n", "1459         Comedy  \n", "1460      adventure  \n", "1461    <PERSON>  \n", "1462  <PERSON>  \n", "1463          anime  \n", "\n", "[1464 rows x 8 columns]"], "text/html": ["\n", "  <div id=\"df-58b42c3b-eb5c-4759-8f65-62d8e99235ae\" class=\"colab-df-container\">\n", "    <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>movieId</th>\n", "      <th>title</th>\n", "      <th>userId</th>\n", "      <th>avgRating</th>\n", "      <th>numVotes</th>\n", "      <th>score</th>\n", "      <th>genres</th>\n", "      <th>tag</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>Toy Story (1995)</td>\n", "      <td>336</td>\n", "      <td>3.833333</td>\n", "      <td>3</td>\n", "      <td>3.788714</td>\n", "      <td>Adventure|Animation|Children|Comedy|Fantasy</td>\n", "      <td>pixar</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td><PERSON><PERSON><PERSON> (1995)</td>\n", "      <td>62</td>\n", "      <td>3.750000</td>\n", "      <td>4</td>\n", "      <td>3.743421</td>\n", "      <td>Adventure|Children|Fantasy</td>\n", "      <td>fantasy</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>Grumpier Old Men (1995)</td>\n", "      <td>289</td>\n", "      <td>2.500000</td>\n", "      <td>2</td>\n", "      <td>3.168895</td>\n", "      <td>Comedy|Romance</td>\n", "      <td>moldy</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>5</td>\n", "      <td>Father of the Bride Part II (1995)</td>\n", "      <td>474</td>\n", "      <td>1.500000</td>\n", "      <td>2</td>\n", "      <td>2.711680</td>\n", "      <td>Comedy</td>\n", "      <td>pregnancy</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>7</td>\n", "      <td><PERSON> (1995)</td>\n", "      <td>474</td>\n", "      <td>3.000000</td>\n", "      <td>1</td>\n", "      <td>3.515304</td>\n", "      <td>Comedy|Romance</td>\n", "      <td>remake</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1459</th>\n", "      <td>183611</td>\n", "      <td>Game Night (2018)</td>\n", "      <td>62</td>\n", "      <td>4.000000</td>\n", "      <td>3</td>\n", "      <td>3.881749</td>\n", "      <td>Action|Comedy|Crime|Horror</td>\n", "      <td>Comedy</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1460</th>\n", "      <td>184471</td>\n", "      <td><PERSON> Raider (2018)</td>\n", "      <td>62</td>\n", "      <td>3.500000</td>\n", "      <td>3</td>\n", "      <td>3.602644</td>\n", "      <td>Action|Adventure|Fantasy</td>\n", "      <td>adventure</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1461</th>\n", "      <td>187593</td>\n", "      <td>Deadpool 2 (2018)</td>\n", "      <td>62</td>\n", "      <td>4.000000</td>\n", "      <td>3</td>\n", "      <td>3.881749</td>\n", "      <td>Action|Comedy|Sci-Fi</td>\n", "      <td><PERSON></td>\n", "    </tr>\n", "    <tr>\n", "      <th>1462</th>\n", "      <td>187595</td>\n", "      <td>Solo: A Star Wars Story (2018)</td>\n", "      <td>62</td>\n", "      <td>4.000000</td>\n", "      <td>2</td>\n", "      <td>3.854716</td>\n", "      <td>Action|Adventure|Children|Sci-Fi</td>\n", "      <td><PERSON></td>\n", "    </tr>\n", "    <tr>\n", "      <th>1463</th>\n", "      <td>193565</td>\n", "      <td><PERSON><PERSON><PERSON>: The Movie (2010)</td>\n", "      <td>184</td>\n", "      <td>3.500000</td>\n", "      <td>4</td>\n", "      <td>3.586541</td>\n", "      <td>Action|Animation|Comedy|Sci-Fi</td>\n", "      <td>anime</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>1464 rows × 8 columns</p>\n", "</div>\n", "    <div class=\"colab-df-buttons\">\n", "\n", "  <div class=\"colab-df-container\">\n", "    <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-58b42c3b-eb5c-4759-8f65-62d8e99235ae')\"\n", "            title=\"Convert this dataframe to an interactive table.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\" viewBox=\"0 -960 960 960\">\n", "    <path d=\"M120-120v-720h720v720H120Zm60-500h600v-160H180v160Zm220 220h160v-160H400v160Zm0 220h160v-160H400v160ZM180-400h160v-160H180v160Zm440 0h160v-160H620v160ZM180-180h160v-160H180v160Zm440 0h160v-160H620v160Z\"/>\n", "  </svg>\n", "    </button>\n", "\n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    .colab-df-buttons div {\n", "      margin-bottom: 4px;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "    <script>\n", "      const buttonEl =\n", "        document.querySelector('#df-58b42c3b-eb5c-4759-8f65-62d8e99235ae button.colab-df-convert');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      async function convertToInteractive(key) {\n", "        const element = document.querySelector('#df-58b42c3b-eb5c-4759-8f65-62d8e99235ae');\n", "        const dataTable =\n", "          await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                    [key], {});\n", "        if (!dataTable) return;\n", "\n", "        const docLinkHtml = 'Like what you see? Visit the ' +\n", "          '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "          + ' to learn more about interactive tables.';\n", "        element.innerHTML = '';\n", "        dataTable['output_type'] = 'display_data';\n", "        await google.colab.output.renderOutput(dataTable, element);\n", "        const docLink = document.createElement('div');\n", "        docLink.innerHTML = docLinkHtml;\n", "        element.appendChild(docLink);\n", "      }\n", "    </script>\n", "  </div>\n", "\n", "\n", "<div id=\"df-e5ea45e9-6b2a-4066-86b5-7b44216d2a29\">\n", "  <button class=\"colab-df-quickchart\" onclick=\"quickchart('df-e5ea45e9-6b2a-4066-86b5-7b44216d2a29')\"\n", "            title=\"Suggest charts\"\n", "            style=\"display:none;\">\n", "\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "     width=\"24px\">\n", "    <g>\n", "        <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n", "    </g>\n", "</svg>\n", "  </button>\n", "\n", "<style>\n", "  .colab-df-quickchart {\n", "      --bg-color: #E8F0FE;\n", "      --fill-color: #1967D2;\n", "      --hover-bg-color: #E2EBFA;\n", "      --hover-fill-color: #174EA6;\n", "      --disabled-fill-color: #AAA;\n", "      --disabled-bg-color: #DDD;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart {\n", "      --bg-color: #3B4455;\n", "      --fill-color: #D2E3FC;\n", "      --hover-bg-color: #434B5C;\n", "      --hover-fill-color: #FFFFFF;\n", "      --disabled-bg-color: #3B4455;\n", "      --disabled-fill-color: #666;\n", "  }\n", "\n", "  .colab-df-quickchart {\n", "    background-color: var(--bg-color);\n", "    border: none;\n", "    border-radius: 50%;\n", "    cursor: pointer;\n", "    display: none;\n", "    fill: var(--fill-color);\n", "    height: 32px;\n", "    padding: 0;\n", "    width: 32px;\n", "  }\n", "\n", "  .colab-df-quickchart:hover {\n", "    background-color: var(--hover-bg-color);\n", "    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "    fill: var(--button-hover-fill-color);\n", "  }\n", "\n", "  .colab-df-quickchart-complete:disabled,\n", "  .colab-df-quickchart-complete:disabled:hover {\n", "    background-color: var(--disabled-bg-color);\n", "    fill: var(--disabled-fill-color);\n", "    box-shadow: none;\n", "  }\n", "\n", "  .colab-df-spinner {\n", "    border: 2px solid var(--fill-color);\n", "    border-color: transparent;\n", "    border-bottom-color: var(--fill-color);\n", "    animation:\n", "      spin 1s steps(1) infinite;\n", "  }\n", "\n", "  @keyframes spin {\n", "    0% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "      border-left-color: var(--fill-color);\n", "    }\n", "    20% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    30% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    40% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    60% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    80% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "    90% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "  }\n", "</style>\n", "\n", "  <script>\n", "    async function quickchart(key) {\n", "      const quickchartButtonEl =\n", "        document.querySelector('#' + key + ' button');\n", "      quickchartButtonEl.disabled = true;  // To prevent multiple clicks.\n", "      quickchartButtonEl.classList.add('colab-df-spinner');\n", "      try {\n", "        const charts = await google.colab.kernel.invokeFunction(\n", "            'suggest<PERSON><PERSON>s', [key], {});\n", "      } catch (error) {\n", "        console.error('Error during call to suggest<PERSON>harts:', error);\n", "      }\n", "      quickchartButtonEl.classList.remove('colab-df-spinner');\n", "      quickchartButtonEl.classList.add('colab-df-quickchart-complete');\n", "    }\n", "    (() => {\n", "      let quickchartButtonEl =\n", "        document.querySelector('#df-e5ea45e9-6b2a-4066-86b5-7b44216d2a29 button');\n", "      quickchartButtonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "    })();\n", "  </script>\n", "</div>\n", "\n", "  <div id=\"id_46466a9b-4417-4e82-bf9f-e1ed58082f5c\">\n", "    <style>\n", "      .colab-df-generate {\n", "        background-color: #E8F0FE;\n", "        border: none;\n", "        border-radius: 50%;\n", "        cursor: pointer;\n", "        display: none;\n", "        fill: #1967D2;\n", "        height: 32px;\n", "        padding: 0 0 0 0;\n", "        width: 32px;\n", "      }\n", "\n", "      .colab-df-generate:hover {\n", "        background-color: #E2EBFA;\n", "        box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "        fill: #174EA6;\n", "      }\n", "\n", "      [theme=dark] .colab-df-generate {\n", "        background-color: #3B4455;\n", "        fill: #D2E3FC;\n", "      }\n", "\n", "      [theme=dark] .colab-df-generate:hover {\n", "        background-color: #434B5C;\n", "        box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "        filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "        fill: #FFFFFF;\n", "      }\n", "    </style>\n", "    <button class=\"colab-df-generate\" onclick=\"generateWithVariable('df_2')\"\n", "            title=\"Generate code using this dataframe.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "       width=\"24px\">\n", "    <path d=\"M7,19H8.4L18.45,9,17,7.55,7,17.6ZM5,21V16.75L18.45,3.32a2,2,0,0,1,2.83,0l1.4,1.43a1.91,1.91,0,0,1,.58,1.4,1.91,1.91,0,0,1-.58,1.4L9.25,21ZM18.45,9,17,7.55Zm-12,3A5.31,5.31,0,0,0,4.9,8.1,5.31,5.31,0,0,0,1,6.5,5.31,5.31,0,0,0,4.9,4.9,5.31,5.31,0,0,0,6.5,1,5.31,5.31,0,0,0,8.1,4.9,5.31,5.31,0,0,0,12,6.5,5.46,5.46,0,0,0,6.5,12Z\"/>\n", "  </svg>\n", "    </button>\n", "    <script>\n", "      (() => {\n", "      const buttonEl =\n", "        document.querySelector('#id_46466a9b-4417-4e82-bf9f-e1ed58082f5c button.colab-df-generate');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      buttonEl.onclick = () => {\n", "        google.colab.notebook.generateWithVariable('df_2');\n", "      }\n", "      })();\n", "    </script>\n", "  </div>\n", "\n", "    </div>\n", "  </div>\n"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "dataframe", "variable_name": "df_2", "summary": "{\n  \"name\": \"df_2\",\n  \"rows\": 1464,\n  \"fields\": [\n    {\n      \"column\": \"movieId\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 36036,\n        \"min\": 1,\n        \"max\": 193565,\n        \"num_unique_values\": 1464,\n        \"samples\": [\n          58295,\n          904,\n          1196\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"title\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 1464,\n        \"samples\": [\n          \"Bank Job, The (2008)\",\n          \"Rear Window (1954)\",\n          \"Star Wars: Episode V - The Empire Strikes Back (1980)\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"userId\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 114,\n        \"min\": 2,\n        \"max\": 610,\n        \"num_unique_values\": 46,\n        \"samples\": [\n          419,\n          106,\n          439\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"avgRating\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0.****************,\n        \"min\": 0.5,\n        \"max\": 5.0,\n        \"num_unique_values\": 75,\n        \"samples\": [\n          3.0,\n          4.6,\n          4.***************\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"numVotes\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 5,\n        \"min\": 1,\n        \"max\": 181,\n        \"num_unique_values\": 26,\n        \"samples\": [\n          35,\n          32,\n          3\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"score\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0.3540052464502227,\n        \"min\": 1.8797333628108288,\n        \"max\": 4.967226407602271,\n        \"num_unique_values\": 144,\n        \"samples\": [\n          4.083329035087286,\n          3.416573638142566,\n          3.9387412025879445\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"genres\",\n      \"properties\": {\n        \"dtype\": \"category\",\n        \"num_unique_values\": 364,\n        \"samples\": [\n          \"Action|Drama|Sci-Fi\",\n          \"Action|Crime|Drama|Thriller\",\n          \"Comedy|Drama|Thriller\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"tag\",\n      \"properties\": {\n        \"dtype\": \"category\",\n        \"num_unique_values\": 727,\n        \"samples\": [\n          \"matchmaker\",\n          \"bloody\",\n          \"big top\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    }\n  ]\n}"}}, "metadata": {}, "execution_count": 19}], "source": ["# We will now create a new DataFrame that contains only the columns we need for our analysis.\n", "df_2 = df_1[['movieId', 'title', 'userId', 'avgRating', 'numVotes', 'score', 'genres', 'tag']].copy()\n", "df_2.reset_index(drop=True, inplace=True)\n", "df_2"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 476}, "id": "c37c1af5-4870-4340-9d67-8b5b2db4153f", "outputId": "029c8f0a-961a-4a9d-8c33-18d10abf82e2"}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["      movieId                               title  userId  avgRating  \\\n", "0           1                    Toy Story (1995)     336   3.833333   \n", "1           2                      <PERSON><PERSON><PERSON> (1995)      62   3.750000   \n", "2           3             Grumpier Old Men (1995)     289   2.500000   \n", "3           5  Father of the Bride Part II (1995)     474   1.500000   \n", "4           7                      <PERSON> (1995)     474   3.000000   \n", "...       ...                                 ...     ...        ...   \n", "1459   183611                   Game Night (2018)      62   4.000000   \n", "1460   184471                  Tomb Raider (2018)      62   3.500000   \n", "1461   187593                   Deadpool 2 (2018)      62   4.000000   \n", "1462   187595      Solo: A Star Wars Story (2018)      62   4.000000   \n", "1463   193565           <PERSON><PERSON><PERSON>: The Movie (2010)     184   3.500000   \n", "\n", "      numVotes     score                                       genres  \\\n", "0            3  3.788714  Adventure|Animation|Children|Comedy|Fantasy   \n", "1            4  3.743421                   Adventure|Children|Fantasy   \n", "2            2  3.168895                               Comedy|Romance   \n", "3            2  2.711680                                       Comedy   \n", "4            1  3.515304                               Comedy|Romance   \n", "...        ...       ...                                          ...   \n", "1459         3  3.881749                   Action|Comedy|Crime|Horror   \n", "1460         3  3.602644                     Action|Adventure|Fantasy   \n", "1461         3  3.881749                         Action|Comedy|Sci-Fi   \n", "1462         2  3.854716             Action|Adventure|Children|Sci-Fi   \n", "1463         4  3.586541               Action|Animation|Comedy|Sci-Fi   \n", "\n", "                tag                                           features  \n", "0             pixar  Adventure Animation Children Comedy Fantasy pixar  \n", "1           fantasy                 Adventure Children Fantasy fantasy  \n", "2             moldy                               Comedy Romance moldy  \n", "3         pregnancy                                   Comedy pregnancy  \n", "4            remake                              Comedy Romance remake  \n", "...             ...                                                ...  \n", "1459         Comedy                  Action Comedy Crime Horror Comedy  \n", "1460      adventure                 Action Adventure Fantasy adventure  \n", "1461    <PERSON> Comedy Sci-Fi <PERSON>  \n", "1462  Emilia <PERSON>     Action Adventure Children Sci-<PERSON> <PERSON>  \n", "1463          anime               Action Animation Comedy Sci-Fi anime  \n", "\n", "[1464 rows x 9 columns]"], "text/html": ["\n", "  <div id=\"df-ebf7d044-2325-4ef8-a714-b2ad40284438\" class=\"colab-df-container\">\n", "    <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>movieId</th>\n", "      <th>title</th>\n", "      <th>userId</th>\n", "      <th>avgRating</th>\n", "      <th>numVotes</th>\n", "      <th>score</th>\n", "      <th>genres</th>\n", "      <th>tag</th>\n", "      <th>features</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>Toy Story (1995)</td>\n", "      <td>336</td>\n", "      <td>3.833333</td>\n", "      <td>3</td>\n", "      <td>3.788714</td>\n", "      <td>Adventure|Animation|Children|Comedy|Fantasy</td>\n", "      <td>pixar</td>\n", "      <td>Adventure Animation Children Comedy Fantasy pixar</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td><PERSON><PERSON><PERSON> (1995)</td>\n", "      <td>62</td>\n", "      <td>3.750000</td>\n", "      <td>4</td>\n", "      <td>3.743421</td>\n", "      <td>Adventure|Children|Fantasy</td>\n", "      <td>fantasy</td>\n", "      <td>Adventure Children Fantasy fantasy</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>Grumpier Old Men (1995)</td>\n", "      <td>289</td>\n", "      <td>2.500000</td>\n", "      <td>2</td>\n", "      <td>3.168895</td>\n", "      <td>Comedy|Romance</td>\n", "      <td>moldy</td>\n", "      <td>Comedy Romance moldy</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>5</td>\n", "      <td>Father of the Bride Part II (1995)</td>\n", "      <td>474</td>\n", "      <td>1.500000</td>\n", "      <td>2</td>\n", "      <td>2.711680</td>\n", "      <td>Comedy</td>\n", "      <td>pregnancy</td>\n", "      <td>Comedy pregnancy</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>7</td>\n", "      <td><PERSON> (1995)</td>\n", "      <td>474</td>\n", "      <td>3.000000</td>\n", "      <td>1</td>\n", "      <td>3.515304</td>\n", "      <td>Comedy|Romance</td>\n", "      <td>remake</td>\n", "      <td>Comedy Romance remake</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1459</th>\n", "      <td>183611</td>\n", "      <td>Game Night (2018)</td>\n", "      <td>62</td>\n", "      <td>4.000000</td>\n", "      <td>3</td>\n", "      <td>3.881749</td>\n", "      <td>Action|Comedy|Crime|Horror</td>\n", "      <td>Comedy</td>\n", "      <td>Action Comedy Crime Horror Comedy</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1460</th>\n", "      <td>184471</td>\n", "      <td><PERSON> Raider (2018)</td>\n", "      <td>62</td>\n", "      <td>3.500000</td>\n", "      <td>3</td>\n", "      <td>3.602644</td>\n", "      <td>Action|Adventure|Fantasy</td>\n", "      <td>adventure</td>\n", "      <td>Action Adventure Fantasy adventure</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1461</th>\n", "      <td>187593</td>\n", "      <td>Deadpool 2 (2018)</td>\n", "      <td>62</td>\n", "      <td>4.000000</td>\n", "      <td>3</td>\n", "      <td>3.881749</td>\n", "      <td>Action|Comedy|Sci-Fi</td>\n", "      <td><PERSON></td>\n", "      <td>Action Comedy Sci-Fi <PERSON></td>\n", "    </tr>\n", "    <tr>\n", "      <th>1462</th>\n", "      <td>187595</td>\n", "      <td>Solo: A Star Wars Story (2018)</td>\n", "      <td>62</td>\n", "      <td>4.000000</td>\n", "      <td>2</td>\n", "      <td>3.854716</td>\n", "      <td>Action|Adventure|Children|Sci-Fi</td>\n", "      <td><PERSON></td>\n", "      <td>Action Adventure Children Sci-Fi <PERSON></td>\n", "    </tr>\n", "    <tr>\n", "      <th>1463</th>\n", "      <td>193565</td>\n", "      <td><PERSON><PERSON><PERSON>: The Movie (2010)</td>\n", "      <td>184</td>\n", "      <td>3.500000</td>\n", "      <td>4</td>\n", "      <td>3.586541</td>\n", "      <td>Action|Animation|Comedy|Sci-Fi</td>\n", "      <td>anime</td>\n", "      <td>Action Animation Comedy Sci-Fi anime</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>1464 rows × 9 columns</p>\n", "</div>\n", "    <div class=\"colab-df-buttons\">\n", "\n", "  <div class=\"colab-df-container\">\n", "    <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-ebf7d044-2325-4ef8-a714-b2ad40284438')\"\n", "            title=\"Convert this dataframe to an interactive table.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\" viewBox=\"0 -960 960 960\">\n", "    <path d=\"M120-120v-720h720v720H120Zm60-500h600v-160H180v160Zm220 220h160v-160H400v160Zm0 220h160v-160H400v160ZM180-400h160v-160H180v160Zm440 0h160v-160H620v160ZM180-180h160v-160H180v160Zm440 0h160v-160H620v160Z\"/>\n", "  </svg>\n", "    </button>\n", "\n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    .colab-df-buttons div {\n", "      margin-bottom: 4px;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "    <script>\n", "      const buttonEl =\n", "        document.querySelector('#df-ebf7d044-2325-4ef8-a714-b2ad40284438 button.colab-df-convert');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      async function convertToInteractive(key) {\n", "        const element = document.querySelector('#df-ebf7d044-2325-4ef8-a714-b2ad40284438');\n", "        const dataTable =\n", "          await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                    [key], {});\n", "        if (!dataTable) return;\n", "\n", "        const docLinkHtml = 'Like what you see? Visit the ' +\n", "          '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "          + ' to learn more about interactive tables.';\n", "        element.innerHTML = '';\n", "        dataTable['output_type'] = 'display_data';\n", "        await google.colab.output.renderOutput(dataTable, element);\n", "        const docLink = document.createElement('div');\n", "        docLink.innerHTML = docLinkHtml;\n", "        element.appendChild(docLink);\n", "      }\n", "    </script>\n", "  </div>\n", "\n", "\n", "<div id=\"df-d36eeed3-607c-46b2-96cd-f12a024193e6\">\n", "  <button class=\"colab-df-quickchart\" onclick=\"quickchart('df-d36eeed3-607c-46b2-96cd-f12a024193e6')\"\n", "            title=\"Suggest charts\"\n", "            style=\"display:none;\">\n", "\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "     width=\"24px\">\n", "    <g>\n", "        <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n", "    </g>\n", "</svg>\n", "  </button>\n", "\n", "<style>\n", "  .colab-df-quickchart {\n", "      --bg-color: #E8F0FE;\n", "      --fill-color: #1967D2;\n", "      --hover-bg-color: #E2EBFA;\n", "      --hover-fill-color: #174EA6;\n", "      --disabled-fill-color: #AAA;\n", "      --disabled-bg-color: #DDD;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart {\n", "      --bg-color: #3B4455;\n", "      --fill-color: #D2E3FC;\n", "      --hover-bg-color: #434B5C;\n", "      --hover-fill-color: #FFFFFF;\n", "      --disabled-bg-color: #3B4455;\n", "      --disabled-fill-color: #666;\n", "  }\n", "\n", "  .colab-df-quickchart {\n", "    background-color: var(--bg-color);\n", "    border: none;\n", "    border-radius: 50%;\n", "    cursor: pointer;\n", "    display: none;\n", "    fill: var(--fill-color);\n", "    height: 32px;\n", "    padding: 0;\n", "    width: 32px;\n", "  }\n", "\n", "  .colab-df-quickchart:hover {\n", "    background-color: var(--hover-bg-color);\n", "    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "    fill: var(--button-hover-fill-color);\n", "  }\n", "\n", "  .colab-df-quickchart-complete:disabled,\n", "  .colab-df-quickchart-complete:disabled:hover {\n", "    background-color: var(--disabled-bg-color);\n", "    fill: var(--disabled-fill-color);\n", "    box-shadow: none;\n", "  }\n", "\n", "  .colab-df-spinner {\n", "    border: 2px solid var(--fill-color);\n", "    border-color: transparent;\n", "    border-bottom-color: var(--fill-color);\n", "    animation:\n", "      spin 1s steps(1) infinite;\n", "  }\n", "\n", "  @keyframes spin {\n", "    0% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "      border-left-color: var(--fill-color);\n", "    }\n", "    20% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    30% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    40% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    60% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    80% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "    90% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "  }\n", "</style>\n", "\n", "  <script>\n", "    async function quickchart(key) {\n", "      const quickchartButtonEl =\n", "        document.querySelector('#' + key + ' button');\n", "      quickchartButtonEl.disabled = true;  // To prevent multiple clicks.\n", "      quickchartButtonEl.classList.add('colab-df-spinner');\n", "      try {\n", "        const charts = await google.colab.kernel.invokeFunction(\n", "            'suggest<PERSON><PERSON>s', [key], {});\n", "      } catch (error) {\n", "        console.error('Error during call to suggest<PERSON>harts:', error);\n", "      }\n", "      quickchartButtonEl.classList.remove('colab-df-spinner');\n", "      quickchartButtonEl.classList.add('colab-df-quickchart-complete');\n", "    }\n", "    (() => {\n", "      let quickchartButtonEl =\n", "        document.querySelector('#df-d36eeed3-607c-46b2-96cd-f12a024193e6 button');\n", "      quickchartButtonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "    })();\n", "  </script>\n", "</div>\n", "\n", "  <div id=\"id_2f43c2dc-c0b4-4f9a-82a1-753fee0ea65c\">\n", "    <style>\n", "      .colab-df-generate {\n", "        background-color: #E8F0FE;\n", "        border: none;\n", "        border-radius: 50%;\n", "        cursor: pointer;\n", "        display: none;\n", "        fill: #1967D2;\n", "        height: 32px;\n", "        padding: 0 0 0 0;\n", "        width: 32px;\n", "      }\n", "\n", "      .colab-df-generate:hover {\n", "        background-color: #E2EBFA;\n", "        box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "        fill: #174EA6;\n", "      }\n", "\n", "      [theme=dark] .colab-df-generate {\n", "        background-color: #3B4455;\n", "        fill: #D2E3FC;\n", "      }\n", "\n", "      [theme=dark] .colab-df-generate:hover {\n", "        background-color: #434B5C;\n", "        box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "        filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "        fill: #FFFFFF;\n", "      }\n", "    </style>\n", "    <button class=\"colab-df-generate\" onclick=\"generateWithVariable('df_2')\"\n", "            title=\"Generate code using this dataframe.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "       width=\"24px\">\n", "    <path d=\"M7,19H8.4L18.45,9,17,7.55,7,17.6ZM5,21V16.75L18.45,3.32a2,2,0,0,1,2.83,0l1.4,1.43a1.91,1.91,0,0,1,.58,1.4,1.91,1.91,0,0,1-.58,1.4L9.25,21ZM18.45,9,17,7.55Zm-12,3A5.31,5.31,0,0,0,4.9,8.1,5.31,5.31,0,0,0,1,6.5,5.31,5.31,0,0,0,4.9,4.9,5.31,5.31,0,0,0,6.5,1,5.31,5.31,0,0,0,8.1,4.9,5.31,5.31,0,0,0,12,6.5,5.46,5.46,0,0,0,6.5,12Z\"/>\n", "  </svg>\n", "    </button>\n", "    <script>\n", "      (() => {\n", "      const buttonEl =\n", "        document.querySelector('#id_2f43c2dc-c0b4-4f9a-82a1-753fee0ea65c button.colab-df-generate');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      buttonEl.onclick = () => {\n", "        google.colab.notebook.generateWithVariable('df_2');\n", "      }\n", "      })();\n", "    </script>\n", "  </div>\n", "\n", "    </div>\n", "  </div>\n"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "dataframe", "variable_name": "df_2", "summary": "{\n  \"name\": \"df_2\",\n  \"rows\": 1464,\n  \"fields\": [\n    {\n      \"column\": \"movieId\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 36036,\n        \"min\": 1,\n        \"max\": 193565,\n        \"num_unique_values\": 1464,\n        \"samples\": [\n          58295,\n          904,\n          1196\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"title\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 1464,\n        \"samples\": [\n          \"Bank Job, The (2008)\",\n          \"Rear Window (1954)\",\n          \"Star Wars: Episode V - The Empire Strikes Back (1980)\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"userId\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 114,\n        \"min\": 2,\n        \"max\": 610,\n        \"num_unique_values\": 46,\n        \"samples\": [\n          419,\n          106,\n          439\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"avgRating\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0.****************,\n        \"min\": 0.5,\n        \"max\": 5.0,\n        \"num_unique_values\": 75,\n        \"samples\": [\n          3.0,\n          4.6,\n          4.***************\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"numVotes\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 5,\n        \"min\": 1,\n        \"max\": 181,\n        \"num_unique_values\": 26,\n        \"samples\": [\n          35,\n          32,\n          3\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"score\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0.3540052464502227,\n        \"min\": 1.8797333628108288,\n        \"max\": 4.967226407602271,\n        \"num_unique_values\": 144,\n        \"samples\": [\n          4.083329035087286,\n          3.416573638142566,\n          3.9387412025879445\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"genres\",\n      \"properties\": {\n        \"dtype\": \"category\",\n        \"num_unique_values\": 364,\n        \"samples\": [\n          \"Action|Drama|Sci-Fi\",\n          \"Action|Crime|Drama|Thriller\",\n          \"Comedy|Drama|Thriller\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"tag\",\n      \"properties\": {\n        \"dtype\": \"category\",\n        \"num_unique_values\": 727,\n        \"samples\": [\n          \"matchmaker\",\n          \"bloody\",\n          \"big top\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"features\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 1286,\n        \"samples\": [\n          \"Action Comedy Fantasy Sci-Fi funny\",\n          \"Drama religion\",\n          \"Drama Sci-Fi Thriller alternate reality\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    }\n  ]\n}"}}, "metadata": {}, "execution_count": 20}], "source": ["# Replace '|' with spaces in 'genres' and combine it with 'tag' using a space\n", "df_2['features'] = df_2['genres'].str.replace('|', ' ') + ' ' + df_2['tag'].fillna('')\n", "\n", "df_2"]}, {"cell_type": "markdown", "metadata": {"id": "480299d7-c68b-44dd-8dc4-8967ccf7aae9"}, "source": ["Next, let's vectorize the features column using TF-IDF vectorizer.\n", "The Term Frequency-Inverse Document Frequency(TF-IDF) vectorizer is used to transform text into numerical representations. It evaluates the importance of a word in a document relative to a collection of documents by considering both its frequency within a specific document (TF) and its rarity across all documents (IDF).\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "287286c6-0eba-4d16-b64f-6105f82c2e4c"}, "outputs": [], "source": ["from sklearn.feature_extraction.text import TfidfVectorizer\n", "\n", "vectorizer = TfidfVectorizer(stop_words='english')\n", "\n", "# Fit and transform the 'features' column to create TF-IDF vectors\n", "X = vectorizer.fit_transform(df_2['features'])"]}, {"cell_type": "markdown", "metadata": {"id": "7fdf9d32-18cc-4d36-91cd-1e6e7db714e4"}, "source": ["Finally, let's get the cosine similarity and recommend items based on users' needs.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "8461d2f2-4fbd-4c15-a58e-4ef0024240a1", "outputId": "b222d154-a1c8-4b5c-899d-ede37ce79b71"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Movies similar to 'Toy Story (1995)' (First movie is itself):\n", "0. <PERSON> Story (1995) (Similarity Score: 1.000)\n", "   Genres: Adventure|Animation|Children|Comedy|Fantasy\n", "   Tag: pixar\n", "\n", "1. <PERSON><PERSON>'s Life, A (1998) (Similarity Score: 0.939)\n", "   Genres: Adventure|Animation|Children|Comedy\n", "   Tag: Pi<PERSON>r\n", "\n", "2. Toy Story 2 (1999) (Similarity Score: 0.675)\n", "   Genres: Adventure|Animation|Children|Comedy|Fantasy\n", "   Tag: animation\n", "\n", "3. <PERSON><PERSON> (2010) (Similarity Score: 0.583)\n", "   Genres: Animation|Fantasy\n", "   Tag: adventure\n", "\n"]}], "source": ["from sklearn.metrics.pairwise import cosine_similarity\n", "\n", "# Calculate Cosine Similarity\n", "similarity = cosine_similarity(X)\n", "\n", "# Recommendation function (including itself as first result)\n", "def recommendation(title, df, similarity, top_n=3):\n", "    try:\n", "        # Get the index of the movie that matches the title\n", "        idx = df[df['title'] == title].index[0]\n", "    except IndexError:\n", "        print(f\"Movie '{title}' not found in the dataset.\")\n", "        return\n", "\n", "    # Get the similarity scores for the given movie\n", "    sim_scores = list(enumerate(similarity[idx]))\n", "\n", "    # Sort the movies based on similarity scores in descending order\n", "    sim_scores = sorted(sim_scores, key=lambda x: x[1], reverse=True)\n", "\n", "    # Print the top_n most similar movies (including itself)\n", "    print(f\"Movies similar to '{title}' (First movie is itself):\")\n", "    for i, (index, score) in enumerate(sim_scores[:top_n+1]):\n", "        movie = df.iloc[index]\n", "        print(f\"{i}. {movie['title']} (Similarity Score: {score:.3f})\")\n", "        print(f\"   Genres: {movie['genres']}\")\n", "        print(f\"   Tag: {movie['tag']}\\n\")\n", "\n", "# Test the recommendation function\n", "recommendation(\"Toy Story (1995)\", df_2, similarity)"]}, {"cell_type": "markdown", "metadata": {"id": "d48ac905-a7ba-424b-9197-9f4c4ec3a607"}, "source": ["### <a id='toc1_6_1_'></a>[Exercise 2 - Check the recommendations for the movie 'Toy Story 2 (1999)'](#toc0_)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "f0640893-08bf-4ab0-96a6-287bd0636829"}, "outputs": [], "source": ["# TODO\n"]}, {"cell_type": "markdown", "metadata": {"id": "b0c440eb-f844-4b66-be18-eb49c0ed98b2"}, "source": ["---\n"]}, {"cell_type": "markdown", "metadata": {"id": "d8d3c13a-0bac-44b6-a553-8ea2a141f49f"}, "source": ["## <a id='toc1_7_'></a>[Collaborative filtering](#toc0_)\n", "\n", "Collaborative filtering is a recommendation system technique that makes automatic predictions about a user’s preferences by collecting taste or preference information from many users. The assumption behind collaborative filtering is that if users agreed on certain items in the past, they are likely to agree on similar items in the future.\n", "\n", "There are two primary approaches to collaborative filtering:\n", "\n", "1.\tUser-based Collaborative Filtering: This method identifies users with similar preferences and recommends items that similar users have liked. In other words, a user receives recommendations based on the preferences of users who have historically rated items similarly.\n", "2.\tItem-based Collaborative Filtering: In this method, items similar to those the user has liked or rated highly in the past are recommended. The system identifies items that are frequently rated similarly across a user base and suggests items that share these patterns.\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 267}, "id": "90f33ecb-499d-43e5-87b5-094ea4ddcc27", "outputId": "e0cd1edb-210f-410b-fd1e-d5a1aa8d8e04"}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["userId   1    2    3    4    5    6    7    8    9    10   ...  601  602  603  \\\n", "movieId                                                    ...                  \n", "1        4.0  0.0  0.0  0.0  4.0  0.0  4.5  0.0  0.0  0.0  ...  4.0  0.0  4.0   \n", "2        0.0  0.0  0.0  0.0  0.0  4.0  0.0  4.0  0.0  0.0  ...  0.0  4.0  0.0   \n", "3        4.0  0.0  0.0  0.0  0.0  5.0  0.0  0.0  0.0  0.0  ...  0.0  0.0  0.0   \n", "4        0.0  0.0  0.0  0.0  0.0  3.0  0.0  0.0  0.0  0.0  ...  0.0  0.0  0.0   \n", "5        0.0  0.0  0.0  0.0  0.0  5.0  0.0  0.0  0.0  0.0  ...  0.0  0.0  0.0   \n", "\n", "userId   604  605  606  607  608  609  610  \n", "movieId                                     \n", "1        3.0  4.0  2.5  4.0  2.5  3.0  5.0  \n", "2        5.0  3.5  0.0  0.0  2.0  0.0  0.0  \n", "3        0.0  0.0  0.0  0.0  2.0  0.0  0.0  \n", "4        0.0  0.0  0.0  0.0  0.0  0.0  0.0  \n", "5        3.0  0.0  0.0  0.0  0.0  0.0  0.0  \n", "\n", "[5 rows x 610 columns]"], "text/html": ["\n", "  <div id=\"df-3b0d9ba1-f711-4db5-ab88-1a80bc03784d\" class=\"colab-df-container\">\n", "    <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th>userId</th>\n", "      <th>1</th>\n", "      <th>2</th>\n", "      <th>3</th>\n", "      <th>4</th>\n", "      <th>5</th>\n", "      <th>6</th>\n", "      <th>7</th>\n", "      <th>8</th>\n", "      <th>9</th>\n", "      <th>10</th>\n", "      <th>...</th>\n", "      <th>601</th>\n", "      <th>602</th>\n", "      <th>603</th>\n", "      <th>604</th>\n", "      <th>605</th>\n", "      <th>606</th>\n", "      <th>607</th>\n", "      <th>608</th>\n", "      <th>609</th>\n", "      <th>610</th>\n", "    </tr>\n", "    <tr>\n", "      <th>movieId</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>4.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>4.0</td>\n", "      <td>0.0</td>\n", "      <td>4.5</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>4.0</td>\n", "      <td>0.0</td>\n", "      <td>4.0</td>\n", "      <td>3.0</td>\n", "      <td>4.0</td>\n", "      <td>2.5</td>\n", "      <td>4.0</td>\n", "      <td>2.5</td>\n", "      <td>3.0</td>\n", "      <td>5.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>4.0</td>\n", "      <td>0.0</td>\n", "      <td>4.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>4.0</td>\n", "      <td>0.0</td>\n", "      <td>5.0</td>\n", "      <td>3.5</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>2.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>5.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>2.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>3.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>5.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>3.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 610 columns</p>\n", "</div>\n", "    <div class=\"colab-df-buttons\">\n", "\n", "  <div class=\"colab-df-container\">\n", "    <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-3b0d9ba1-f711-4db5-ab88-1a80bc03784d')\"\n", "            title=\"Convert this dataframe to an interactive table.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\" viewBox=\"0 -960 960 960\">\n", "    <path d=\"M120-120v-720h720v720H120Zm60-500h600v-160H180v160Zm220 220h160v-160H400v160Zm0 220h160v-160H400v160ZM180-400h160v-160H180v160Zm440 0h160v-160H620v160ZM180-180h160v-160H180v160Zm440 0h160v-160H620v160Z\"/>\n", "  </svg>\n", "    </button>\n", "\n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    .colab-df-buttons div {\n", "      margin-bottom: 4px;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "    <script>\n", "      const buttonEl =\n", "        document.querySelector('#df-3b0d9ba1-f711-4db5-ab88-1a80bc03784d button.colab-df-convert');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      async function convertToInteractive(key) {\n", "        const element = document.querySelector('#df-3b0d9ba1-f711-4db5-ab88-1a80bc03784d');\n", "        const dataTable =\n", "          await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                    [key], {});\n", "        if (!dataTable) return;\n", "\n", "        const docLinkHtml = 'Like what you see? Visit the ' +\n", "          '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "          + ' to learn more about interactive tables.';\n", "        element.innerHTML = '';\n", "        dataTable['output_type'] = 'display_data';\n", "        await google.colab.output.renderOutput(dataTable, element);\n", "        const docLink = document.createElement('div');\n", "        docLink.innerHTML = docLinkHtml;\n", "        element.appendChild(docLink);\n", "      }\n", "    </script>\n", "  </div>\n", "\n", "\n", "<div id=\"df-6922597b-3c0b-42a6-8024-b0cec6a7fbd3\">\n", "  <button class=\"colab-df-quickchart\" onclick=\"quickchart('df-6922597b-3c0b-42a6-8024-b0cec6a7fbd3')\"\n", "            title=\"Suggest charts\"\n", "            style=\"display:none;\">\n", "\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "     width=\"24px\">\n", "    <g>\n", "        <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n", "    </g>\n", "</svg>\n", "  </button>\n", "\n", "<style>\n", "  .colab-df-quickchart {\n", "      --bg-color: #E8F0FE;\n", "      --fill-color: #1967D2;\n", "      --hover-bg-color: #E2EBFA;\n", "      --hover-fill-color: #174EA6;\n", "      --disabled-fill-color: #AAA;\n", "      --disabled-bg-color: #DDD;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart {\n", "      --bg-color: #3B4455;\n", "      --fill-color: #D2E3FC;\n", "      --hover-bg-color: #434B5C;\n", "      --hover-fill-color: #FFFFFF;\n", "      --disabled-bg-color: #3B4455;\n", "      --disabled-fill-color: #666;\n", "  }\n", "\n", "  .colab-df-quickchart {\n", "    background-color: var(--bg-color);\n", "    border: none;\n", "    border-radius: 50%;\n", "    cursor: pointer;\n", "    display: none;\n", "    fill: var(--fill-color);\n", "    height: 32px;\n", "    padding: 0;\n", "    width: 32px;\n", "  }\n", "\n", "  .colab-df-quickchart:hover {\n", "    background-color: var(--hover-bg-color);\n", "    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "    fill: var(--button-hover-fill-color);\n", "  }\n", "\n", "  .colab-df-quickchart-complete:disabled,\n", "  .colab-df-quickchart-complete:disabled:hover {\n", "    background-color: var(--disabled-bg-color);\n", "    fill: var(--disabled-fill-color);\n", "    box-shadow: none;\n", "  }\n", "\n", "  .colab-df-spinner {\n", "    border: 2px solid var(--fill-color);\n", "    border-color: transparent;\n", "    border-bottom-color: var(--fill-color);\n", "    animation:\n", "      spin 1s steps(1) infinite;\n", "  }\n", "\n", "  @keyframes spin {\n", "    0% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "      border-left-color: var(--fill-color);\n", "    }\n", "    20% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    30% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    40% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    60% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    80% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "    90% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "  }\n", "</style>\n", "\n", "  <script>\n", "    async function quickchart(key) {\n", "      const quickchartButtonEl =\n", "        document.querySelector('#' + key + ' button');\n", "      quickchartButtonEl.disabled = true;  // To prevent multiple clicks.\n", "      quickchartButtonEl.classList.add('colab-df-spinner');\n", "      try {\n", "        const charts = await google.colab.kernel.invokeFunction(\n", "            'suggest<PERSON><PERSON>s', [key], {});\n", "      } catch (error) {\n", "        console.error('Error during call to suggest<PERSON>harts:', error);\n", "      }\n", "      quickchartButtonEl.classList.remove('colab-df-spinner');\n", "      quickchartButtonEl.classList.add('colab-df-quickchart-complete');\n", "    }\n", "    (() => {\n", "      let quickchartButtonEl =\n", "        document.querySelector('#df-6922597b-3c0b-42a6-8024-b0cec6a7fbd3 button');\n", "      quickchartButtonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "    })();\n", "  </script>\n", "</div>\n", "\n", "    </div>\n", "  </div>\n"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "dataframe", "variable_name": "user_rating_matrix"}}, "metadata": {}, "execution_count": 24}], "source": ["# Pivot user-item matrix from ratings\n", "user_rating_matrix = rating_df.pivot(index=\"movieId\", columns=\"userId\", values=\"rating\")\n", "\n", "# fill na with 0\n", "user_rating_matrix = user_rating_matrix.fillna(0)\n", "\n", "user_rating_matrix.head()"]}, {"cell_type": "markdown", "metadata": {"id": "5970abb3-805c-4d87-8f0e-e3a52cfc0965"}, "source": ["In this section, we will be using a NearestNeighbors classifier and using it based on the cosine similarity metric.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 80}, "id": "4ea08bb8-01b5-466d-b4d9-f3fd5053094b", "outputId": "b8f72d8e-0ffd-4034-dcf4-34eb96ccdc7c"}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["NearestNeighbors(metric='cosine')"], "text/html": ["<style>#sk-container-id-1 {\n", "  /* Definition of color scheme common for light and dark mode */\n", "  --sklearn-color-text: black;\n", "  --sklearn-color-line: gray;\n", "  /* Definition of color scheme for unfitted estimators */\n", "  --sklearn-color-unfitted-level-0: #fff5e6;\n", "  --sklearn-color-unfitted-level-1: #f6e4d2;\n", "  --sklearn-color-unfitted-level-2: #ffe0b3;\n", "  --sklearn-color-unfitted-level-3: chocolate;\n", "  /* Definition of color scheme for fitted estimators */\n", "  --sklearn-color-fitted-level-0: #f0f8ff;\n", "  --sklearn-color-fitted-level-1: #d4ebff;\n", "  --sklearn-color-fitted-level-2: #b3dbfd;\n", "  --sklearn-color-fitted-level-3: cornflowerblue;\n", "\n", "  /* Specific color for light theme */\n", "  --sklearn-color-text-on-default-background: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, black)));\n", "  --sklearn-color-background: var(--sg-background-color, var(--theme-background, var(--jp-layout-color0, white)));\n", "  --sklearn-color-border-box: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, black)));\n", "  --sklearn-color-icon: #696969;\n", "\n", "  @media (prefers-color-scheme: dark) {\n", "    /* Redefinition of color scheme for dark theme */\n", "    --sklearn-color-text-on-default-background: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, white)));\n", "    --sklearn-color-background: var(--sg-background-color, var(--theme-background, var(--jp-layout-color0, #111)));\n", "    --sklearn-color-border-box: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, white)));\n", "    --sklearn-color-icon: #878787;\n", "  }\n", "}\n", "\n", "#sk-container-id-1 {\n", "  color: var(--sklearn-color-text);\n", "}\n", "\n", "#sk-container-id-1 pre {\n", "  padding: 0;\n", "}\n", "\n", "#sk-container-id-1 input.sk-hidden--visually {\n", "  border: 0;\n", "  clip: rect(1px 1px 1px 1px);\n", "  clip: rect(1px, 1px, 1px, 1px);\n", "  height: 1px;\n", "  margin: -1px;\n", "  overflow: hidden;\n", "  padding: 0;\n", "  position: absolute;\n", "  width: 1px;\n", "}\n", "\n", "#sk-container-id-1 div.sk-dashed-wrapped {\n", "  border: 1px dashed var(--sklearn-color-line);\n", "  margin: 0 0.4em 0.5em 0.4em;\n", "  box-sizing: border-box;\n", "  padding-bottom: 0.4em;\n", "  background-color: var(--sklearn-color-background);\n", "}\n", "\n", "#sk-container-id-1 div.sk-container {\n", "  /* jup<PERSON>r's `normalize.less` sets `[hidden] { display: none; }`\n", "     but bootstrap.min.css set `[hidden] { display: none !important; }`\n", "     so we also need the `!important` here to be able to override the\n", "     default hidden behavior on the sphinx rendered scikit-learn.org.\n", "     See: https://github.com/scikit-learn/scikit-learn/issues/21755 */\n", "  display: inline-block !important;\n", "  position: relative;\n", "}\n", "\n", "#sk-container-id-1 div.sk-text-repr-fallback {\n", "  display: none;\n", "}\n", "\n", "div.sk-parallel-item,\n", "div.sk-serial,\n", "div.sk-item {\n", "  /* draw centered vertical line to link estimators */\n", "  background-image: linear-gradient(var(--sklearn-color-text-on-default-background), var(--sklearn-color-text-on-default-background));\n", "  background-size: 2px 100%;\n", "  background-repeat: no-repeat;\n", "  background-position: center center;\n", "}\n", "\n", "/* Parallel-specific style estimator block */\n", "\n", "#sk-container-id-1 div.sk-parallel-item::after {\n", "  content: \"\";\n", "  width: 100%;\n", "  border-bottom: 2px solid var(--sklearn-color-text-on-default-background);\n", "  flex-grow: 1;\n", "}\n", "\n", "#sk-container-id-1 div.sk-parallel {\n", "  display: flex;\n", "  align-items: stretch;\n", "  justify-content: center;\n", "  background-color: var(--sklearn-color-background);\n", "  position: relative;\n", "}\n", "\n", "#sk-container-id-1 div.sk-parallel-item {\n", "  display: flex;\n", "  flex-direction: column;\n", "}\n", "\n", "#sk-container-id-1 div.sk-parallel-item:first-child::after {\n", "  align-self: flex-end;\n", "  width: 50%;\n", "}\n", "\n", "#sk-container-id-1 div.sk-parallel-item:last-child::after {\n", "  align-self: flex-start;\n", "  width: 50%;\n", "}\n", "\n", "#sk-container-id-1 div.sk-parallel-item:only-child::after {\n", "  width: 0;\n", "}\n", "\n", "/* Serial-specific style estimator block */\n", "\n", "#sk-container-id-1 div.sk-serial {\n", "  display: flex;\n", "  flex-direction: column;\n", "  align-items: center;\n", "  background-color: var(--sklearn-color-background);\n", "  padding-right: 1em;\n", "  padding-left: 1em;\n", "}\n", "\n", "\n", "/* Toggleable style: style used for estimator/Pipeline/ColumnTransformer box that is\n", "clickable and can be expanded/collapsed.\n", "- Pipeline and ColumnTransformer use this feature and define the default style\n", "- Estimators will overwrite some part of the style using the `sk-estimator` class\n", "*/\n", "\n", "/* Pipeline and ColumnTransformer style (default) */\n", "\n", "#sk-container-id-1 div.sk-toggleable {\n", "  /* Default theme specific background. It is overwritten whether we have a\n", "  specific estimator or a Pipeline/ColumnTransformer */\n", "  background-color: var(--sklearn-color-background);\n", "}\n", "\n", "/* Toggleable label */\n", "#sk-container-id-1 label.sk-toggleable__label {\n", "  cursor: pointer;\n", "  display: block;\n", "  width: 100%;\n", "  margin-bottom: 0;\n", "  padding: 0.5em;\n", "  box-sizing: border-box;\n", "  text-align: center;\n", "}\n", "\n", "#sk-container-id-1 label.sk-toggleable__label-arrow:before {\n", "  /* <PERSON> on the left of the label */\n", "  content: \"▸\";\n", "  float: left;\n", "  margin-right: 0.25em;\n", "  color: var(--sklearn-color-icon);\n", "}\n", "\n", "#sk-container-id-1 label.sk-toggleable__label-arrow:hover:before {\n", "  color: var(--sklearn-color-text);\n", "}\n", "\n", "/* Toggleable content - dropdown */\n", "\n", "#sk-container-id-1 div.sk-toggleable__content {\n", "  max-height: 0;\n", "  max-width: 0;\n", "  overflow: hidden;\n", "  text-align: left;\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-1 div.sk-toggleable__content.fitted {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "#sk-container-id-1 div.sk-toggleable__content pre {\n", "  margin: 0.2em;\n", "  border-radius: 0.25em;\n", "  color: var(--sklearn-color-text);\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-1 div.sk-toggleable__content.fitted pre {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "#sk-container-id-1 input.sk-toggleable__control:checked~div.sk-toggleable__content {\n", "  /* Expand drop-down */\n", "  max-height: 200px;\n", "  max-width: 100%;\n", "  overflow: auto;\n", "}\n", "\n", "#sk-container-id-1 input.sk-toggleable__control:checked~label.sk-toggleable__label-arrow:before {\n", "  content: \"▾\";\n", "}\n", "\n", "/* Pipeline/ColumnTransformer-specific style */\n", "\n", "#sk-container-id-1 div.sk-label input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-1 div.sk-label.fitted input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Estimator-specific style */\n", "\n", "/* Colorize estimator box */\n", "#sk-container-id-1 div.sk-estimator input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-1 div.sk-estimator.fitted input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "#sk-container-id-1 div.sk-label label.sk-toggleable__label,\n", "#sk-container-id-1 div.sk-label label {\n", "  /* The background is the default theme color */\n", "  color: var(--sklearn-color-text-on-default-background);\n", "}\n", "\n", "/* On hover, darken the color of the background */\n", "#sk-container-id-1 div.sk-label:hover label.sk-toggleable__label {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "/* Label box, darken color on hover, fitted */\n", "#sk-container-id-1 div.sk-label.fitted:hover label.sk-toggleable__label.fitted {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Estimator label */\n", "\n", "#sk-container-id-1 div.sk-label label {\n", "  font-family: monospace;\n", "  font-weight: bold;\n", "  display: inline-block;\n", "  line-height: 1.2em;\n", "}\n", "\n", "#sk-container-id-1 div.sk-label-container {\n", "  text-align: center;\n", "}\n", "\n", "/* Estimator-specific */\n", "#sk-container-id-1 div.sk-estimator {\n", "  font-family: monospace;\n", "  border: 1px dotted var(--sklearn-color-border-box);\n", "  border-radius: 0.25em;\n", "  box-sizing: border-box;\n", "  margin-bottom: 0.5em;\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-1 div.sk-estimator.fitted {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "/* on hover */\n", "#sk-container-id-1 div.sk-estimator:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-1 div.sk-estimator.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Specification for estimator info (e.g. \"i\" and \"?\") */\n", "\n", "/* Common style for \"i\" and \"?\" */\n", "\n", ".sk-estimator-doc-link,\n", "a:link.sk-estimator-doc-link,\n", "a:visited.sk-estimator-doc-link {\n", "  float: right;\n", "  font-size: smaller;\n", "  line-height: 1em;\n", "  font-family: monospace;\n", "  background-color: var(--sklearn-color-background);\n", "  border-radius: 1em;\n", "  height: 1em;\n", "  width: 1em;\n", "  text-decoration: none !important;\n", "  margin-left: 1ex;\n", "  /* unfitted */\n", "  border: var(--sklearn-color-unfitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-unfitted-level-1);\n", "}\n", "\n", ".sk-estimator-doc-link.fitted,\n", "a:link.sk-estimator-doc-link.fitted,\n", "a:visited.sk-estimator-doc-link.fitted {\n", "  /* fitted */\n", "  border: var(--sklearn-color-fitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-fitted-level-1);\n", "}\n", "\n", "/* On hover */\n", "div.sk-estimator:hover .sk-estimator-doc-link:hover,\n", ".sk-estimator-doc-link:hover,\n", "div.sk-label-container:hover .sk-estimator-doc-link:hover,\n", ".sk-estimator-doc-link:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "div.sk-estimator.fitted:hover .sk-estimator-doc-link.fitted:hover,\n", ".sk-estimator-doc-link.fitted:hover,\n", "div.sk-label-container:hover .sk-estimator-doc-link.fitted:hover,\n", ".sk-estimator-doc-link.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "/* Span, style for the box shown on hovering the info icon */\n", ".sk-estimator-doc-link span {\n", "  display: none;\n", "  z-index: 9999;\n", "  position: relative;\n", "  font-weight: normal;\n", "  right: .2ex;\n", "  padding: .5ex;\n", "  margin: .5ex;\n", "  width: min-content;\n", "  min-width: 20ex;\n", "  max-width: 50ex;\n", "  color: var(--sklearn-color-text);\n", "  box-shadow: 2pt 2pt 4pt #999;\n", "  /* unfitted */\n", "  background: var(--sklearn-color-unfitted-level-0);\n", "  border: .5pt solid var(--sklearn-color-unfitted-level-3);\n", "}\n", "\n", ".sk-estimator-doc-link.fitted span {\n", "  /* fitted */\n", "  background: var(--sklearn-color-fitted-level-0);\n", "  border: var(--sklearn-color-fitted-level-3);\n", "}\n", "\n", ".sk-estimator-doc-link:hover span {\n", "  display: block;\n", "}\n", "\n", "/* \"?\"-specific style due to the `<a>` HTML tag */\n", "\n", "#sk-container-id-1 a.estimator_doc_link {\n", "  float: right;\n", "  font-size: 1rem;\n", "  line-height: 1em;\n", "  font-family: monospace;\n", "  background-color: var(--sklearn-color-background);\n", "  border-radius: 1rem;\n", "  height: 1rem;\n", "  width: 1rem;\n", "  text-decoration: none;\n", "  /* unfitted */\n", "  color: var(--sklearn-color-unfitted-level-1);\n", "  border: var(--sklearn-color-unfitted-level-1) 1pt solid;\n", "}\n", "\n", "#sk-container-id-1 a.estimator_doc_link.fitted {\n", "  /* fitted */\n", "  border: var(--sklearn-color-fitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-fitted-level-1);\n", "}\n", "\n", "/* On hover */\n", "#sk-container-id-1 a.estimator_doc_link:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "#sk-container-id-1 a.estimator_doc_link.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-3);\n", "}\n", "</style><div id=\"sk-container-id-1\" class=\"sk-top-container\"><div class=\"sk-text-repr-fallback\"><pre>NearestNeighbors(metric=&#x27;cosine&#x27;)</pre><b>In a Jupyter environment, please rerun this cell to show the HTML representation or trust the notebook. <br />On GitHub, the HTML representation is unable to render, please try loading this page with nbviewer.org.</b></div><div class=\"sk-container\" hidden><div class=\"sk-item\"><div class=\"sk-estimator fitted sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-1\" type=\"checkbox\" checked><label for=\"sk-estimator-id-1\" class=\"sk-toggleable__label fitted sk-toggleable__label-arrow fitted\">&nbsp;&nbsp;NearestNeighbors<a class=\"sk-estimator-doc-link fitted\" rel=\"noreferrer\" target=\"_blank\" href=\"https://scikit-learn.org/1.5/modules/generated/sklearn.neighbors.NearestNeighbors.html\">?<span>Documentation for NearestNeighbors</span></a><span class=\"sk-estimator-doc-link fitted\">i<span>Fitted</span></span></label><div class=\"sk-toggleable__content fitted\"><pre>NearestNeighbors(metric=&#x27;cosine&#x27;)</pre></div> </div></div></div></div>"]}, "metadata": {}, "execution_count": 25}], "source": ["from sklearn.neighbors import NearestNeighbors\n", "\n", "rec = NearestNeighbors(metric = 'cosine')\n", "rec.fit(user_rating_matrix)"]}, {"cell_type": "markdown", "metadata": {"id": "c3433c7b-3658-4741-a797-eff0d1eac161"}, "source": ["Finally, here is our function to get 5 recommended items based on a movie previously watched.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 206}, "id": "47ca3b8c-d44e-4a51-9169-e986813676b4", "outputId": "237b388c-c3b1-4c6f-ae80-67c38e37c445"}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["                                       title  avgRating  \\\n", "0                         Toy Story 2 (1999)   3.125000   \n", "1                       Jurassic Park (1993)   4.500000   \n", "2       Independence Day (a.k.a. ID4) (1996)   4.000000   \n", "3  Star Wars: Episode IV - A New Hope (1977)   4.527778   \n", "4                        <PERSON> (1994)   3.666667   \n", "\n", "                                        genres  \n", "0  Adventure|Animation|Children|Comedy|Fantasy  \n", "1             Action|Adventure|Sci-Fi|Thriller  \n", "2             Action|Adventure|Sci-Fi|Thriller  \n", "3                      Action|Adventure|Sci-Fi  \n", "4                     Comedy|Drama|Romance|War  "], "text/html": ["\n", "  <div id=\"df-1c7e237d-4284-41a4-8f26-65e1145ee23b\" class=\"colab-df-container\">\n", "    <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>title</th>\n", "      <th>avgRating</th>\n", "      <th>genres</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Toy Story 2 (1999)</td>\n", "      <td>3.125000</td>\n", "      <td>Adventure|Animation|Children|Comedy|Fantasy</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Jurassic Park (1993)</td>\n", "      <td>4.500000</td>\n", "      <td>Action|Adventure|Sci-Fi|Thriller</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Independence Day (a.k.a. ID4) (1996)</td>\n", "      <td>4.000000</td>\n", "      <td>Action|Adventure|Sci-Fi|Thriller</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Star Wars: Episode IV - A New Hope (1977)</td>\n", "      <td>4.527778</td>\n", "      <td>Action|Adventure|Sci-Fi</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td><PERSON> (1994)</td>\n", "      <td>3.666667</td>\n", "      <td>Comedy|Drama|Romance|War</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>\n", "    <div class=\"colab-df-buttons\">\n", "\n", "  <div class=\"colab-df-container\">\n", "    <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-1c7e237d-4284-41a4-8f26-65e1145ee23b')\"\n", "            title=\"Convert this dataframe to an interactive table.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\" viewBox=\"0 -960 960 960\">\n", "    <path d=\"M120-120v-720h720v720H120Zm60-500h600v-160H180v160Zm220 220h160v-160H400v160Zm0 220h160v-160H400v160ZM180-400h160v-160H180v160Zm440 0h160v-160H620v160ZM180-180h160v-160H180v160Zm440 0h160v-160H620v160Z\"/>\n", "  </svg>\n", "    </button>\n", "\n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    .colab-df-buttons div {\n", "      margin-bottom: 4px;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "    <script>\n", "      const buttonEl =\n", "        document.querySelector('#df-1c7e237d-4284-41a4-8f26-65e1145ee23b button.colab-df-convert');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      async function convertToInteractive(key) {\n", "        const element = document.querySelector('#df-1c7e237d-4284-41a4-8f26-65e1145ee23b');\n", "        const dataTable =\n", "          await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                    [key], {});\n", "        if (!dataTable) return;\n", "\n", "        const docLinkHtml = 'Like what you see? Visit the ' +\n", "          '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "          + ' to learn more about interactive tables.';\n", "        element.innerHTML = '';\n", "        dataTable['output_type'] = 'display_data';\n", "        await google.colab.output.renderOutput(dataTable, element);\n", "        const docLink = document.createElement('div');\n", "        docLink.innerHTML = docLinkHtml;\n", "        element.appendChild(docLink);\n", "      }\n", "    </script>\n", "  </div>\n", "\n", "\n", "<div id=\"df-1f38ea11-6c8a-4fd3-a896-ed27e00393db\">\n", "  <button class=\"colab-df-quickchart\" onclick=\"quickchart('df-1f38ea11-6c8a-4fd3-a896-ed27e00393db')\"\n", "            title=\"Suggest charts\"\n", "            style=\"display:none;\">\n", "\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "     width=\"24px\">\n", "    <g>\n", "        <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n", "    </g>\n", "</svg>\n", "  </button>\n", "\n", "<style>\n", "  .colab-df-quickchart {\n", "      --bg-color: #E8F0FE;\n", "      --fill-color: #1967D2;\n", "      --hover-bg-color: #E2EBFA;\n", "      --hover-fill-color: #174EA6;\n", "      --disabled-fill-color: #AAA;\n", "      --disabled-bg-color: #DDD;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart {\n", "      --bg-color: #3B4455;\n", "      --fill-color: #D2E3FC;\n", "      --hover-bg-color: #434B5C;\n", "      --hover-fill-color: #FFFFFF;\n", "      --disabled-bg-color: #3B4455;\n", "      --disabled-fill-color: #666;\n", "  }\n", "\n", "  .colab-df-quickchart {\n", "    background-color: var(--bg-color);\n", "    border: none;\n", "    border-radius: 50%;\n", "    cursor: pointer;\n", "    display: none;\n", "    fill: var(--fill-color);\n", "    height: 32px;\n", "    padding: 0;\n", "    width: 32px;\n", "  }\n", "\n", "  .colab-df-quickchart:hover {\n", "    background-color: var(--hover-bg-color);\n", "    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "    fill: var(--button-hover-fill-color);\n", "  }\n", "\n", "  .colab-df-quickchart-complete:disabled,\n", "  .colab-df-quickchart-complete:disabled:hover {\n", "    background-color: var(--disabled-bg-color);\n", "    fill: var(--disabled-fill-color);\n", "    box-shadow: none;\n", "  }\n", "\n", "  .colab-df-spinner {\n", "    border: 2px solid var(--fill-color);\n", "    border-color: transparent;\n", "    border-bottom-color: var(--fill-color);\n", "    animation:\n", "      spin 1s steps(1) infinite;\n", "  }\n", "\n", "  @keyframes spin {\n", "    0% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "      border-left-color: var(--fill-color);\n", "    }\n", "    20% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    30% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    40% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    60% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    80% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "    90% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "  }\n", "</style>\n", "\n", "  <script>\n", "    async function quickchart(key) {\n", "      const quickchartButtonEl =\n", "        document.querySelector('#' + key + ' button');\n", "      quickchartButtonEl.disabled = true;  // To prevent multiple clicks.\n", "      quickchartButtonEl.classList.add('colab-df-spinner');\n", "      try {\n", "        const charts = await google.colab.kernel.invokeFunction(\n", "            'suggest<PERSON><PERSON>s', [key], {});\n", "      } catch (error) {\n", "        console.error('Error during call to suggest<PERSON>harts:', error);\n", "      }\n", "      quickchartButtonEl.classList.remove('colab-df-spinner');\n", "      quickchartButtonEl.classList.add('colab-df-quickchart-complete');\n", "    }\n", "    (() => {\n", "      let quickchartButtonEl =\n", "        document.querySelector('#df-1f38ea11-6c8a-4fd3-a896-ed27e00393db button');\n", "      quickchartButtonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "    })();\n", "  </script>\n", "</div>\n", "\n", "    </div>\n", "  </div>\n"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "dataframe", "summary": "{\n  \"name\": \"get_recommendations('Toy Story (1995)')\",\n  \"rows\": 5,\n  \"fields\": [\n    {\n      \"column\": \"title\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 5,\n        \"samples\": [\n          \"Jurassic Park (1993)\",\n          \"<PERSON> (1994)\",\n          \"Independence Day (a.k.a. ID4) (1996)\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"avgRating\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0.591347070699341,\n        \"min\": 3.125,\n        \"max\": 4.527777777777778,\n        \"num_unique_values\": 5,\n        \"samples\": [\n          4.5,\n          3.6666666666666665,\n          4.0\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"genres\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 4,\n        \"samples\": [\n          \"Action|Adventure|Sci-Fi|Thriller\",\n          \"Comedy|Drama|Romance|War\",\n          \"Adventure|Animation|Children|Comedy|Fantasy\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    }\n  ]\n}"}}, "metadata": {}, "execution_count": 26}], "source": ["# Function to get movie recommendations based on a title\n", "def get_recommendations(title):\n", "    # Get movie details\n", "    movie = df_2[df_2['title'] == title]\n", "\n", "    if movie.empty:\n", "        print(f\"Movie '{title}' not found in dataset.\")\n", "        return None\n", "\n", "    movie_id = int(movie['movieId'])\n", "\n", "    # Get the index of the movie in the user-item matrix\n", "    try:\n", "        user_index = user_rating_matrix.index.get_loc(movie_id)\n", "    except KeyError:\n", "        print(f\"Movie ID {movie_id} not found in the user rating matrix.\")\n", "        return None\n", "\n", "    # Get the user ratings for the movie\n", "    user_ratings = user_rating_matrix.iloc[user_index]\n", "\n", "    # Reshape the ratings to be a single sample (1, -1)\n", "    reshaped_df = user_ratings.values.reshape(1, -1)\n", "\n", "    # Find the nearest neighbors (similar movies)\n", "    distances, indices = rec.kneighbors(reshaped_df, n_neighbors=15)\n", "\n", "    # Get the movieIds of the nearest neighbors (excluding the first, which is the queried movie itself)\n", "    nearest_idx = user_rating_matrix.iloc[indices[0]].index[1:]\n", "\n", "    # Get the movie details for the nearest neighbors\n", "    nearest_neighbors = pd.DataFrame({'movieId': nearest_idx})\n", "    result = pd.merge(nearest_neighbors, df_2, on='movieId', how='left')\n", "\n", "    # Return the top recommendations\n", "    return result[['title', 'avgRating', 'genres']].head()\n", "\n", "# Test the recommendation function\n", "get_recommendations('Toy Story (1995)')"]}, {"cell_type": "markdown", "metadata": {"id": "127682cd-e3f6-404d-9633-e7ff6994bc64"}, "source": ["### <a id='toc1_7_1_'></a>[Exercise 3 - Check the recommendations for the movie 'Jurassic Park (1993)'](#toc0_)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "28e96778-b0ec-487a-bf9e-1abbbe7955e8", "colab": {"base_uri": "https://localhost:8080/", "height": 206}, "outputId": "5e8aeb4b-d0ba-4fdb-fd61-393603fc36f4"}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["                               title  avgRating                    genres\n", "0  Terminator 2: Judgment Day (1991)   2.625000             Action|Sci-Fi\n", "1                <PERSON> (1994)   3.666667  Comedy|Drama|Romance|War\n", "2                  Braveheart (1995)   4.350000          Action|Drama|War\n", "3               Fugit<PERSON>, The (1993)   5.000000                  Thriller\n", "4                       Speed (1994)   4.000000   Action|Romance|Thriller"], "text/html": ["\n", "  <div id=\"df-cf04cdca-a536-4154-92ee-da1406ee43b1\" class=\"colab-df-container\">\n", "    <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>title</th>\n", "      <th>avgRating</th>\n", "      <th>genres</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Terminator 2: Judgment Day (1991)</td>\n", "      <td>2.625000</td>\n", "      <td>Action|Sci-Fi</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td><PERSON> (1994)</td>\n", "      <td>3.666667</td>\n", "      <td>Comedy|Drama|Romance|War</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td><PERSON><PERSON> (1995)</td>\n", "      <td>4.350000</td>\n", "      <td>Action|Drama|War</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Fu<PERSON><PERSON>, The (1993)</td>\n", "      <td>5.000000</td>\n", "      <td>Thriller</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td><PERSON> (1994)</td>\n", "      <td>4.000000</td>\n", "      <td>Action|Romance|Thriller</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>\n", "    <div class=\"colab-df-buttons\">\n", "\n", "  <div class=\"colab-df-container\">\n", "    <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-cf04cdca-a536-4154-92ee-da1406ee43b1')\"\n", "            title=\"Convert this dataframe to an interactive table.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\" viewBox=\"0 -960 960 960\">\n", "    <path d=\"M120-120v-720h720v720H120Zm60-500h600v-160H180v160Zm220 220h160v-160H400v160Zm0 220h160v-160H400v160ZM180-400h160v-160H180v160Zm440 0h160v-160H620v160ZM180-180h160v-160H180v160Zm440 0h160v-160H620v160Z\"/>\n", "  </svg>\n", "    </button>\n", "\n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    .colab-df-buttons div {\n", "      margin-bottom: 4px;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "    <script>\n", "      const buttonEl =\n", "        document.querySelector('#df-cf04cdca-a536-4154-92ee-da1406ee43b1 button.colab-df-convert');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      async function convertToInteractive(key) {\n", "        const element = document.querySelector('#df-cf04cdca-a536-4154-92ee-da1406ee43b1');\n", "        const dataTable =\n", "          await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                    [key], {});\n", "        if (!dataTable) return;\n", "\n", "        const docLinkHtml = 'Like what you see? Visit the ' +\n", "          '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "          + ' to learn more about interactive tables.';\n", "        element.innerHTML = '';\n", "        dataTable['output_type'] = 'display_data';\n", "        await google.colab.output.renderOutput(dataTable, element);\n", "        const docLink = document.createElement('div');\n", "        docLink.innerHTML = docLinkHtml;\n", "        element.appendChild(docLink);\n", "      }\n", "    </script>\n", "  </div>\n", "\n", "\n", "<div id=\"df-7c8824bd-d0ce-418a-9aaf-0b834c954c49\">\n", "  <button class=\"colab-df-quickchart\" onclick=\"quickchart('df-7c8824bd-d0ce-418a-9aaf-0b834c954c49')\"\n", "            title=\"Suggest charts\"\n", "            style=\"display:none;\">\n", "\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "     width=\"24px\">\n", "    <g>\n", "        <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n", "    </g>\n", "</svg>\n", "  </button>\n", "\n", "<style>\n", "  .colab-df-quickchart {\n", "      --bg-color: #E8F0FE;\n", "      --fill-color: #1967D2;\n", "      --hover-bg-color: #E2EBFA;\n", "      --hover-fill-color: #174EA6;\n", "      --disabled-fill-color: #AAA;\n", "      --disabled-bg-color: #DDD;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart {\n", "      --bg-color: #3B4455;\n", "      --fill-color: #D2E3FC;\n", "      --hover-bg-color: #434B5C;\n", "      --hover-fill-color: #FFFFFF;\n", "      --disabled-bg-color: #3B4455;\n", "      --disabled-fill-color: #666;\n", "  }\n", "\n", "  .colab-df-quickchart {\n", "    background-color: var(--bg-color);\n", "    border: none;\n", "    border-radius: 50%;\n", "    cursor: pointer;\n", "    display: none;\n", "    fill: var(--fill-color);\n", "    height: 32px;\n", "    padding: 0;\n", "    width: 32px;\n", "  }\n", "\n", "  .colab-df-quickchart:hover {\n", "    background-color: var(--hover-bg-color);\n", "    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "    fill: var(--button-hover-fill-color);\n", "  }\n", "\n", "  .colab-df-quickchart-complete:disabled,\n", "  .colab-df-quickchart-complete:disabled:hover {\n", "    background-color: var(--disabled-bg-color);\n", "    fill: var(--disabled-fill-color);\n", "    box-shadow: none;\n", "  }\n", "\n", "  .colab-df-spinner {\n", "    border: 2px solid var(--fill-color);\n", "    border-color: transparent;\n", "    border-bottom-color: var(--fill-color);\n", "    animation:\n", "      spin 1s steps(1) infinite;\n", "  }\n", "\n", "  @keyframes spin {\n", "    0% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "      border-left-color: var(--fill-color);\n", "    }\n", "    20% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    30% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    40% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    60% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    80% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "    90% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "  }\n", "</style>\n", "\n", "  <script>\n", "    async function quickchart(key) {\n", "      const quickchartButtonEl =\n", "        document.querySelector('#' + key + ' button');\n", "      quickchartButtonEl.disabled = true;  // To prevent multiple clicks.\n", "      quickchartButtonEl.classList.add('colab-df-spinner');\n", "      try {\n", "        const charts = await google.colab.kernel.invokeFunction(\n", "            'suggest<PERSON><PERSON>s', [key], {});\n", "      } catch (error) {\n", "        console.error('Error during call to suggest<PERSON>harts:', error);\n", "      }\n", "      quickchartButtonEl.classList.remove('colab-df-spinner');\n", "      quickchartButtonEl.classList.add('colab-df-quickchart-complete');\n", "    }\n", "    (() => {\n", "      let quickchartButtonEl =\n", "        document.querySelector('#df-7c8824bd-d0ce-418a-9aaf-0b834c954c49 button');\n", "      quickchartButtonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "    })();\n", "  </script>\n", "</div>\n", "\n", "    </div>\n", "  </div>\n"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "dataframe", "summary": "{\n  \"name\": \"get_recommendations('Jurassic Park (1993)')\",\n  \"rows\": 5,\n  \"fields\": [\n    {\n      \"column\": \"title\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 5,\n        \"samples\": [\n          \"<PERSON> Gump (1994)\",\n          \"<PERSON> (1994)\",\n          \"Braveheart (1995)\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"avgRating\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0.8801357218570832,\n        \"min\": 2.625,\n        \"max\": 5.0,\n        \"num_unique_values\": 5,\n        \"samples\": [\n          3.6666666666666665,\n          4.0,\n          4.35\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"genres\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 5,\n        \"samples\": [\n          \"Comedy|Drama|Romance|War\",\n          \"Action|Romance|Thriller\",\n          \"Action|Drama|War\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    }\n  ]\n}"}}, "metadata": {}, "execution_count": 28}], "source": ["# TODO\n", "get_recommendations('Jurassic Park (1993)')"]}, {"cell_type": "markdown", "metadata": {"id": "6ebaa636-534b-44a9-aff0-9f6982dc08e7"}, "source": ["---\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.2"}, "prev_pub_hash": "b0b82231895db011041de88091170f7aae4296b418b954efd651001688cdcbc9", "colab": {"provenance": []}}, "nbformat": 4, "nbformat_minor": 0}