#!/usr/bin/env node

/**
 * Test complet de l'API du chatbot
 */

const axios = require('axios');

async function testCompleteAPI() {
    console.log('🌐 TEST COMPLET DE L\'API CHATBOT');
    console.log('='.repeat(50));
    
    const baseURL = 'http://localhost:5001';
    const testMessage = 'هل يوجد درس اسمه الضرب في رقمين؟';
    const userId = 3712; // ID utilisateur de test
    
    try {
        console.log('\n📤 Envoi de la requête API...');
        console.log(`URL: ${baseURL}/api/chatbot/ask`);
        console.log(`Message: "${testMessage}"`);
        console.log(`User ID: ${userId}`);
        
        const response = await axios.post(`${baseURL}/api/chatbot/ask`, {
            message: testMessage,
            userId: userId,
            messageType: 'text'
        }, {
            headers: {
                'Content-Type': 'application/json'
            },
            timeout: 30000 // 30 secondes de timeout
        });
        
        console.log('\n✅ RÉPONSE REÇUE:');
        console.log('='.repeat(30));
        console.log(`Status: ${response.status}`);
        console.log(`Reply: ${response.data.reply}`);
        
        if (response.data.audio) {
            console.log(`Audio: ${response.data.audio}`);
        }
        
        if (response.data.navigation) {
            console.log('\n🧭 NAVIGATION:');
            console.log(JSON.stringify(response.data.navigation, null, 2));
        }
        
        if (response.data.voiceId) {
            console.log(`Voice ID: ${response.data.voiceId}`);
        }
        
        if (response.data.gender) {
            console.log(`Gender: ${response.data.gender}`);
        }
        
        console.log('\n🎉 TEST RÉUSSI !');
        
        // Analyser la réponse
        if (response.data.reply.includes('لقيت') || response.data.reply.includes('وجدت')) {
            console.log('✅ Le chatbot a trouvé les cours !');
        } else if (response.data.reply.includes('ما لقيتش') || response.data.reply.includes('لم أجد')) {
            console.log('❌ Le chatbot dit qu\'il n\'a pas trouvé les cours');
        } else {
            console.log('🤔 Réponse ambiguë du chatbot');
        }
        
    } catch (error) {
        console.error('\n❌ ERREUR LORS DU TEST:');
        
        if (error.response) {
            console.error(`Status: ${error.response.status}`);
            console.error(`Data:`, error.response.data);
        } else if (error.request) {
            console.error('Aucune réponse reçue du serveur');
            console.error('Vérifiez que le serveur est démarré sur le port 5001');
        } else {
            console.error('Erreur de configuration:', error.message);
        }
    }
}

// Exécuter si appelé directement
if (require.main === module) {
    testCompleteAPI().catch(console.error);
}

module.exports = { testCompleteAPI };
