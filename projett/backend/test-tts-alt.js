require('dotenv').config();
const { ElevenLabsClient } = require("elevenlabs");
const fs = require("fs");
const path = require("path");

const ELEVENLABS_API_KEY = process.env.ELEVENLABS_API_KEY;
// Alternative female voice with good multilingual support
const VOICE_ID = "EXAVITQu4vr4xnSDxMaL";

const client = new ElevenLabsClient({ 
    apiKey: ELEVENLABS_API_KEY,
    timeoutMs: 30000 
});

// Convert ReadableStream to Buffer
async function streamToBuffer(stream) {
    const reader = stream.getReader();
    const chunks = [];
    
    try {
        while (true) {
            const { done, value } = await reader.read();
            if (done) break;
            chunks.push(value);
        }
        
        return Buffer.concat(chunks);
    } catch (error) {
        console.error("Error in streamToBuffer:", error);
        throw error;
    }
}

async function testAlternativeVoice() {
    try {
        // Arabic text
        const arabicText = "مرحبا، كيف حالك اليوم؟ أنا هنا لمساعدتك.";
        console.log("Testing alternative voice with Arabic text:", arabicText);
        
        const audioStream = await client.textToSpeech.convert(VOICE_ID, {
            text: arabicText,
            model_id: "eleven_turbo_v2",
            output_format: "mp3_44100_64",
            voice_settings: {
                stability: 0.3,
                similarity_boost: 0.8,
                style: 0.0,
                use_speaker_boost: true,
                speaking_rate: 1.0
            },
            optimize_streaming_latency: 4,
            apply_text_normalization: "off"
        });

        // Convert to Buffer
        const audioBuffer = await streamToBuffer(audioStream);

        // Create uploads directory if needed
        const uploadDir = path.join(__dirname, "public/src/uploads/");
        if (!fs.existsSync(uploadDir)) {
            fs.mkdirSync(uploadDir, { recursive: true });
        }

        // Generate unique filename
        const uniqueName = `speech_alt_${Date.now()}_${Math.floor(Math.random() * 1000)}.mp3`;
        const filePath = path.join(uploadDir, uniqueName);

        // Save file
        fs.writeFileSync(filePath, audioBuffer);

        console.log("Alternative voice audio generated at:", filePath);
        console.log("Web path:", `/uploads/${uniqueName}`);
    } catch (error) {
        console.error("Error testing alternative voice:", error);
    }
}

testAlternativeVoice();
