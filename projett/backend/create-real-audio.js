#!/usr/bin/env node

/**
 * Script pour créer un vrai fichier audio avec voix TTS
 */

const fs = require('fs');
const path = require('path');
const { exec } = require('child_process');

const UPLOADS_DIR = path.join(__dirname, 'uploads/asmaa_cache');
const ASSETS_DIR = path.join(__dirname, 'assets/audio');
const FALLBACK_FILE = 'asmaa_fallback.mp3';

// Créer les dossiers s'ils n'existent pas
[UPLOADS_DIR, ASSETS_DIR].forEach(dir => {
    if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
        console.log(`📁 Dossier créé: ${dir}`);
    }
});

/**
 * Créer un fichier audio avec une vraie voix
 */
function createRealAudio() {
    return new Promise((resolve, reject) => {
        const tempPath = path.join(UPLOADS_DIR, 'temp_fallback.aiff');
        const outputPath = path.join(UPLOADS_DIR, FALLBACK_FILE);
        
        console.log('🎵 Création du fichier audio avec voix réelle...');
        
        // Texte plus long pour le fallback
        const englishText = "Hello, I am Asmaa, your smart assistant. I am here to help you with your learning journey. How can I assist you today? Please feel free to ask me any questions about your studies.";

        // Étape 1: Créer un fichier AIFF avec say (voix système)
        const sayCommand = `say -v "Samantha" -r 160 -o "${tempPath}" "${englishText}"`;
        
        console.log('🗣️ Génération de la voix...');
        exec(sayCommand, (error1, stdout1, stderr1) => {
            if (error1) {
                console.error('❌ Erreur avec say:', error1.message);
                // Fallback avec texte anglais
                const englishText = "Hello, I am Asmaa, your smart assistant. How can I help you today?";
                const fallbackCommand = `say -v "Samantha" -r 180 -o "${tempPath}" "${englishText}"`;
                
                exec(fallbackCommand, (error2, stdout2, stderr2) => {
                    if (error2) {
                        reject(error2);
                        return;
                    }
                    convertToMp3(tempPath, outputPath, resolve, reject);
                });
                return;
            }
            
            convertToMp3(tempPath, outputPath, resolve, reject);
        });
    });
}

function convertToMp3(tempPath, outputPath, resolve, reject) {
    console.log('🔄 Conversion en MP3...');
    
    // Étape 2: Convertir AIFF en MP3 avec ffmpeg
    const convertCommand = `ffmpeg -i "${tempPath}" -ac 1 -ar 22050 -b:a 64k -y "${outputPath}"`;
    
    exec(convertCommand, (error2, stdout2, stderr2) => {
        // Nettoyer le fichier temporaire
        if (fs.existsSync(tempPath)) {
            fs.unlinkSync(tempPath);
        }
        
        if (error2) {
            console.error('❌ Erreur conversion MP3:', error2.message);
            reject(error2);
        } else {
            console.log('✅ Fichier audio créé avec succès');
            resolve(outputPath);
        }
    });
}

/**
 * Copier le fichier vers le dossier assets
 */
function copyToAssets(sourcePath) {
    const destPath = path.join(ASSETS_DIR, FALLBACK_FILE);
    
    try {
        fs.copyFileSync(sourcePath, destPath);
        console.log(`✅ Fichier copié vers: ${destPath}`);
        return destPath;
    } catch (error) {
        console.error(`❌ Erreur lors de la copie: ${error.message}`);
        throw error;
    }
}

/**
 * Vérifier qu'un fichier audio est valide
 */
function verifyAudioFile(filePath) {
    return new Promise((resolve, reject) => {
        const command = `ffprobe -v quiet -print_format json -show_format "${filePath}"`;
        
        exec(command, (error, stdout, stderr) => {
            if (error) {
                reject(error);
            } else {
                try {
                    const info = JSON.parse(stdout);
                    const duration = parseFloat(info.format.duration);
                    const size = parseInt(info.format.size);
                    
                    console.log(`📊 Fichier audio valide: ${duration.toFixed(2)}s, ${size} bytes`);
                    
                    if (duration < 0.5) {
                        reject(new Error('Fichier audio trop court'));
                    } else {
                        resolve(true);
                    }
                } catch (parseError) {
                    reject(parseError);
                }
            }
        });
    });
}

/**
 * Fonction principale
 */
async function createRealFallbackAudio() {
    console.log('🎯 Création du fichier audio fallback avec vraie voix...');
    
    try {
        // Supprimer l'ancien fichier s'il existe
        const oldFile = path.join(UPLOADS_DIR, FALLBACK_FILE);
        if (fs.existsSync(oldFile)) {
            fs.unlinkSync(oldFile);
            console.log('🗑️ Ancien fichier supprimé');
        }
        
        // Créer le nouveau fichier audio
        const audioPath = await createRealAudio();
        
        // Vérifier que le fichier est valide
        await verifyAudioFile(audioPath);
        
        // Copier vers assets
        copyToAssets(audioPath);
        
        console.log('🎉 Fichier audio fallback créé avec succès !');
        console.log(`📁 Emplacement: ${audioPath}`);
        
        // Tester l'URL ngrok
        console.log('🌐 URL ngrok: https://0e2a-41-231-66-207.ngrok-free.app/uploads/asmaa_cache/asmaa_fallback.mp3');
        
    } catch (error) {
        console.error('❌ Erreur lors de la création du fichier audio:', error.message);
        process.exit(1);
    }
}

// Exécuter si appelé directement
if (require.main === module) {
    createRealFallbackAudio();
}

module.exports = { createRealFallbackAudio };
