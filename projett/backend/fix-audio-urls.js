#!/usr/bin/env node

/**
 * Script pour corriger toutes les URLs audio dupliquées dans ChatbotController.js
 */

const fs = require('fs');
const path = require('path');

const CONTROLLER_FILE = path.join(__dirname, 'public/src/controllers/ChatbotController.js');

function fixAudioUrls() {
    console.log('🔧 Correction des URLs audio dupliquées...');
    
    if (!fs.existsSync(CONTROLLER_FILE)) {
        console.error('❌ Fichier ChatbotController.js non trouvé');
        return;
    }
    
    let content = fs.readFileSync(CONTROLLER_FILE, 'utf8');
    let changes = 0;
    
    // Pattern à remplacer : les blocs de code qui créent fullTtsPath
    const patterns = [
        // Pattern 1: let fullTtsPath = null; if (ttsPath) { ... }
        {
            search: /let fullTtsPath = null;\s*if \(ttsPath\) \{\s*const cleanPath = ttsPath\.startsWith\('\/uploads\/'\) \? ttsPath : `\/uploads\/\$\{ttsPath\}`;\s*fullTtsPath = `\$\{cleanPath\}&gender=female&v=\$\{Date\.now\(\)\}`;\s*\}/g,
            replace: 'const fullTtsPath = cleanAudioUrl(ttsPath);'
        },
        // Pattern 2: Construire le chemin complet avec cache-busting
        {
            search: /const cleanPath = ttsPath\.startsWith\('\/uploads\/'\) \? ttsPath : `\/uploads\/\$\{ttsPath\}`;\s*const fullTtsPath = `\$\{cleanPath\}&gender=female&v=\$\{Date\.now\(\)\}`;/g,
            replace: 'const fullTtsPath = cleanAudioUrl(ttsPath);'
        }
    ];
    
    patterns.forEach((pattern, index) => {
        const matches = content.match(pattern.search);
        if (matches) {
            console.log(`🔄 Pattern ${index + 1}: ${matches.length} occurrences trouvées`);
            content = content.replace(pattern.search, pattern.replace);
            changes += matches.length;
        }
    });
    
    // Corrections manuelles pour les cas spécifiques
    const manualFixes = [
        // Remplacer les patterns plus complexes
        {
            search: /let fullTtsPath = null;\s*if \(ttsPath\) \{\s*\/\/ Assurer que le chemin est correct\s*const cleanPath = ttsPath\.startsWith\('\/uploads\/'\) \? ttsPath : `\/uploads\/\$\{ttsPath\}`;\s*fullTtsPath = `\$\{cleanPath\}&gender=female&v=\$\{Date\.now\(\)\}`;\s*\}/g,
            replace: 'const fullTtsPath = cleanAudioUrl(ttsPath);'
        },
        {
            search: /\/\/ Assurer que le frontend reçoit un chemin complet qui fonctionne avec NGROK\s*let fullTtsPath = null;\s*if \(ttsPath\) \{\s*const cleanPath = ttsPath\.startsWith\('\/uploads\/'\) \? ttsPath : `\/uploads\/\$\{ttsPath\}`;\s*fullTtsPath = `\$\{cleanPath\}&gender=female&v=\$\{Date\.now\(\)\}`;\s*\}/g,
            replace: '// Nettoyer l\'URL audio pour éviter les duplications\n          const fullTtsPath = cleanAudioUrl(ttsPath);'
        }
    ];
    
    manualFixes.forEach((fix, index) => {
        const beforeLength = content.length;
        content = content.replace(fix.search, fix.replace);
        const afterLength = content.length;
        
        if (beforeLength !== afterLength) {
            console.log(`🔧 Correction manuelle ${index + 1} appliquée`);
            changes++;
        }
    });
    
    if (changes > 0) {
        // Sauvegarder le fichier modifié
        fs.writeFileSync(CONTROLLER_FILE, content, 'utf8');
        console.log(`✅ ${changes} corrections appliquées dans ChatbotController.js`);
    } else {
        console.log('ℹ️ Aucune correction nécessaire');
    }
}

// Exécuter si appelé directement
if (require.main === module) {
    fixAudioUrls();
}

module.exports = { fixAudioUrls };
