#!/usr/bin/env python3
"""
Script pour gu00e9nu00e9rer des exemples de recommandations pour le rapport sur le Sprint 2 ABAJIM.
Ce script gu00e9nu00e8re des donnu00e9es simulu00e9es pour illustrer le fonctionnement du moteur de recommandation.
"""

import json
import os
import random
from datetime import datetime, timedelta

# Cru00e9er le dossier de sortie
output_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "rapport_data")
os.makedirs(output_dir, exist_ok=True)

# Du00e9finir les scu00e9narios
SCENARIOS = [
    {
        "name": "u00c9lu00e8ve qui a visionnu00e9 plusieurs vidu00e9os d'un mu00eame professeur",
        "student_id": 12345,
        "level_id": 4,  # 3u00e8me
        "level_name": "3u00e8me",
        "matiere": "u0631u064au0627u0636u064au0627u062a",  # Mathu00e9matiques
        "matiere_fr": "Mathu00e9matiques",
        "description": "Un u00e9lu00e8ve de 3u00e8me qui a visionnu00e9 5 vidu00e9os du Prof. Ahmed Kader (mathu00e9matiques)"
    },
    {
        "name": "u00c9lu00e8ve ayant terminu00e9 un cours spu00e9cifique",
        "student_id": 23456,
        "level_id": 2,  # 1u00e8re
        "level_name": "1u00e8re",
        "matiere": "u0627u0644u0641u064au0632u064au0627u0621",  # Physique
        "matiere_fr": "Physique",
        "description": "Un u00e9lu00e8ve de 1u00e8re qui a terminu00e9 le cours 'Introduction u00e0 la mu00e9canique'"
    },
    {
        "name": "Nouvel utilisateur sans historique",
        "student_id": 34567,
        "level_id": 1,  # Terminale
        "level_name": "Terminale",
        "matiere": None,
        "matiere_fr": None,
        "description": "Un nouvel u00e9lu00e8ve de Terminale sans historique d'interactions"
    }
]

# Du00e9finir des enseignants simulu00e9s
TEACHERS = [
    {"id": 12, "nom": "Kader", "prenom": "Ahmed", "matiere": "u0631u064au0627u0636u064au0627u062a", "matiere_fr": "Mathu00e9matiques", "popularite": 85},
    {"id": 15, "nom": "Benali", "prenom": "Sophia", "matiere": "u0631u064au0627u0636u064au0627u062a", "matiere_fr": "Mathu00e9matiques", "popularite": 78},
    {"id": 23, "nom": "Hamdi", "prenom": "Rachid", "matiere": "u0627u0644u0641u064au0632u064au0627u0621", "matiere_fr": "Physique", "popularite": 82},
    {"id": 8, "nom": "Dubois", "prenom": "Marie", "matiere": "u0631u064au0627u0636u064au0627u062a", "matiere_fr": "Mathu00e9matiques", "popularite": 75},
    {"id": 31, "nom": "Hajri", "prenom": "Youssef", "matiere": "u0627u0644u0641u0631u0646u0633u064au0629", "matiere_fr": "Franu00e7ais", "popularite": 80},
    {"id": 42, "nom": "Ben Salem", "prenom": "Fatima", "matiere": "u0627u0644u0639u0631u0628u064au0629", "matiere_fr": "Arabe", "popularite": 88}
]

# Du00e9finir des cours simulu00e9s
COURSES = [
    {"id": 78, "titre": "Ru00e9solution d'u00e9quations du premier degru00e9", "prof_id": 12, "matiere": "u0631u064au0627u0636u064au0627u062a", "matiere_fr": "Mathu00e9matiques", "niveau": "3u00e8me", "popularite": 82},
    {"id": 81, "titre": "Factorisation et du00e9veloppement", "prof_id": 12, "matiere": "u0631u064au0627u0636u064au0627u062a", "matiere_fr": "Mathu00e9matiques", "niveau": "3u00e8me", "popularite": 78},
    {"id": 85, "titre": "Thu00e9oru00e8me de Pythagore", "prof_id": 12, "matiere": "u0631u064au0627u0636u064au0627u062a", "matiere_fr": "Mathu00e9matiques", "niveau": "3u00e8me", "popularite": 85},
    {"id": 92, "titre": "Gu00e9omu00e9trie dans l'espace", "prof_id": 12, "matiere": "u0631u064au0627u0636u064au0627u062a", "matiere_fr": "Mathu00e9matiques", "niveau": "3u00e8me", "popularite": 76},
    {"id": 98, "titre": "Systu00e8mes d'u00e9quations", "prof_id": 12, "matiere": "u0631u064au0627u0636u064au0627u062a", "matiere_fr": "Mathu00e9matiques", "niveau": "3u00e8me", "popularite": 79},
    {"id": 102, "titre": "u00c9quations du second degru00e9", "prof_id": 12, "matiere": "u0631u064au0627u0636u064au0627u062a", "matiere_fr": "Mathu00e9matiques", "niveau": "3u00e8me", "popularite": 84},
    {"id": 143, "titre": "Fonctions du00e9rivu00e9es", "prof_id": 15, "matiere": "u0631u064au0627u0636u064au0627u062a", "matiere_fr": "Mathu00e9matiques", "niveau": "3u00e8me", "popularite": 82},
    {"id": 87, "titre": "Forces et mouvement", "prof_id": 23, "matiere": "u0627u0644u0641u064au0632u064au0627u0621", "matiere_fr": "Physique", "niveau": "1u00e8re", "popularite": 88},
    {"id": 152, "titre": "Introduction u00e0 la mu00e9canique", "prof_id": 23, "matiere": "u0627u0644u0641u064au0632u064au0627u0621", "matiere_fr": "Physique", "niveau": "1u00e8re", "popularite": 90},
    {"id": 177, "titre": "Mu00e9canique des fluides", "prof_id": 23, "matiere": "u0627u0644u0641u064au0632u064au0627u0621", "matiere_fr": "Physique", "niveau": "1u00e8re", "popularite": 79}
]

# Du00e9finir des exercices simulu00e9s
EXERCISES = [
    {"id": 75, "titre": "Applications des u00e9quations", "matiere": "u0631u064au0627u0636u064au0627u062a", "matiere_fr": "Mathu00e9matiques", "niveau": "3u00e8me", "popularite": 85},
    {"id": 112, "titre": "Application des lois de Newton", "matiere": "u0627u0644u0641u064au0632u064au0627u0621", "matiere_fr": "Physique", "niveau": "1u00e8re", "popularite": 88},
    {"id": 48, "titre": "Problu00e8mes complexes d'u00e9quations", "matiere": "u0631u064au0627u0636u064au0627u062a", "matiere_fr": "Mathu00e9matiques", "niveau": "3u00e8me", "popularite": 76},
    {"id": 95, "titre": "Gu00e9omu00e9trie analytique", "matiere": "u0631u064au0627u0636u064au0627u062a", "matiere_fr": "Mathu00e9matiques", "niveau": "3u00e8me", "popularite": 82},
    {"id": 128, "titre": "Principe d'inertie", "matiere": "u0627u0644u0641u064au0632u064au0627u0621", "matiere_fr": "Physique", "niveau": "1u00e8re", "popularite": 80},
    {"id": 130, "titre": "Forces et u00e9quilibre", "matiere": "u0627u0644u0641u064au0632u064au0627u0621", "matiere_fr": "Physique", "niveau": "1u00e8re", "popularite": 85}
]

# Poids pour les composantes de score
WEIGHTS = {
    'content': 0.3,      # Poids du score de contenu
    'popularity': 0.2,   # Poids du score de popularitu00e9
    'interaction': 0.3,  # Poids du score d'interaction
    'similarity': 0.2    # Poids du score de similaritu00e9
}

# Scores pour les interactions
INTERACTION_SCORES = {
    'follow': 5,         # Score pour suivre un professeur
    'like': 3,           # Score pour aimer une vidu00e9o
    'view': 1            # Score pour visionner une vidu00e9o
}

# Fonctions pour générer les données d'exemple
def generate_user_profile(scenario):
    """Génère un profil utilisateur pour un scénario"""
    return {
        "student_id": scenario["student_id"],
        "level_id": scenario["level_id"],
        "level_name": scenario["level_name"],
        "matiere_dominante": scenario["matiere_fr"],
        "date_creation": (datetime.now() - timedelta(days=30)).strftime("%Y-%m-%d")
    }

def generate_interactions(scenario):
    """Génère des interactions simulées pour un scénario"""
    # Par défaut, pas d'interactions
    interactions = {
        "vues": [],
        "likes": [],
        "follows": []
    }
    
    # Scénario 1: Élève qui a visionné plusieurs vidéos du même professeur
    if scenario["student_id"] == 12345:
        # Trouver les cours du Prof. Ahmed Kader (id: 12)
        teacher_courses = [c for c in COURSES if c["prof_id"] == 12]
        
        # Générer des vues pour 5 vidéos
        for i, course in enumerate(teacher_courses[:5]):
            view_date = (datetime.now() - timedelta(days=15-i)).strftime("%Y-%m-%d")
            interactions["vues"].append({
                "id_cours": course["id"],
                "prof_id": course["prof_id"],
                "prof_nom": "Kader",
                "prof_prenom": "Ahmed",
                "date": view_date,
                "matiere": course["matiere"]
            })
        
        # Générer des likes pour certaines vidéos
        for i, course in enumerate(teacher_courses[:5]):
            if i % 2 == 0:  # Une vidéo sur deux
                like_date = (datetime.now() - timedelta(days=15-i)).strftime("%Y-%m-%d")
                interactions["likes"].append({
                    "id_cours": course["id"],
                    "prof_id": course["prof_id"],
                    "date": like_date
                })
        
        # Suivre le professeur
        follow_date = (datetime.now() - timedelta(days=15)).strftime("%Y-%m-%d")
        interactions["follows"].append({
            "prof_id": 12,
            "prof_nom": "Kader",
            "prof_prenom": "Ahmed",
            "date": follow_date
        })
    
    # Scénario 2: Élève ayant terminé un cours spécifique
    elif scenario["student_id"] == 23456:
        # Cours "Introduction à la mécanique" (id: 152)
        completed_course = next((c for c in COURSES if c["id"] == 152), None)
        if completed_course:
            # Vue du cours
            view_date = (datetime.now() - timedelta(days=5)).strftime("%Y-%m-%d")
            interactions["vues"].append({
                "id_cours": completed_course["id"],
                "prof_id": completed_course["prof_id"],
                "prof_nom": "Hamdi",
                "prof_prenom": "Rachid",
                "date": view_date,
                "matiere": completed_course["matiere"]
            })
            
            # Like du cours
            like_date = (datetime.now() - timedelta(days=5)).strftime("%Y-%m-%d")
            interactions["likes"].append({
                "id_cours": completed_course["id"],
                "prof_id": completed_course["prof_id"],
                "date": like_date
            })
    
    # Scénario 3: Nouvel utilisateur - pas d'interactions
    
    return interactions

def generate_similar_users(scenario):
    """Génère des utilisateurs similaires pour un scénario"""
    similar_users = []
    
    # Scénario 1: Élève qui a visionné plusieurs vidéos du même professeur
    if scenario["student_id"] == 12345:
        similar_users = [
            {"student_id": 12789, "score_similarite": 0.85, "raison": "Même niveau, même matière, suit aussi Prof. Kader"},
            {"student_id": 13254, "score_similarite": 0.72, "raison": "Même niveau, même matière, intérêt pour les mêmes cours"},
            {"student_id": 14001, "score_similarite": 0.68, "raison": "Même niveau, même matière, profil de visionnage similaire"}
        ]
    
    # Scénario 2: Élève ayant terminé un cours spécifique
    elif scenario["student_id"] == 23456:
        similar_users = [
            {"student_id": 23789, "score_similarite": 0.76, "raison": "Même niveau, même matière, a terminé le même cours"},
            {"student_id": 24567, "score_similarite": 0.65, "raison": "Même niveau, même matière, intérêt pour la physique"}
        ]
    
    # Scénario 3: Nouvel utilisateur - pas d'utilisateurs similaires car pas d'historique
    
    return similar_users

def recommend_teachers(scenario):
    """Génère des recommandations de professeurs pour un scénario"""
    recommendations = []
    
    # Scénario 1: Élève qui a visionné plusieurs vidéos du même professeur
    if scenario["student_id"] == 12345:
        # Recommander des profs de maths autres que Ahmed Kader
        math_teachers = [t for t in TEACHERS if t["matiere_fr"] == "Mathématiques" and t["id"] != 12]
        
        # Ajouter Sophia Benali avec un score élevé (suivi par des utilisateurs similaires)
        sophia = next((t for t in math_teachers if t["id"] == 15), None)
        if sophia:
            recommendations.append({
                "id": sophia["id"],
                "nom": sophia["nom"],
                "prenom": sophia["prenom"],
                "matiere": sophia["matiere_fr"],
                "score": 8.7,
                "detail_score": {
                    "relation": 3.2,  # Basé sur des interactions similaires
                    "popularite": 2.8, # Nombre d'abonnés et vues
                    "pertinence_matiere": 2.7 # Correspondance avec la matière dominante
                }
            })
        
        # Ajouter Rachid Hamdi (physique) comme diversification
        rachid = next((t for t in TEACHERS if t["id"] == 23), None)
        if rachid:
            recommendations.append({
                "id": rachid["id"],
                "nom": rachid["nom"],
                "prenom": rachid["prenom"],
                "matiere": rachid["matiere_fr"],
                "score": 7.9,
                "detail_score": {
                    "relation": 2.1,
                    "popularite": 3.4,
                    "pertinence_matiere": 2.4
                }
            })
        
        # Ajouter Marie Dubois (maths) avec un score légèrement inférieur
        marie = next((t for t in TEACHERS if t["id"] == 8), None)
        if marie:
            recommendations.append({
                "id": marie["id"],
                "nom": marie["nom"],
                "prenom": marie["prenom"],
                "matiere": marie["matiere_fr"],
                "score": 7.5,
                "detail_score": {
                    "relation": 2.8,
                    "popularite": 2.2,
                    "pertinence_matiere": 2.5
                }
            })
    
    # Scénario 2: Élève ayant terminé un cours spécifique
    elif scenario["student_id"] == 23456:
        # Recommander des profs de physique et matières connexes
        rachid = next((t for t in TEACHERS if t["id"] == 23), None)
        if rachid:
            recommendations.append({
                "id": rachid["id"],
                "nom": rachid["nom"],
                "prenom": rachid["prenom"],
                "matiere": rachid["matiere_fr"],
                "score": 9.2,
                "detail_score": {
                    "relation": 3.5,  # A déjà vu son cours
                    "popularite": 3.0,
                    "pertinence_matiere": 2.7
                }
            })
        
        # Ajouter d'autres professeurs variés
        sophia = next((t for t in TEACHERS if t["id"] == 15), None)
        if sophia:
            recommendations.append({
                "id": sophia["id"],
                "nom": sophia["nom"],
                "prenom": sophia["prenom"],
                "matiere": sophia["matiere_fr"],
                "score": 7.8,
                "detail_score": {
                    "relation": 2.0,
                    "popularite": 2.8,
                    "pertinence_matiere": 3.0
                }
            })
        
        youssef = next((t for t in TEACHERS if t["id"] == 31), None)
        if youssef:
            recommendations.append({
                "id": youssef["id"],
                "nom": youssef["nom"],
                "prenom": youssef["prenom"],
                "matiere": youssef["matiere_fr"],
                "score": 7.2,
                "detail_score": {
                    "relation": 1.8,
                    "popularite": 2.9,
                    "pertinence_matiere": 2.5
                }
            })
    
    # Scénario 3: Nouvel utilisateur sans historique
    else:
        # Recommander les professeurs les plus populaires
        sorted_teachers = sorted(TEACHERS, key=lambda t: t["popularite"], reverse=True)[:3]
        
        for i, teacher in enumerate(sorted_teachers):
            base_score = 8.5 - (i * 0.4)  # Décroissant: 8.5, 8.1, 7.7
            recommendations.append({
                "id": teacher["id"],
                "nom": teacher["nom"],
                "prenom": teacher["prenom"],
                "matiere": teacher["matiere_fr"],
                "score": round(base_score, 1),
                "detail_score": {
                    "relation": 0.0,  # Pas d'interactions
                    "popularite": round(base_score * 0.7, 1),  # Basé principalement sur popularité
                    "pertinence_matiere": round(base_score * 0.3, 1)  # Score générique
                }
            })
    
    return recommendations

def recommend_courses(scenario):
    """Génère des recommandations de cours pour un scénario"""
    recommendations = []
    
    # Scénario 1: Élève qui a visionné plusieurs vidéos du même professeur
    if scenario["student_id"] == 12345:
        # Cours "Équations du second degré" de Ahmed Kader
        equations = next((c for c in COURSES if c["id"] == 102), None)
        if equations:
            recommendations.append({
                "id": equations["id"],
                "titre": equations["titre"],
                "prof": "Ahmed Kader",
                "matiere": equations["matiere_fr"],
                "niveau": equations["niveau"],
                "score": 9.2,
                "detail_score": {
                    "popularite": 1.8,      # 0.2 * popularité normalisée
                    "pertinence_contenu": 2.7, # 0.3 * pertinence
                    "interaction_prof": 3.0,   # 0.3 * score d'interaction
                    "similarite": 1.7        # 0.2 * similarité
                }
            })
        
        # Cours "Forces et mouvement" de Rachid Hamdi
        forces = next((c for c in COURSES if c["id"] == 87), None)
        if forces:
            recommendations.append({
                "id": forces["id"],
                "titre": forces["titre"],
                "prof": "Rachid Hamdi",
                "matiere": forces["matiere_fr"],
                "niveau": forces["niveau"],
                "score": 8.5,
                "detail_score": {
                    "popularite": 1.6,
                    "pertinence_contenu": 2.7,
                    "interaction_prof": 2.4,
                    "similarite": 1.8
                }
            })
        
        # Cours "Fonctions dérivées" de Sophia Benali
        fonctions = next((c for c in COURSES if c["id"] == 143), None)
        if fonctions:
            recommendations.append({
                "id": fonctions["id"],
                "titre": fonctions["titre"],
                "prof": "Sophia Benali",
                "matiere": fonctions["matiere_fr"],
                "niveau": fonctions["niveau"],
                "score": 8.3,
                "detail_score": {
                    "popularite": 1.5,
                    "pertinence_contenu": 2.4,
                    "interaction_prof": 2.7,
                    "similarite": 1.7
                }
            })
    
    # Scénario 2: Élève ayant terminé un cours spécifique
    elif scenario["student_id"] == 23456:
        # Recommander la suite logique après "Introduction à la mécanique"
        forces = next((c for c in COURSES if c["id"] == 87), None)
        if forces:
            recommendations.append({
                "id": forces["id"],
                "titre": forces["titre"],
                "prof": "Rachid Hamdi",
                "matiere": forces["matiere_fr"],
                "niveau": forces["niveau"],
                "score": 9.5,
                "detail_score": {
                    "popularite": 1.9,
                    "pertinence_contenu": 3.0,  # Forte pertinence car suite logique
                    "interaction_prof": 2.8,
                    "similarite": 1.8
                }
            })
        
        # Cours "Mécanique des fluides" de Rachid Hamdi
        fluides = next((c for c in COURSES if c["id"] == 177), None)
        if fluides:
            recommendations.append({
                "id": fluides["id"],
                "titre": fluides["titre"],
                "prof": "Rachid Hamdi",
                "matiere": fluides["matiere_fr"],
                "niveau": fluides["niveau"],
                "score": 8.8,
                "detail_score": {
                    "popularite": 1.6,
                    "pertinence_contenu": 2.8,
                    "interaction_prof": 2.8,
                    "similarite": 1.6
                }
            })
        
        # Cours de mathématiques comme diversification
        equations = next((c for c in COURSES if c["id"] == 102), None)
        if equations:
            recommendations.append({
                "id": equations["id"],
                "titre": equations["titre"],
                "prof": "Ahmed Kader",
                "matiere": equations["matiere_fr"],
                "niveau": equations["niveau"],
                "score": 7.8,
                "detail_score": {
                    "popularite": 1.8,
                    "pertinence_contenu": 2.4,
                    "interaction_prof": 1.8,
                    "similarite": 1.8
                }
            })
    
    # Scénario 3: Nouvel utilisateur sans historique
    else:
        # Recommander les cours les plus populaires
        sorted_courses = sorted(COURSES, key=lambda c: c["popularite"], reverse=True)[:3]
        
        for i, course in enumerate(sorted_courses):
            teacher = next((t for t in TEACHERS if t["id"] == course["prof_id"]), None)
            teacher_name = f"{teacher['prenom']} {teacher['nom']}" if teacher else "Unknown"
            
            base_score = 8.6 - (i * 0.3)  # Décroissant: 8.6, 8.3, 8.0
            recommendations.append({
                "id": course["id"],
                "titre": course["titre"],
                "prof": teacher_name,
                "matiere": course["matiere_fr"],
                "niveau": course["niveau"],
                "score": round(base_score, 1),
                "detail_score": {
                    "popularite": round(base_score * WEIGHTS["popularity"] * 5, 1),  # Fortement pondéré
                    "pertinence_contenu": round(base_score * WEIGHTS["content"] * 3, 1),  # Modérément pondéré
                    "interaction_prof": 0.0,  # Pas d'interactions
                    "similarite": round(base_score * WEIGHTS["similarity"] * 2, 1)  # Faiblement pondéré
                }
            })
    
    return recommendations

def recommend_exercises(scenario):
    """Génère des recommandations d'exercices pour un scénario"""
    recommendations = []
    
    # Scénario 1: Élève qui a visionné plusieurs vidéos du même professeur
    if scenario["student_id"] == 12345:
        # Exercice "Applications des équations"
        applications = next((e for e in EXERCISES if e["id"] == 75), None)
        if applications:
            recommendations.append({
                "id": applications["id"],
                "titre": applications["titre"],
                "matiere": applications["matiere_fr"],
                "niveau": applications["niveau"],
                "score": 8.9,
                "detail_score": {
                    "pertinence_pedagogique": 2.7, # 0.3 * pertinence pédagogique
                    "interactions": 2.4,          # 0.3 * score interactions
                    "popularite": 1.8,            # 0.2 * popularité
                    "collaboratif": 2.0           # 0.2 * score collaboratif
                }
            })
        
        # Exercice "Problèmes complexes d'équations"
        problemes = next((e for e in EXERCISES if e["id"] == 48), None)
        if problemes:
            recommendations.append({
                "id": problemes["id"],
                "titre": problemes["titre"],
                "matiere": problemes["matiere_fr"],
                "niveau": problemes["niveau"],
                "score": 8.2,
                "detail_score": {
                    "pertinence_pedagogique": 2.4,
                    "interactions": 2.4,
                    "popularite": 1.6,
                    "collaboratif": 1.8
                }
            })
        
        # Exercice "Géométrie analytique"
        geometrie = next((e for e in EXERCISES if e["id"] == 95), None)
        if geometrie:
            recommendations.append({
                "id": geometrie["id"],
                "titre": geometrie["titre"],
                "matiere": geometrie["matiere_fr"],
                "niveau": geometrie["niveau"],
                "score": 7.9,
                "detail_score": {
                    "pertinence_pedagogique": 2.1,
                    "interactions": 2.2,
                    "popularite": 1.8,
                    "collaboratif": 1.8
                }
            })
    
    # Scénario 2: Élève ayant terminé un cours spécifique
    elif scenario["student_id"] == 23456:
        # Exercice directement lié au cours terminé
        newton = next((e for e in EXERCISES if e["id"] == 112), None)
        if newton:
            recommendations.append({
                "id": newton["id"],
                "titre": newton["titre"],
                "matiere": newton["matiere_fr"],
                "niveau": newton["niveau"],
                "score": 9.4,
                "detail_score": {
                    "pertinence_pedagogique": 3.0, # Très pertinent pour le cours terminé
                    "interactions": 2.4,
                    "popularite": 2.0,
                    "collaboratif": 2.0
                }
            })
        
        # Exercice "Principe d'inertie"
        inertie = next((e for e in EXERCISES if e["id"] == 128), None)
        if inertie:
            recommendations.append({
                "id": inertie["id"],
                "titre": inertie["titre"],
                "matiere": inertie["matiere_fr"],
                "niveau": inertie["niveau"],
                "score": 8.7,
                "detail_score": {
                    "pertinence_pedagogique": 2.7,
                    "interactions": 2.2,
                    "popularite": 1.8,
                    "collaboratif": 2.0
                }
            })
        
        # Exercice "Forces et équilibre"
        equilibre = next((e for e in EXERCISES if e["id"] == 130), None)
        if equilibre:
            recommendations.append({
                "id": equilibre["id"],
                "titre": equilibre["titre"],
                "matiere": equilibre["matiere_fr"],
                "niveau": equilibre["niveau"],
                "score": 8.5,
                "detail_score": {
                    "pertinence_pedagogique": 2.6,
                    "interactions": 2.1,
                    "popularite": 1.9,
                    "collaboratif": 1.9
                }
            })
    
    # Scénario 3: Nouvel utilisateur sans historique
    else:
        # Recommander les exercices les plus populaires
        sorted_exercises = sorted(EXERCISES, key=lambda e: e["popularite"], reverse=True)[:3]
        
        for i, exercise in enumerate(sorted_exercises):
            base_score = 8.4 - (i * 0.3)  # Décroissant: 8.4, 8.1, 7.8
            recommendations.append({
                "id": exercise["id"],
                "titre": exercise["titre"],
                "matiere": exercise["matiere_fr"],
                "niveau": exercise["niveau"],
                "score": round(base_score, 1),
                "detail_score": {
                    "pertinence_pedagogique": round(base_score * 0.3, 1),
                    "interactions": 0.0,  # Pas d'interactions
                    "popularite": round(base_score * 0.5, 1),  # Principalement basé sur popularité
                    "collaboratif": round(base_score * 0.2, 1)
                }
            })
    
    return recommendations

def generate_example_data(scenario):
    """Génère des données d'exemple pour un scénario"""
    # Profil utilisateur
    user_profile = generate_user_profile(scenario)
    
    # Historique des interactions
    interactions = generate_interactions(scenario)
    
    # Utilisateurs similaires
    similar_users = generate_similar_users(scenario)
    
    # Recommandations
    teacher_recommendations = recommend_teachers(scenario)
    course_recommendations = recommend_courses(scenario)
    exercise_recommendations = recommend_exercises(scenario)
    
    # Compiler toutes les données
    return {
        "profil": user_profile,
        "interactions": interactions,
        "utilisateurs_similaires": similar_users,
        "recommandations": {
            "enseignants": teacher_recommendations,
            "cours": course_recommendations,
            "exercices": exercise_recommendations
        }
    }

# Fonction principale
def main():
    """Fonction principale"""
    print("\n" + "=" * 80)
    print(f" Génération d'exemples de recommandation pour le rapport ".center(80, "="))
    print("=" * 80)
    
    # Générer des exemples pour chaque scénario
    for scenario in SCENARIOS:
        print(f"\nTraitement du scénario: {scenario['name']}")
        print(f"Description: {scenario['description']}")
        
        # Générer l'exemple détaillé
        print("Génération de l'exemple détaillé...")
        example_data = generate_example_data(scenario)
        
        # Enregistrer les données au format JSON
        output_file = os.path.join(output_dir, f"{scenario['name'].replace(' ', '_').lower()}.json")
        with open(output_file, "w", encoding="utf-8") as f:
            json.dump(example_data, f, ensure_ascii=False, indent=2)
        
        print(f"Données enregistrées dans {output_file}")
        
        # Afficher un résumé
        print("\nRésumé des recommandations:")
        print(f"  - Enseignants: {len(example_data['recommandations']['enseignants'])} recommandations")
        print(f"  - Cours: {len(example_data['recommandations']['cours'])} recommandations")
        print(f"  - Exercices: {len(example_data['recommandations']['exercices'])} recommandations")
    
    print("\nToutes les données ont été générées avec succès !")
    print(f"Les fichiers JSON sont disponibles dans le dossier: {output_dir}")

if __name__ == "__main__":
    main()
