#!/usr/bin/env node

/**
 * Script pour prévisualiser les données de la nouvelle carte de cours améliorée
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:5001';

// Couleurs pour les logs
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
  magenta: '\x1b[35m',
  reset: '\x1b[0m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

async function previewCourseCard() {
  log('🎨 Prévisualisation de la nouvelle carte de cours améliorée', 'blue');
  log('=' * 60, 'blue');

  try {
    // Obtenir une recommandation de cours
    const response = await axios.post(`${BASE_URL}/api/chatbot/ask`, {
      message: 'اقترح لي دورة في الرياضيات',
      userId: 3707,
      childId: 3707
    });

    if (!response.data.courseData) {
      log('❌ Aucune donnée de cours trouvée', 'red');
      return;
    }

    const course = response.data.courseData;
    
    log('\n📚 Données du cours pour la nouvelle carte:', 'cyan');
    log('─'.repeat(50), 'cyan');
    
    // Titre et informations de base
    log(`📖 Titre: ${course.title}`, 'green');
    log(`🆔 ID: ${course.id}`, 'green');
    log(`🔗 Slug: ${course.slug}`, 'green');
    
    // Image et prix
    log(`🖼️  Image: ${course.image_cover ? '✅ Disponible' : '❌ Non disponible'}`, course.image_cover ? 'green' : 'yellow');
    if (course.image_cover) {
      log(`   URL: https://www.abajim.com/${course.image_cover}`, 'cyan');
    }
    log(`💰 Prix: ${course.price > 0 ? `${course.price} د.ت` : 'مجاني'}`, course.price > 0 ? 'yellow' : 'green');
    
    // Enseignant
    log(`👨‍🏫 Enseignant:`, 'magenta');
    if (course.teacher) {
      log(`   📛 Nom: ${course.teacher.name || course.teacher.full_name}`, 'green');
      log(`   🆔 ID: ${course.teacher.id}`, 'green');
      log(`   🖼️  Avatar: ${course.teacher.avatar ? '✅ Disponible' : '❌ Non disponible'}`, course.teacher.avatar ? 'green' : 'yellow');
    } else {
      log(`   ❌ Aucune information d'enseignant`, 'red');
    }
    
    // Chapitres et contenu
    log(`📚 Chapitres:`, 'magenta');
    if (course.chapters && Array.isArray(course.chapters)) {
      log(`   📊 Nombre: ${course.chapters.length}`, 'green');
      course.chapters.forEach((chapter, index) => {
        log(`   📖 Chapitre ${index + 1}: ID ${chapter.id}`, 'cyan');
        if (chapter.files && Array.isArray(chapter.files)) {
          log(`      📁 Fichiers: ${chapter.files.length}`, 'cyan');
          chapter.files.forEach((file, fileIndex) => {
            log(`         🎥 Fichier ${fileIndex + 1}: ${file.translations?.[0]?.title || 'Sans titre'}`, 'cyan');
          });
        }
      });
    } else {
      log(`   ❌ Aucun chapitre trouvé`, 'red');
    }
    
    // Métadonnées
    log(`📊 Métadonnées:`, 'magenta');
    log(`   ⏱️  Durée: ${course.duration || 'Non spécifiée'}`, course.duration ? 'green' : 'yellow');
    log(`   📝 Description: ${course.description ? '✅ Disponible' : '❌ Non disponible'}`, course.description ? 'green' : 'yellow');
    log(`   ⭐ Score: ${course.score ? course.score.toFixed(1) : 'Non disponible'}`, course.score ? 'green' : 'yellow');
    log(`   🎯 Type: ${course.type}`, 'green');
    log(`   📊 Statut: ${course.status}`, 'green');
    
    // Informations de recommandation
    log(`🎯 Informations de recommandation:`, 'magenta');
    log(`   📍 Index: ${response.data.index}/${response.data.total}`, 'green');
    log(`   🔑 Session ID: ${response.data.sessionId}`, 'green');
    log(`   📱 Type: ${response.data.recommendationType}`, 'green');
    
    // Aperçu de la structure de la carte
    log('\n🎨 Structure de la nouvelle carte:', 'blue');
    log('─'.repeat(50), 'blue');
    log('┌─ 📱 CourseRecommendationCard', 'cyan');
    log('├─ 🖼️  Header (Image + Infos de base)', 'cyan');
    log('│  ├─ 🖼️  Image du cours (80x80px)', 'cyan');
    log('│  │  └─ 💰 Badge de prix (overlay)', 'cyan');
    log('│  └─ 📊 Informations du cours', 'cyan');
    log('│     ├─ 📖 Titre', 'cyan');
    log('│     ├─ 📊 Statistiques (chapitres, durée)', 'cyan');
    log('│     └─ 🏆 Badge de difficulté', 'cyan');
    log('├─ 👨‍🏫 Section enseignant (cliquable)', 'cyan');
    log('│  ├─ 🖼️  Avatar enseignant (32x32px)', 'cyan');
    log('│  └─ 📛 Nom enseignant', 'cyan');
    log('├─ 📝 Description (si disponible)', 'cyan');
    log('├─ ⭐ Score de recommandation', 'cyan');
    log('└─ 🎬 Bouton "بدء الدورة"', 'cyan');
    
    // Améliorations apportées
    log('\n✨ Améliorations apportées:', 'green');
    log('─'.repeat(50), 'green');
    log('✅ Design cohérent avec les autres cartes', 'green');
    log('✅ Image du cours plus grande et mieux positionnée', 'green');
    log('✅ Badge de prix en overlay sur l\'image', 'green');
    log('✅ Section enseignant séparée et cliquable', 'green');
    log('✅ Badge de difficulté basé sur le score', 'green');
    log('✅ Statistiques visuelles (chapitres, durée)', 'green');
    log('✅ Score de recommandation mis en évidence', 'green');
    log('✅ Bouton d\'action plus attrayant', 'green');
    log('✅ Ombres et bordures pour plus de profondeur', 'green');
    log('✅ Espacement et padding optimisés', 'green');
    
    log('\n🎉 La nouvelle carte de cours est prête !', 'green');
    log('📱 Elle s\'affichera automatiquement dans le chatbot', 'green');
    log('🔗 Navigation vers WebinarDetail fonctionnelle', 'green');
    
  } catch (error) {
    log(`❌ Erreur: ${error.message}`, 'red');
  }
}

// Exécuter la prévisualisation
if (require.main === module) {
  previewCourseCard()
    .then(() => {
      process.exit(0);
    })
    .catch(error => {
      log(`❌ Erreur fatale: ${error.message}`, 'red');
      process.exit(1);
    });
}

module.exports = { previewCourseCard };
