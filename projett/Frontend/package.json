{"name": "frontend", "version": "1.0.0", "main": "index.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@react-native-async-storage/async-storage": "2.1.2", "@react-navigation/native": "^7.1.9", "@react-navigation/native-stack": "^7.3.13", "@react-navigation/stack": "^7.3.2", "@reduxjs/toolkit": "^2.8.1", "axios": "^1.8.3", "dayjs": "^1.11.13", "expo": "^53.0.0", "expo-av": "~15.1.4", "expo-document-picker": "~13.1.5", "expo-image-picker": "~16.1.4", "expo-linear-gradient": "~14.1.4", "expo-speech": "^13.1.6", "expo-status-bar": "~2.2.3", "mime": "^4.0.6", "mime-types": "^2.1.35", "react": "19.0.0", "react-native": "^0.79.2", "react-native-chart-kit": "^6.12.0", "react-native-gesture-handler": "^2.25.0", "react-native-modal": "^14.0.0-rc.1", "react-native-reanimated": "^3.17.5", "react-native-safe-area-context": "^5.4.0", "react-native-screens": "^4.10.0", "react-native-vector-icons": "^10.2.0", "react-native-webview": "13.13.5", "react-redux": "^9.2.0", "expo-haptics": "~14.1.4", "expo-video": "~2.1.9"}, "devDependencies": {"@babel/core": "^7.20.0"}, "private": true}