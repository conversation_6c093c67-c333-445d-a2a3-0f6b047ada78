/**
 * Test simple pour vérifier que CourseListCard peut être importé
 * et que ses dépendances sont correctes
 */

const fs = require('fs');
const path = require('path');

function testCourseListCardImports() {
    console.log('🧪 TEST DU COMPOSANT COURSE LIST CARD');
    console.log('='.repeat(50));
    
    const cardPath = path.join(__dirname, 'src/components/CourseListCard.js');
    
    // Vérifier que le fichier existe
    if (!fs.existsSync(cardPath)) {
        console.log('❌ Fichier CourseListCard.js non trouvé');
        return false;
    }
    
    console.log('✅ Fichier CourseListCard.js trouvé');
    
    // Lire le contenu du fichier
    const content = fs.readFileSync(cardPath, 'utf8');
    
    // Vérifier les imports essentiels
    const requiredImports = [
        'React',
        'View',
        'Text',
        'TouchableOpacity',
        'ScrollView',
        'Image',
        'useNavigation',
        'Ionicons',
        'logChildActivity'
    ];
    
    console.log('\n📋 VÉRIFICATION DES IMPORTS:');
    let allImportsPresent = true;
    
    requiredImports.forEach(importName => {
        const isPresent = content.includes(importName);
        const status = isPresent ? '✅' : '❌';
        console.log(`${status} ${importName}: ${isPresent ? 'PRÉSENT' : 'MANQUANT'}`);
        if (!isPresent) allImportsPresent = false;
    });
    
    // Vérifier les fonctions essentielles
    const requiredFunctions = [
        'handleCoursePress',
        'getCourseImageUrl',
        'getInitials'
    ];
    
    console.log('\n🔧 VÉRIFICATION DES FONCTIONS:');
    let allFunctionsPresent = true;
    
    requiredFunctions.forEach(funcName => {
        const isPresent = content.includes(funcName);
        const status = isPresent ? '✅' : '❌';
        console.log(`${status} ${funcName}: ${isPresent ? 'PRÉSENT' : 'MANQUANT'}`);
        if (!isPresent) allFunctionsPresent = false;
    });
    
    // Vérifier les props essentielles
    const requiredProps = [
        'courses',
        'title',
        'onCoursePress'
    ];
    
    console.log('\n📝 VÉRIFICATION DES PROPS:');
    let allPropsPresent = true;
    
    requiredProps.forEach(propName => {
        const isPresent = content.includes(propName);
        const status = isPresent ? '✅' : '❌';
        console.log(`${status} ${propName}: ${isPresent ? 'PRÉSENT' : 'MANQUANT'}`);
        if (!isPresent) allPropsPresent = false;
    });
    
    // Vérifier la navigation
    const navigationChecks = [
        'navigation.navigate',
        'WebinarDetail',
        'webinarId'
    ];
    
    console.log('\n🧭 VÉRIFICATION DE LA NAVIGATION:');
    let navigationCorrect = true;
    
    navigationChecks.forEach(check => {
        const isPresent = content.includes(check);
        const status = isPresent ? '✅' : '❌';
        console.log(`${status} ${check}: ${isPresent ? 'PRÉSENT' : 'MANQUANT'}`);
        if (!isPresent) navigationCorrect = false;
    });
    
    // Vérifier les styles
    const styleChecks = [
        'StyleSheet.create',
        'container',
        'header',
        'coursesList',
        'courseItem'
    ];
    
    console.log('\n🎨 VÉRIFICATION DES STYLES:');
    let stylesCorrect = true;
    
    styleChecks.forEach(check => {
        const isPresent = content.includes(check);
        const status = isPresent ? '✅' : '❌';
        console.log(`${status} ${check}: ${isPresent ? 'PRÉSENT' : 'MANQUANT'}`);
        if (!isPresent) stylesCorrect = false;
    });
    
    // Résultat final
    console.log('\n🏁 RÉSULTAT FINAL:');
    
    if (allImportsPresent && allFunctionsPresent && allPropsPresent && navigationCorrect && stylesCorrect) {
        console.log('🎉 TOUS LES TESTS RÉUSSIS !');
        console.log('✅ Le composant CourseListCard est correctement structuré');
        console.log('✅ Tous les imports sont présents');
        console.log('✅ Toutes les fonctions sont définies');
        console.log('✅ Toutes les props sont utilisées');
        console.log('✅ La navigation est configurée');
        console.log('✅ Les styles sont définis');
        return true;
    } else {
        console.log('❌ CERTAINS TESTS ONT ÉCHOUÉ');
        if (!allImportsPresent) console.log('❌ Imports manquants');
        if (!allFunctionsPresent) console.log('❌ Fonctions manquantes');
        if (!allPropsPresent) console.log('❌ Props manquantes');
        if (!navigationCorrect) console.log('❌ Navigation incorrecte');
        if (!stylesCorrect) console.log('❌ Styles manquants');
        return false;
    }
}

// Vérifier aussi que ChatMessage.js importe bien CourseListCard
function testChatMessageImport() {
    console.log('\n🔗 TEST DE L\'IMPORT DANS CHATMESSAGE');
    console.log('='.repeat(50));
    
    const chatMessagePath = path.join(__dirname, 'src/components/ChatMessage.js');
    
    if (!fs.existsSync(chatMessagePath)) {
        console.log('❌ Fichier ChatMessage.js non trouvé');
        return false;
    }
    
    const content = fs.readFileSync(chatMessagePath, 'utf8');
    
    // Vérifier l'import
    const hasImport = content.includes("import CourseListCard from './CourseListCard'");
    console.log(`${hasImport ? '✅' : '❌'} Import CourseListCard: ${hasImport ? 'PRÉSENT' : 'MANQUANT'}`);
    
    // Vérifier la détection
    const hasDetection = content.includes('isCourseList');
    console.log(`${hasDetection ? '✅' : '❌'} Détection isCourseList: ${hasDetection ? 'PRÉSENT' : 'MANQUANT'}`);
    
    // Vérifier l'utilisation
    const hasUsage = content.includes('<CourseListCard');
    console.log(`${hasUsage ? '✅' : '❌'} Utilisation CourseListCard: ${hasUsage ? 'PRÉSENT' : 'MANQUANT'}`);
    
    // Vérifier les props
    const hasCoursesProps = content.includes('courses={message.courseListData.courses}');
    console.log(`${hasCoursesProps ? '✅' : '❌'} Props courses: ${hasCoursesProps ? 'PRÉSENT' : 'MANQUANT'}`);
    
    const hasTitleProps = content.includes('title={message.courseListData.title}');
    console.log(`${hasTitleProps ? '✅' : '❌'} Props title: ${hasTitleProps ? 'PRÉSENT' : 'MANQUANT'}`);
    
    if (hasImport && hasDetection && hasUsage && hasCoursesProps && hasTitleProps) {
        console.log('\n✅ INTÉGRATION DANS CHATMESSAGE RÉUSSIE !');
        return true;
    } else {
        console.log('\n❌ PROBLÈMES D\'INTÉGRATION DÉTECTÉS');
        return false;
    }
}

// Exécuter les tests
if (require.main === module) {
    const cardTest = testCourseListCardImports();
    const integrationTest = testChatMessageImport();
    
    console.log('\n' + '='.repeat(50));
    console.log('📊 RÉSUMÉ FINAL:');
    console.log(`${cardTest ? '✅' : '❌'} Composant CourseListCard: ${cardTest ? 'OK' : 'PROBLÈME'}`);
    console.log(`${integrationTest ? '✅' : '❌'} Intégration ChatMessage: ${integrationTest ? 'OK' : 'PROBLÈME'}`);
    
    if (cardTest && integrationTest) {
        console.log('\n🎉 TOUS LES TESTS FRONTEND RÉUSSIS !');
        console.log('✅ Le système de navigation intelligente est prêt');
    } else {
        console.log('\n⚠️ CORRECTIONS NÉCESSAIRES');
    }
}

module.exports = { testCourseListCardImports, testChatMessageImport };
