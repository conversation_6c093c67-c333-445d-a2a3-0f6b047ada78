import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView, Image } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { logChildActivity } from '../services/childActivityService';

const CourseListCard = ({ courses, title, onCoursePress }) => {
  const navigation = useNavigation();

  if (!courses || courses.length === 0) {
    return null;
  }

  const handleCoursePress = (course) => {
    if (onCoursePress) {
      onCoursePress(course);
    }

    // Log l'activité
    logChildActivity({
      action_type: "webinar",
      reference_id: course.id,
    });

    console.log('🎯 Navigation vers le cours depuis CourseListCard:', course.title);
    console.log('🔗 ID du cours:', course.id);

    // Naviguer vers les détails du cours
    navigation.navigate("WebinarDetail", { webinarId: course.id });
  };

  const getCourseImageUrl = (course) => {
    if (!course.image) return null;

    if (course.image.startsWith('http')) {
      return course.image;
    }

    return `https://www.abajim.com/${course.image.startsWith('/') ? course.image.slice(1) : course.image}`;
  };

  const getInitials = (fullName) => {
    if (!fullName) return "؟";
    const names = fullName.trim().split(" ");
    return names.length >= 2
      ? (names[0][0] + names[1][0]).toUpperCase()
      : names[0].slice(0, 2).toUpperCase();
  };

  return (
    <View style={styles.container}>
      {/* En-tête de la carte */}
      <View style={styles.header}>
        <View style={styles.headerContent}>
          <Ionicons name="library" size={20} color="#0097A7" />
          <Text style={styles.headerTitle}>
            {title || `${courses.length} دروس متطابقة`}
          </Text>
        </View>
        <View style={styles.badge}>
          <Text style={styles.badgeText}>{courses.length}</Text>
        </View>
      </View>

      {/* Liste des cours */}
      <ScrollView 
        style={styles.coursesList}
        showsVerticalScrollIndicator={false}
        nestedScrollEnabled={true}
      >
        {courses.map((course, index) => (
          <TouchableOpacity
            key={course.id || index}
            style={[
              styles.courseItem,
              index === courses.length - 1 && styles.lastCourseItem
            ]}
            onPress={() => handleCoursePress(course)}
            activeOpacity={0.8}
          >
            {/* Image du cours */}
            <View style={styles.courseImageContainer}>
              {getCourseImageUrl(course) ? (
                <Image
                  source={{ uri: getCourseImageUrl(course) }}
                  style={styles.courseImage}
                  resizeMode="cover"
                />
              ) : (
                <View style={styles.placeholderImage}>
                  <Ionicons name="play-circle" size={24} color="#0097A7" />
                </View>
              )}
            </View>

            {/* Contenu du cours */}
            <View style={styles.courseContent}>
              <Text style={styles.courseTitle} numberOfLines={2}>
                {course.title}
              </Text>
              
              {course.teacher && (
                <View style={styles.teacherInfo}>
                  <View style={styles.teacherAvatar}>
                    <Text style={styles.teacherInitials}>
                      {getInitials(course.teacher)}
                    </Text>
                  </View>
                  <Text style={styles.teacherName} numberOfLines={1}>
                    {course.teacher}
                  </Text>
                </View>
              )}

              {/* Score de correspondance si disponible */}
              {course.score && (
                <View style={styles.scoreContainer}>
                  <Ionicons name="checkmark-circle" size={14} color="#4CAF50" />
                  <Text style={styles.scoreText}>
                    {Math.round(course.score * 100)}% مطابق
                  </Text>
                </View>
              )}
            </View>

            {/* Flèche de navigation */}
            <View style={styles.arrowContainer}>
              <Ionicons name="chevron-forward" size={20} color="#0097A7" />
            </View>
          </TouchableOpacity>
        ))}
      </ScrollView>

      {/* Pied de carte */}
      <View style={styles.footer}>
        <Text style={styles.footerText}>
          اضغط على أي درس لبدء المشاهدة
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    marginTop: 8,
    marginHorizontal: 4,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    borderWidth: 1,
    borderColor: '#E8F4F8',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  headerTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
    marginLeft: 8,
    textAlign: 'right',
  },
  badge: {
    backgroundColor: '#0097A7',
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 4,
    minWidth: 24,
    alignItems: 'center',
  },
  badgeText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: 'bold',
  },
  coursesList: {
    maxHeight: 300, // Limiter la hauteur pour éviter que la carte soit trop grande
  },
  courseItem: {
    flexDirection: 'row',
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F5F5F5',
    alignItems: 'center',
  },
  lastCourseItem: {
    borderBottomWidth: 0,
  },
  courseImageContainer: {
    width: 50,
    height: 50,
    borderRadius: 8,
    overflow: 'hidden',
    marginRight: 12,
  },
  courseImage: {
    width: '100%',
    height: '100%',
  },
  placeholderImage: {
    width: '100%',
    height: '100%',
    backgroundColor: '#F0F8FF',
    justifyContent: 'center',
    alignItems: 'center',
  },
  courseContent: {
    flex: 1,
    paddingRight: 8,
  },
  courseTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333333',
    textAlign: 'right',
    marginBottom: 4,
  },
  teacherInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4,
  },
  teacherAvatar: {
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: '#E8F4F8',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 6,
  },
  teacherInitials: {
    fontSize: 8,
    fontWeight: 'bold',
    color: '#0097A7',
  },
  teacherName: {
    fontSize: 12,
    color: '#666666',
    flex: 1,
    textAlign: 'right',
  },
  scoreContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4,
  },
  scoreText: {
    fontSize: 11,
    color: '#4CAF50',
    marginLeft: 4,
    fontWeight: '500',
  },
  arrowContainer: {
    padding: 4,
  },
  footer: {
    padding: 12,
    backgroundColor: '#F8F9FA',
    borderBottomLeftRadius: 16,
    borderBottomRightRadius: 16,
  },
  footerText: {
    fontSize: 12,
    color: '#666666',
    textAlign: 'center',
    fontStyle: 'italic',
  },
});

export default CourseListCard;
