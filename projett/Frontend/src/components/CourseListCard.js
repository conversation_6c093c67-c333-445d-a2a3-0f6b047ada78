import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView, Image, Animated } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { logChildActivity } from '../services/activityLogger';

const CourseListCard = ({ courses, title, onCoursePress }) => {
  const navigation = useNavigation();

  // Debug: Log des données reçues
  console.log('🎯 CourseListCard - Données reçues:', {
    coursesLength: courses?.length,
    title: title,
    courses: courses?.map(course => ({
      id: course.id,
      title: course.title,
      teacher: course.teacher,
      score: course.score
    }))
  });

  if (!courses || courses.length === 0) {
    return null;
  }

  const handleCoursePress = (course) => {
    if (onCoursePress) {
      onCoursePress(course);
    }

    // Log l'activité
    logChildActivity({
      action_type: "webinar",
      reference_id: course.id,
    });

    console.log('🎯 Navigation vers le cours depuis CourseListCard:', course.title);
    console.log('🔗 ID du cours:', course.id);

    // Naviguer vers les détails du cours
    navigation.navigate("WebinarDetail", { webinarId: course.id });
  };

  const getCourseImageUrl = (course) => {
    if (!course.image) return null;

    if (course.image.startsWith('http')) {
      return course.image;
    }

    return `https://www.abajim.com/${course.image.startsWith('/') ? course.image.slice(1) : course.image}`;
  };

  const getInitials = (fullName) => {
    if (!fullName) return "؟";
    const names = fullName.trim().split(" ");
    return names.length >= 2
      ? (names[0][0] + names[1][0]).toUpperCase()
      : names[0].slice(0, 2).toUpperCase();
  };

  return (
    <View style={styles.container}>
      {/* En-tête de la carte */}
      <View style={styles.header}>
        <View style={styles.headerContent}>
          <View style={styles.iconContainer}>
            <Ionicons name="library" size={24} color="#FFFFFF" />
          </View>
          <Text style={styles.headerTitle}>
            {title || `${courses.length} دروس متطابقة`}
          </Text>
        </View>
        <View style={styles.badge}>
          <Text style={styles.badgeText}>{courses.length}</Text>
        </View>
      </View>

      {/* Liste des cours */}
      <ScrollView 
        style={styles.coursesList}
        showsVerticalScrollIndicator={false}
        nestedScrollEnabled={true}
      >
        {courses.map((course, index) => {
          // Debug: Log pour chaque cours
          console.log(`🎯 Rendu cours ${index + 1}:`, {
            id: course.id,
            title: course.title,
            titleFallback: course.title || course.name || course.webinar_title || 'عنوان الدرس',
            teacher: course.teacher,
            teacherFallback: course.teacher || course.teacher_name,
            score: course.score
          });

          return (
            <TouchableOpacity
              key={course.id || index}
              style={[
                styles.courseItem,
                index === courses.length - 1 && styles.lastCourseItem
              ]}
              onPress={() => handleCoursePress(course)}
              activeOpacity={0.9}
              onPressIn={() => {
                // Animation de pression
              }}
              onPressOut={() => {
                // Animation de relâchement
              }}
            >
            {/* Image du cours */}
            <View style={styles.courseImageContainer}>
              {getCourseImageUrl(course) ? (
                <Image
                  source={{ uri: getCourseImageUrl(course) }}
                  style={styles.courseImage}
                  resizeMode="cover"
                />
              ) : (
                <View style={styles.placeholderImage}>
                  <Ionicons name="play-circle" size={28} color="#0097A7" />
                </View>
              )}
            </View>

            {/* Contenu du cours */}
            <View style={styles.courseContent}>
              {/* Titre du cours */}
              <View style={styles.titleSection}>
                <Text
                  style={styles.courseTitle}
                  numberOfLines={3}
                  ellipsizeMode="tail"
                >
                  {course.title || course.name || course.webinar_title || 'عنوان الدرس'}
                </Text>
              </View>

              {/* Informations supplémentaires */}
              <View style={styles.infoSection}>
                {(course.teacher || course.teacher_name) && (
                <View style={styles.teacherInfo}>
                  <View style={styles.teacherAvatar}>
                    <Text style={styles.teacherInitials}>
                      {getInitials(course.teacher || course.teacher_name)}
                    </Text>
                  </View>
                  <Text style={styles.teacherName} numberOfLines={1}>
                    {course.teacher || course.teacher_name}
                  </Text>
                </View>
              )}

                {/* Score de correspondance si disponible */}
                {course.score && (
                  <View style={styles.scoreContainer}>
                    <Ionicons name="checkmark-circle" size={14} color="#4CAF50" />
                    <Text style={styles.scoreText}>
                      {Math.round(course.score * 100)}% مطابق
                    </Text>
                  </View>
                )}
              </View>
            </View>

            {/* Flèche de navigation */}
            <View style={styles.arrowContainer}>
              <Ionicons name="chevron-forward" size={22} color="#0097A7" />
            </View>
          </TouchableOpacity>
          );
        })}
      </ScrollView>

      {/* Pied de carte */}
      <View style={styles.footer}>
        <Text style={styles.footerText}>
          اضغط على أي درس لبدء المشاهدة
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
    borderRadius: 20,
    marginTop: 12,
    marginHorizontal: 8,
    shadowColor: '#0097A7',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 6,
    borderWidth: 1,
    borderColor: 'rgba(0, 151, 167, 0.1)',
    overflow: 'hidden',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#0097A7',
    borderBottomWidth: 0,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  iconContainer: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: '#FFFFFF',
    marginLeft: 12,
    textAlign: 'right',
    flex: 1,
  },
  badge: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 16,
    paddingHorizontal: 12,
    paddingVertical: 6,
    minWidth: 32,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
  badgeText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: 'bold',
  },
  coursesList: {
    maxHeight: 320,
    paddingHorizontal: 4,
  },
  courseItem: {
    flexDirection: 'row',
    padding: 16,
    marginHorizontal: 12,
    marginVertical: 6,
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    alignItems: 'flex-start',
    minHeight: 120,
    shadowColor: '#0097A7',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.08,
    shadowRadius: 4,
    elevation: 3,
    borderWidth: 1,
    borderColor: 'rgba(0, 151, 167, 0.05)',
    transform: [{ scale: 1 }],
  },
  lastCourseItem: {
    marginBottom: 12,
  },
  courseImageContainer: {
    width: 60,
    height: 60,
    borderRadius: 16,
    overflow: 'hidden',
    marginRight: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
  },
  courseImage: {
    width: '100%',
    height: '100%',
  },
  placeholderImage: {
    width: '100%',
    height: '100%',
    backgroundColor: '#E8F4F8',
    justifyContent: 'center',
    alignItems: 'center',
  },
  courseContent: {
    flex: 1,
    paddingRight: 12,
    justifyContent: 'space-between',
    minHeight: 88,
  },
  titleSection: {
    flex: 1,
    marginBottom: 8,
  },
  courseTitle: {
    fontSize: 16,
    fontWeight: '700',
    color: '#1F3B64',
    textAlign: 'right',
    lineHeight: 22,
    flexWrap: 'wrap',
  },
  infoSection: {
    flexDirection: 'column',
    gap: 6,
  },
  teacherInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 6,
    backgroundColor: 'rgba(0, 151, 167, 0.05)',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  teacherAvatar: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: '#0097A7',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
    shadowColor: '#0097A7',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.3,
    shadowRadius: 2,
    elevation: 2,
  },
  teacherInitials: {
    fontSize: 10,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  teacherName: {
    fontSize: 13,
    color: '#0097A7',
    flex: 1,
    textAlign: 'right',
    fontWeight: '600',
  },
  scoreContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 6,
    backgroundColor: 'rgba(76, 175, 80, 0.1)',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 8,
    alignSelf: 'flex-end',
  },
  scoreText: {
    fontSize: 11,
    color: '#4CAF50',
    marginLeft: 4,
    fontWeight: '700',
  },
  arrowContainer: {
    padding: 8,
    backgroundColor: 'rgba(0, 151, 167, 0.1)',
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  footer: {
    padding: 16,
    backgroundColor: '#F8F9FA',
    borderBottomLeftRadius: 20,
    borderBottomRightRadius: 20,
    borderTopWidth: 1,
    borderTopColor: 'rgba(0, 151, 167, 0.1)',
  },
  footerText: {
    fontSize: 13,
    color: '#0097A7',
    textAlign: 'center',
    fontWeight: '600',
    fontStyle: 'italic',
  },
});

export default CourseListCard;
