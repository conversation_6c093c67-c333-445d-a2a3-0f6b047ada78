#!/usr/bin/env python3
"""
Test script to verify material name lookup in the recommendation system.
"""

import os
import sys
import logging

# Add the parent directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Import the necessary modules
from recommendation_service.common.db_connector import get_db_session, close_db_session
from recommendation_service.common.config import MANUAL_LEVEL_MAPPING
from recommendation_service.models import Material

def test_material_lookup():
    """
    Test material name lookup for different levels.
    """
    logger.info("Testing material name lookup")

    # Get database session
    session = get_db_session()

    try:
        # Test for each level
        for level_id, material_ids in MANUAL_LEVEL_MAPPING.items():
            logger.info(f"Testing level {level_id} with material IDs {material_ids}")

            # Get all materials for this level
            materials = session.query(Material).filter(
                Material.id.in_(material_ids)
            ).all()

            logger.info(f"Found {len(materials)} materials for level {level_id}")

            # Test lookup for each material
            for material in materials:
                logger.info(f"Testing lookup for material '{material.name}' (ID: {material.id})")

                # Look up material by name and level
                found_material = session.query(Material).filter(
                    Material.name == material.name,
                    Material.id.in_(material_ids)
                ).first()

                if found_material:
                    logger.info(f"✅ Successfully found material '{found_material.name}' (ID: {found_material.id})")
                else:
                    logger.error(f"❌ Failed to find material '{material.name}' (ID: {material.id})")

    finally:
        # Close database session
        close_db_session(session)

if __name__ == "__main__":
    test_material_lookup()
