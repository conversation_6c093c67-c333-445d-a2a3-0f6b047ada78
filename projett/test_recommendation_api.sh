#!/bin/bash
# Script pour tester l'API de recommandation

# Définir l'URL de l'API
API_URL="http://localhost:5000/api"

# Vérifier si curl est installé
if ! command -v curl &> /dev/null; then
    echo "❌ curl n'est pas installé. Veuillez l'installer avant de continuer."
    exit 1
fi

# Vérifier si jq est installé
if ! command -v jq &> /dev/null; then
    echo "⚠️ jq n'est pas installé. L'affichage des résultats sera moins lisible."
    JQ_AVAILABLE=false
else
    JQ_AVAILABLE=true
fi

# Fonction pour formater la sortie JSON
format_json() {
    if [ "$JQ_AVAILABLE" = true ]; then
        echo "$1" | jq .
    else
        echo "$1"
    fi
}

# Vérifier si l'API est disponible
echo "🔍 Vérification de la disponibilité de l'API..."
HEALTH_RESPONSE=$(curl -s "$API_URL/health")
if [ -z "$HEALTH_RESPONSE" ]; then
    echo "❌ L'API n'est pas disponible. Veuillez démarrer le service de recommandation."
    exit 1
fi

echo "✅ L'API est disponible."
format_json "$HEALTH_RESPONSE"

# Tester la recommandation de professeurs
echo -e "\n🧑‍🏫 Test de la recommandation de professeurs..."
TEACHERS_RESPONSE=$(curl -s "$API_URL/recommendations/teachers?student_id=3707&matiere_name=رياضيات&level_id=9&top_n=3")
echo "✅ Réponse reçue pour la recommandation de professeurs."
format_json "$TEACHERS_RESPONSE"

# Tester la recommandation d'exercices
echo -e "\n📝 Test de la recommandation d'exercices..."
EXERCISES_RESPONSE=$(curl -s "$API_URL/recommendations/exercises?student_id=3707&matiere_name=رياضيات&top_n=3")
echo "✅ Réponse reçue pour la recommandation d'exercices."
format_json "$EXERCISES_RESPONSE"

# Tester la recommandation de cours
echo -e "\n📚 Test de la recommandation de cours..."
COURSES_RESPONSE=$(curl -s "$API_URL/recommendations/courses?student_id=3707&matiere_name=رياضيات&top_n=3")
echo "✅ Réponse reçue pour la recommandation de cours."
format_json "$COURSES_RESPONSE"

echo -e "\n✅ Tests terminés."
