/**
 * Test de navigation vers VideoScreen avec des données d'exercice recommandé
 */

// Simuler les données d'exercice qui seraient passées depuis ExerciseRecommendationCard
const exerciseData = {
    id: 565,
    title: "تمرين بدون عنوان",
    description: "",
    thumbnail: null,
    video_url: "https://videos-abajim-1.s3.de.io.cloud.ovh.net/videos/1729100500_exercice 1 page 10 leçon 2 période 1 trimestre 1 4 éme math ‐ Réalisée avec Clipchamp.mp4",
    page: 10,
    manuel_id: 13,
    manuel_name: "رياضيات",
    teacher_id: 1318,
    teacher_name: "نسرين خواجة",
    views: 0,
    likes: 0,
    score: 15
};

// Simuler les paramètres de navigation
const navigationParams = {
    exerciseData: exerciseData,
    isRecommendedExercise: true
};

console.log('🎯 Test de navigation vers VideoScreen');
console.log('📱 Paramètres de navigation:', JSON.stringify(navigationParams, null, 2));

// Vérifier que toutes les propriétés nécessaires sont présentes
const requiredFields = ['id', 'video_url'];
const optionalFields = ['title', 'teacher_name', 'page', 'manuel_name'];

console.log('\n✅ Vérification des champs requis:');
requiredFields.forEach(field => {
    if (exerciseData[field]) {
        console.log(`  ✅ ${field}: ${exerciseData[field]}`);
    } else {
        console.log(`  ❌ ${field}: MANQUANT`);
    }
});

console.log('\n📋 Champs optionnels disponibles:');
optionalFields.forEach(field => {
    if (exerciseData[field]) {
        console.log(`  ✅ ${field}: ${exerciseData[field]}`);
    } else {
        console.log(`  ⚪ ${field}: Non défini`);
    }
});

// Simuler le processus de chargement dans VideoScreen
console.log('\n🎬 Simulation du chargement dans VideoScreen:');

// 1. Construire l'URL de la vidéo
let videoUrl = exerciseData.video_url || exerciseData.video;
if (videoUrl && !videoUrl.startsWith('http')) {
    videoUrl = `https://www.abajim.com/${videoUrl.startsWith('/') ? videoUrl.slice(1) : videoUrl}`;
}

console.log(`  📹 URL de la vidéo: ${videoUrl}`);

// 2. Préparer les données pour VideoScreen
const videoData = {
    id: exerciseData.id,
    url: videoUrl,
    title: exerciseData.titre || exerciseData.title,
    views: exerciseData.vues || exerciseData.views || 0,
    likes: exerciseData.likes || 0,
    teacher: exerciseData.teacher || null,
};

console.log('  📊 Données préparées pour VideoScreen:', JSON.stringify(videoData, null, 2));

// 3. Vérifier la validité de l'URL
if (videoUrl && videoUrl.startsWith('https://')) {
    console.log('  ✅ URL de vidéo valide');
} else {
    console.log('  ❌ URL de vidéo invalide ou manquante');
}

console.log('\n🎉 Test de navigation terminé avec succès!');
console.log('\n📝 Résumé:');
console.log('  - ExerciseRecommendationCard navigue vers VideoScreen avec exerciseData');
console.log('  - VideoScreen détecte isRecommendedExercise=true');
console.log('  - VideoScreen charge les données directement sans appel API');
console.log('  - L\'URL de la vidéo est construite et prête pour la lecture');
console.log('  - L\'enregistrement de vue sera effectué automatiquement');
