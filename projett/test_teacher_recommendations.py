#!/usr/bin/env python3
"""
Test script for teacher recommendations.
"""

import sys
import json
from recommendation_service import get_teacher_recommendations

def main():
    """Main function."""
    # Check command line arguments
    if len(sys.argv) < 3:
        print("Usage: python test_teacher_recommendations.py <matiere_name> <level_id> [student_id] [top_n]")
        print("Example: python test_teacher_recommendations.py 'رياضيات' 9 3707 5")
        return
    
    # Get command line arguments
    matiere_name = sys.argv[1]
    level_id = int(sys.argv[2])
    student_id = int(sys.argv[3]) if len(sys.argv) > 3 else None
    top_n = int(sys.argv[4]) if len(sys.argv) > 4 else 5
    
    # Get recommendations
    recommendations = get_teacher_recommendations(student_id, matiere_name, level_id, top_n)
    
    # Print recommendations
    print(f"Recommendations for student {student_id}, subject '{matiere_name}', level {level_id}:")
    print(json.dumps(recommendations, indent=2, ensure_ascii=False))
    print(f"Found {len(recommendations)} recommendations")

if __name__ == "__main__":
    main()
