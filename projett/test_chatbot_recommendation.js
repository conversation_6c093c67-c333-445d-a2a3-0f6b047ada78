/**
 * Script de test pour la recommandation de professeurs via le chatbot
 */

const RecommendationService = require('./backend/public/src/services/RecommendationService');
const ChatbotMobileService = require('./backend/public/src/services/ChatbotMobileService');
const db = require('./backend/public/src/models');

// Paramètres de test
const userId = 3707; // ID d'un étudiant
const matiereName = 'رياضيات'; // Mathématiques
const levelId = 9; // 4ème année

async function testRecommendation() {
  try {
    console.log(`🔍 Test de recommandation pour l'utilisateur ${userId}, matière '${matiereName}', niveau ${levelId}`);
    
    // Test direct du service de recommandation
    console.log('\n1. Test direct du service de recommandation:');
    const directRecommendations = await RecommendationService.recommendProf(userId, matiereName, levelId);
    console.log(`✅ ${directRecommendations.length} professeurs recommandés directement`);
    console.log('Premier professeur recommandé:', JSON.stringify(directRecommendations[0], null, 2));
    
    // Test via le service du chatbot
    console.log('\n2. Test via le service du chatbot:');
    const chatbotRecommendation = await ChatbotMobileService.recommander_professeur(userId, matiereName);
    console.log('Professeur recommandé via le chatbot:', JSON.stringify(chatbotRecommendation, null, 2));
    
    process.exit(0);
  } catch (error) {
    console.error('❌ Erreur lors du test:', error);
    process.exit(1);
  }
}

// Exécuter le test
testRecommendation();
