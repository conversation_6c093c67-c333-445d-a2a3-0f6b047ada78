"""
<PERSON><PERSON><PERSON> to check webinars in the database.
"""

from recommendation_service.common.db_connector import get_db_session, close_db_session
from recommendation_service.models import Webinar

def main():
    """Check webinars in the database."""
    session = get_db_session()
    
    try:
        # Get all webinars with level_id=9 and matiere_id=9
        webinars = session.query(Webinar).filter(
            Webinar.level_id == 9,
            Webinar.matiere_id == 9
        ).all()
        
        print(f'Found {len(webinars)} webinars with level_id=9 and matiere_id=9')
        
        for w in webinars:
            print(f'- Webinar ID: {w.id}, Status: {w.status}, Deleted: {w.deleted_at}')
    
    finally:
        close_db_session(session)

if __name__ == '__main__':
    main()
