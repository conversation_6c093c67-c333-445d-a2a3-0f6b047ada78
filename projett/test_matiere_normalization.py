#!/usr/bin/env python3
"""
Test script to verify matiere name normalization in the recommendation system.
"""

import os
import sys
import logging

# Add the parent directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Import the necessary modules
from recommendation_service.common.db_connector import get_db_session, close_db_session
from recommendation_service.common.config import MANUAL_LEVEL_MAPPING
from recommendation_service.models import Material
from recommendation_service.app import normalize_matiere_name

def test_matiere_normalization():
    """
    Test matiere name normalization for different inputs.
    """
    logger.info("Testing matiere name normalization")

    # Get database session
    session = get_db_session()

    try:
        # Test cases for different matiere names
        test_cases = [
            # Français
            ('فرنسية', 'الفرنسية'),
            ('فرنساوي', 'الفرنسية'),
            ('français', 'الفرنسية'),
            ('francais', 'الفرنسية'),
            ('french', 'الفرنسية'),
            
            # Mathématiques
            ('رياضيات', 'رياضيات'),
            ('رياضة', 'رياضيات'),
            ('رياض', 'رياضيات'),
            ('math', 'رياضيات'),
            
            # Arabe
            ('عربية', 'العربية'),
            ('عربي', 'العربية'),
            ('arabe', 'العربية'),
            
            # Sciences
            ('علوم', 'الإيقاظ العلمي'),
            ('إيقاظ', 'الإيقاظ العلمي'),
            ('science', 'الإيقاظ العلمي'),
            
            # Anglais
            ('إنجليزية', 'الإنجليزية'),
            ('انجليزي', 'الإنجليزية'),
            ('english', 'الإنجليزية'),
            
            # Sciences sociales
            ('اجتماعيات', 'المواد الاجتماعية'),
            ('social', 'المواد الاجتماعية'),
        ]

        # Test each case
        for input_name, expected_output in test_cases:
            normalized_name = normalize_matiere_name(input_name)
            if normalized_name == expected_output:
                logger.info(f"✅ '{input_name}' -> '{normalized_name}' (expected: '{expected_output}')")
            else:
                logger.error(f"❌ '{input_name}' -> '{normalized_name}' (expected: '{expected_output}')")

        # Now test that each normalized name exists in the database
        all_materials = session.query(Material).all()
        material_names = [material.name for material in all_materials]
        
        logger.info(f"Found {len(material_names)} materials in the database: {material_names}")
        
        for _, normalized_name in test_cases:
            if normalized_name in material_names:
                logger.info(f"✅ Material '{normalized_name}' exists in the database")
            else:
                logger.error(f"❌ Material '{normalized_name}' does not exist in the database")

    finally:
        # Close database session
        close_db_session(session)

if __name__ == "__main__":
    test_matiere_normalization()
