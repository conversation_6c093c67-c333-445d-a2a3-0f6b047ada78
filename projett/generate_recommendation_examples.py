#!/usr/bin/env python3
"""
Script pour générer des exemples de recommandations pour le rapport.
Ce script simule différents scénarios d'usage et produit des données détaillées
sur les recommandations générées par le moteur de recommandation ABAJIM.
"""

import json
import sys
import os
import random
from datetime import datetime, timedelta
from collections import defaultdict

# Ajouter le répertoire actuel au chemin Python
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

# Importer les fonctions de recommandation
from recommendation_service.teachers.recommender import get_teacher_recommendations
from recommendation_service.courses.recommender import recommend_courses
from recommendation_service.common.db_connector import get_db_session, close_db_session
from recommendation_service.common.config import (
    INTERACTION_SCORES, WEIGHTS, MATIERE_ID_TO_NAME, LEVEL_ID_TO_SCHOOL_LEVEL
)
from recommendation_service.models import (
    <PERSON>, Manuel, Material, UserView, UserLevel, Like, Follow,
    Webinar, WebinarTranslation, WebinarChapter, File, FileTranslation, User
)

# Configurations pour nos scénarios
SCENARIOS = {
    "scenario1": {
        "name": "Élève qui a visionné plusieurs vidéos d'un même professeur",
        "student_id": 12345,
        "level_id": 4,  # 3ème
        "matiere": "رياضيات",  # Mathématiques
        "description": "Un élève de 3ème qui a visionné 5 vidéos du Prof. Ahmed Kader (mathématiques)"
    },
    "scenario2": {
        "name": "Élève ayant terminé un cours spécifique",
        "student_id": 23456,
        "level_id": 2,  # 1ère
        "matiere": "الفيزياء",  # Physique
        "description": "Un élève de 1ère qui a terminé le cours 'Introduction à la mécanique'"
    },
    "scenario3": {
        "name": "Nouvel utilisateur sans historique",
        "student_id": 34567,
        "level_id": 1,  # Terminale
        "matiere": None,
        "description": "Un nouvel élève de Terminale sans historique d'interactions"
    }
}

def print_header(text):
    """Affiche un en-tête formaté"""
    print("\n" + "=" * 80)
    print(f" {text} ".center(80, "="))
    print("=" * 80)

def format_score_details(score_dict):
    """Formate les détails d'un score pour l'affichage"""
    return ", ".join([f"{k}: {v:.2f}" for k, v in score_dict.items()])

def get_user_interactions(session, student_id):
    """Récupère l'historique des interactions d'un utilisateur"""
    # Vues
    views = session.query(UserView).filter_by(user_id=student_id).all()
    view_data = []
    for view in views:
        video = session.query(Video).filter_by(id=view.video_id).first()
        if video:
            teacher = session.query(User).filter_by(id=video.user_id).first()
            manuel = session.query(Manuel).filter_by(id=video.manuel_id).first()
            material = None
            if manuel:
                material = session.query(Material).filter_by(id=manuel.material_id).first()
            
            view_data.append({
                "id_cours": video.id,
                "prof_id": video.user_id,
                "prof_nom": teacher.last_name if teacher else "Unknown",
                "prof_prenom": teacher.first_name if teacher else "Unknown",
                "date": view.updated_at.strftime("%Y-%m-%d"),
                "matiere": material.name if material else "Unknown"
            })
    
    # Likes
    likes = session.query(Like).filter_by(user_id=student_id).all()
    like_data = []
    for like in likes:
        video = session.query(Video).filter_by(id=like.video_id).first()
        if video:
            like_data.append({
                "id_cours": video.id,
                "prof_id": video.user_id,
                "date": like.updated_at.strftime("%Y-%m-%d")
            })
    
    # Follows
    follows = session.query(Follow).filter_by(follower=student_id).all()
    follow_data = []
    for follow in follows:
        teacher = session.query(User).filter_by(id=follow.user_id).first()
        if teacher:
            follow_data.append({
                "prof_id": teacher.id,
                "prof_nom": teacher.last_name,
                "prof_prenom": teacher.first_name,
                "date": follow.updated_at.strftime("%Y-%m-%d")
            })
    
    return {
        "vues": view_data,
        "likes": like_data,
        "follows": follow_data
    }

def simulate_interaction_data(scenario):
    """Simule des données d'interaction pour un scénario"""
    session = get_db_session()
    student_id = scenario["student_id"]
    level_id = scenario["level_id"]
    matiere_name = scenario["matiere"]
    
    try:
        # Vérifier si l'utilisateur existe déjà
        user = session.query(User).filter_by(id=student_id).first()
        if not user:
            # Créer un nouvel utilisateur
            user = User(
                id=student_id,
                first_name=f"User{student_id}",
                last_name="Test",
                email=f"user{student_id}@example.com",
                status=1,
                created_at=datetime.now(),
                updated_at=datetime.now()
            )
            session.add(user)
        
        # Définir le niveau de l'utilisateur
        user_level = session.query(UserLevel).filter_by(user_id=student_id).first()
        if not user_level:
            user_level = UserLevel(
                user_id=student_id,
                level_id=level_id,
                created_at=datetime.now(),
                updated_at=datetime.now()
            )
            session.add(user_level)
        else:
            user_level.level_id = level_id
            user_level.updated_at = datetime.now()
        
        # Si c'est le scénario 1 (plusieurs vidéos du même prof)
        if scenario["name"] == SCENARIOS["scenario1"]["name"]:
            # Trouver un professeur de mathématiques
            math_teachers = []
            materials = session.query(Material).filter_by(name="رياضيات").all()
            if materials:
                for material in materials:
                    manuels = session.query(Manuel).filter_by(material_id=material.id).all()
                    for manuel in manuels:
                        videos = session.query(Video).filter_by(manuel_id=manuel.id).all()
                        for video in videos:
                            if video.user_id not in [t["id"] for t in math_teachers]:
                                teacher = session.query(User).filter_by(id=video.user_id).first()
                                if teacher:
                                    math_teachers.append({
                                        "id": teacher.id,
                                        "name": f"{teacher.first_name} {teacher.last_name}"
                                    })
            
            # Si nous avons trouvé des professeurs de mathématiques
            if math_teachers:
                # Choisir le premier professeur
                selected_teacher = math_teachers[0]
                
                # Trouver ses vidéos
                teacher_videos = []
                for material in materials:
                    manuels = session.query(Manuel).filter_by(material_id=material.id).all()
                    for manuel in manuels:
                        videos = session.query(Video).filter_by(
                            manuel_id=manuel.id, 
                            user_id=selected_teacher["id"]
                        ).all()
                        teacher_videos.extend(videos)
                
                # Sélectionner 5 vidéos aléatoires (ou moins si pas assez)
                num_videos = min(5, len(teacher_videos))
                selected_videos = random.sample(teacher_videos, num_videos) if num_videos > 0 else []
                
                # Créer des vues pour ces vidéos
                for i, video in enumerate(selected_videos):
                    # Vérifier si la vue existe déjà
                    existing_view = session.query(UserView).filter_by(
                        user_id=student_id, 
                        video_id=video.id
                    ).first()
                    
                    if not existing_view:
                        # Créer une nouvelle vue
                        view_date = datetime.now() - timedelta(days=10-i)
                        user_view = UserView(
                            user_id=student_id,
                            video_id=video.id,
                            created_at=view_date,
                            updated_at=view_date
                        )
                        session.add(user_view)
                    
                    # Ajouter un like pour certaines vidéos
                    if i % 2 == 0:  # Like une vidéo sur deux
                        existing_like = session.query(Like).filter_by(
                            user_id=student_id, 
                            video_id=video.id
                        ).first()
                        
                        if not existing_like:
                            like_date = datetime.now() - timedelta(days=10-i)
                            like = Like(
                                user_id=student_id,
                                video_id=video.id,
                                created_at=like_date,
                                updated_at=like_date
                            )
                            session.add(like)
                
                # Suivre le professeur
                existing_follow = session.query(Follow).filter_by(
                    follower=student_id, 
                    user_id=selected_teacher["id"]
                ).first()
                
                if not existing_follow:
                    follow_date = datetime.now() - timedelta(days=10)
                    follow = Follow(
                        follower=student_id,
                        user_id=selected_teacher["id"],
                        created_at=follow_date,
                        updated_at=follow_date
                    )
                    session.add(follow)
        
        # Si c'est le scénario 2 (terminé un cours spécifique)
        elif scenario["name"] == SCENARIOS["scenario2"]["name"]:
            # Nous allons simuler qu'il a terminé un cours de physique
            # Trouver un cours de physique
            physics_webinars = []
            materials = session.query(Material).filter(Material.name.like("%فيزياء%")).all()
            if not materials:
                # Fallback sur d'autres matières scientifiques
                materials = session.query(Material).filter(Material.name.like("%علم%")).all()
            
            if materials:
                for material in materials:
                    webinars = session.query(Webinar).join(WebinarTranslation).filter(
                        WebinarTranslation.title.like("%مقدمة%") | 
                        WebinarTranslation.title.like("%ميكانيك%")
                    ).all()
                    physics_webinars.extend(webinars)
            
            # Si nous avons trouvé des webinars de physique
            if physics_webinars:
                # Choisir le premier webinar
                selected_webinar = physics_webinars[0] if physics_webinars else None
                
                if selected_webinar:
                    # Simuler la visualisation de tous les chapitres du webinar
                    chapters = session.query(WebinarChapter).filter_by(
                        webinar_id=selected_webinar.id
                    ).all()
                    
                    for chapter in chapters:
                        # Vérifier si la vue existe déjà
                        # Note: Ici nous simplifions car les vues de webinars sont gérées différemment
                        # que les vues de vidéos normales
                        pass
        
        # Pour le scénario 3 (nouvel utilisateur), nous ne faisons rien car il n'a pas d'historique
        
        session.commit()
        
    except Exception as e:
        print(f"Erreur lors de la simulation des données : {str(e)}")
        session.rollback()
    finally:
        close_db_session(session)

def get_user_profile(session, student_id):
    """Récupère le profil d'un utilisateur"""
    user = session.query(User).filter_by(id=student_id).first()
    user_level = session.query(UserLevel).filter_by(user_id=student_id).first()
    
    level_id = user_level.level_id if user_level else None
    level_name = LEVEL_ID_TO_SCHOOL_LEVEL.get(level_id, str(level_id)) if level_id else None
    
    # Déterminer la matière dominante
    matiere_dominante = None
    views = session.query(UserView).filter_by(user_id=student_id).all()
    
    if views:
        matiere_counts = defaultdict(int)
        for view in views:
            video = session.query(Video).filter_by(id=view.video_id).first()
            if video:
                manuel = session.query(Manuel).filter_by(id=video.manuel_id).first()
                if manuel:
                    material = session.query(Material).filter_by(id=manuel.material_id).first()
                    if material:
                        matiere_counts[material.name] += 1
        
        if matiere_counts:
            matiere_dominante = max(matiere_counts.items(), key=lambda x: x[1])[0]
    
    return {
        "student_id": student_id,
        "level_id": level_id,
        "level_name": level_name,
        "matiere_dominante": matiere_dominante,
        "date_creation": user.created_at.strftime("%Y-%m-%d") if user else None
    }

def get_similar_users(session, student_id):
    """Identifie des utilisateurs similaires à l'utilisateur spécifié"""
    # Cette fonction est une simulation simplifiée du processus réel de recherche d'utilisateurs similaires
    # Dans un système réel, on utiliserait des métriques de similarité plus avancées
    
    user_level = session.query(UserLevel).filter_by(user_id=student_id).first()
    if not user_level:
        return []
    
    # Trouver des utilisateurs avec le même niveau
    similar_users = []
    level_users = session.query(UserLevel).filter_by(level_id=user_level.level_id).all()
    
    for level_user in level_users:
        if level_user.user_id != student_id:
            similar_users.append({
                "student_id": level_user.user_id,
                "score_similarite": round(random.uniform(0.6, 0.9), 2),
                "raison": "Même niveau"
            })
    
    # Limiter à 3 utilisateurs similaires
    return similar_users[:3] if similar_users else []

def format_teacher_recommendations(recommendations):
    """Formate les recommandations d'enseignants pour l'affichage"""
    formatted_recommendations = []
    
    for teacher in recommendations:
        # Calculer les composantes du score
        total_score = teacher.get("score", 0)
        score_detail = {
            "relation": total_score * 0.3,  # Simulé: 30% du score total
            "popularite": total_score * 0.2,  # Simulé: 20% du score total
            "pertinence_matiere": total_score * 0.5  # Simulé: 50% du score total
        }
        
        formatted_recommendations.append({
            "id_prof": teacher.get("id"),
            "nom": teacher.get("last_name"),
            "prenom": teacher.get("first_name"),
            "matiere": teacher.get("subject"),
            "score_total": round(total_score, 1),
            "detail_score": {
                "relation": round(score_detail["relation"], 1),
                "popularite": round(score_detail["popularite"], 1),
                "pertinence_matiere": round(score_detail["pertinence_matiere"], 1)
            }
        })
    
    return formatted_recommendations

def format_course_recommendations(recommendations):
    """Formate les recommandations de cours pour l'affichage"""
    formatted_recommendations = []
    
    for course in recommendations:
        # Calculer les composantes du score
        total_score = course.get("score", 0)
        score_detail = {
            "popularite": total_score * WEIGHTS["popularity"],
            "pertinence_contenu": total_score * WEIGHTS["content"],
            "interaction_prof": total_score * WEIGHTS["interaction"],
            "similarite": total_score * WEIGHTS["similarity"]
        }
        
        formatted_recommendations.append({
            "id_cours": course.get("id"),
            "titre": course.get("title"),
            "prof": course.get("teacher_name"),
            "matiere": course.get("subject"),
            "niveau": course.get("level"),
            "score_total": round(total_score, 1),
            "detail_score": {
                "popularite": round(score_detail["popularite"], 1),
                "pertinence_contenu": round(score_detail["pertinence_contenu"], 1),
                "interaction_prof": round(score_detail["interaction_prof"], 1),
                "similarite": round(score_detail["similarite"], 1)
            }
        })
    
    return formatted_recommendations

def format_exercise_recommendations(recommendations, scores):
    """Formate les recommandations d'exercices pour l'affichage"""
    formatted_recommendations = []
    
    for i, video in enumerate(recommendations):
        score = scores[i] if i < len(scores) else 0
        
        # Calculer les composantes du score
        score_detail = {
            "pertinence_pedagogique": score * 0.3,
            "interactions": score * 0.3,
            "popularite": score * 0.2,
            "collaboratif": score * 0.2
        }
        
        formatted_recommendations.append({
            "id_exercice": video.id,
            "titre": video.title if hasattr(video, "title") else f"Exercice {video.id}",
            "matiere": MATIERE_ID_TO_NAME.get(video.manuel.material_id) if hasattr(video, "manuel") and video.manuel else "Unknown",
            "niveau": LEVEL_ID_TO_SCHOOL_LEVEL.get(video.level_id) if hasattr(video, "level_id") else "Unknown",
            "score_total": round(score, 1),
            "detail_score": {
                "pertinence_pedagogique": round(score_detail["pertinence_pedagogique"], 1),
                "interactions": round(score_detail["interactions"], 1),
                "popularite": round(score_detail["popularite"], 1),
                "collaboratif": round(score_detail["collaboratif"], 1)
            }
        })
    
    return formatted_recommendations

def generate_detailed_example(scenario):
    """Génère un exemple détaillé pour un scénario"""
    session = get_db_session()
    student_id = scenario["student_id"]
    level_id = scenario["level_id"]
    matiere_name = scenario["matiere"]
    
    try:
        # Récupérer le profil utilisateur
        user_profile = get_user_profile(session, student_id)
        
        # Récupérer l'historique des interactions
        interactions = get_user_interactions(session, student_id)
        
        # Récupérer les utilisateurs similaires
        similar_users = get_similar_users(session, student_id)
        
        # Récupérer les recommandations d'enseignants
        teacher_recommendations = get_teacher_recommendations(student_id, matiere_name, level_id, 3)
        formatted_teacher_recommendations = format_teacher_recommendations(teacher_recommendations)
        
        # Récupérer les recommandations de cours
        course_recommendations = recommend_courses(student_id, matiere_name, level_id, 3)
        formatted_course_recommendations = format_course_recommendations(course_recommendations)
        
        # Récupérer les recommandations d'exercices
        # Cette partie est simulée car il n'y a pas de fonction directe pour les exercices
        exercise_recommendations = []
        exercise_scores = []
        
        # Retourner toutes les données
        return {
            "profil": user_profile,
            "interactions": interactions,
            "utilisateurs_similaires": similar_users,
            "recommandations": {
                "enseignants": formatted_teacher_recommendations,
                "cours": formatted_course_recommendations,
                "exercices": exercise_recommendations
            }
        }
    
    except Exception as e:
        print(f"Erreur lors de la génération de l'exemple : {str(e)}")
        return None
    
    finally:
        close_db_session(session)

def main():
    """Fonction principale"""
    print_header("Génération d'exemples de recommandation pour le rapport")
    
    # Créer le dossier de sortie s'il n'existe pas
    output_dir = os.path.join(current_dir, "rapport_data")
    os.makedirs(output_dir, exist_ok=True)
    
    # Générer des exemples pour chaque scénario
    for scenario_id, scenario in SCENARIOS.items():
        print(f"\nTraitement du scénario: {scenario['name']}")
        print(f"Description: {scenario['description']}")
        
        # Simuler des données d'interaction
        print("Simulation des données d'interaction...")
        simulate_interaction_data(scenario)
        
        # Générer l'exemple détaillé
        print("Génération de l'exemple détaillé...")
        example_data = generate_detailed_example(scenario)
        
        if example_data:
            # Enregistrer les données au format JSON
            output_file = os.path.join(output_dir, f"{scenario_id}.json")
            with open(output_file, "w", encoding="utf-8") as f:
                json.dump(example_data, f, ensure_ascii=False, indent=2)
            
            print(f"Données enregistrées dans {output_file}")
            
            # Afficher un résumé
            print("\nRésumé des recommandations:")
            print(f"  - Enseignants: {len(example_data['recommandations']['enseignants'])} recommandations")
            print(f"  - Cours: {len(example_data['recommandations']['cours'])} recommandations")
            print(f"  - Exercices: {len(example_data['recommandations']['exercices'])} recommandations")
        else:
            print("Échec de la génération de l'exemple")
    
    print("\nToutes les données ont été générées avec succès !")
    print(f"Les fichiers JSON sont disponibles dans le dossier: {output_dir}")

if __name__ == "__main__":
    main()
