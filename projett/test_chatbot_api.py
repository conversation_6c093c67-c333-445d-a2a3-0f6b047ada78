#!/usr/bin/env python3
"""
Test script to verify the chatbot API with the new recommendation system.
"""

import requests
import json
import sys

def test_chatbot_api(user_id, message):
    """
    Test chatbot API with a message.

    Args:
        user_id (int): User ID
        message (str): Message to send to the chatbot
    """
    url = "http://localhost:5001/api/chatbot/ask"
    data = {
        'userId': user_id,
        'message': message
    }

    print(f"Testing chatbot API with user_id={user_id}, message='{message}'")

    try:
        response = requests.post(url, json=data)

        print(f"Response status code: {response.status_code}")

        if response.status_code == 200:
            data = response.json()
            print(f"Response: {data['reply']}")

            if 'professeurs' in data:
                print(f"Number of professors: {len(data['professeurs'])}")
                print("Professors:")
                for i, prof in enumerate(data['professeurs']):
                    print(f"  {i+1}. {prof['full_name']} (ID: {prof['id']}, Score: {prof['score']})")

            if 'audio' in data:
                print(f"Audio URL: {data['audio']}")
        else:
            print(f"Error: {response.text}")

    except Exception as e:
        print(f"Error: {str(e)}")

if __name__ == "__main__":
    # Test with a message asking for a math teacher recommendation
    test_chatbot_api(1, "اقترح لي أستاذ رياضيات")
    print("-" * 50)

    # Test with a message asking for a French teacher recommendation
    test_chatbot_api(1, "اقترح لي أستاذ فرنسية")
    print("-" * 50)

    # Test with a message asking for an Arabic teacher recommendation
    test_chatbot_api(1, "اقترح لي أستاذ عربية")
    print("-" * 50)
