/**
 * Script de test pour vérifier la navigation des exercices recommandés
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:5001/api';
const RECOMMENDATION_URL = 'http://localhost:8000/api';

// Test de recommandation d'exercices
async function testExerciseRecommendation() {
    try {
        console.log('🧪 Test de recommandation d\'exercices...');
        
        // Paramètres de test
        const userId = 3707;
        const matiereName = 'رياضيات';
        const levelId = 9;
        
        // 1. Test direct du service de recommandation
        console.log('\n📡 Test du service de recommandation...');
        const recResponse = await axios.get(`${RECOMMENDATION_URL}/recommendations/exercises`, {
            params: {
                student_id: userId,
                matiere_name: matiereName,
                level_id: levelId,
                top_n: 5
            }
        });
        
        console.log('✅ Réponse du service de recommandation:');
        console.log(JSON.stringify(recResponse.data, null, 2));
        
        // 2. Test du backend chatbot
        console.log('\n🤖 Test du backend chatbot...');
        const chatResponse = await axios.post(`${BASE_URL}/chatbot/mobile`, {
            message: 'أريد تمرينًا في الرياضيات',
            userId: userId
        });
        
        console.log('✅ Réponse du chatbot:');
        console.log(JSON.stringify(chatResponse.data, null, 2));
        
        // 3. Vérifier la structure des données d'exercice
        if (recResponse.data.recommendations && recResponse.data.recommendations.length > 0) {
            const exercise = recResponse.data.recommendations[0];
            console.log('\n📊 Structure de l\'exercice recommandé:');
            console.log({
                id: exercise.id,
                title: exercise.titre || exercise.title,
                video_url: exercise.video || exercise.lien,
                thumbnail: exercise.thumbnail,
                teacher_name: exercise.teacher_name,
                manuel_id: exercise.manuel_id,
                page: exercise.page,
                views: exercise.vues || exercise.views,
                likes: exercise.likes
            });
            
            // Vérifier si l'URL de la vidéo est valide
            if (exercise.video || exercise.lien) {
                console.log('✅ URL de vidéo trouvée:', exercise.video || exercise.lien);
            } else {
                console.log('❌ Aucune URL de vidéo trouvée');
            }
        }
        
    } catch (error) {
        console.error('❌ Erreur lors du test:', error.message);
        if (error.response) {
            console.error('Détails de l\'erreur:', error.response.data);
        }
    }
}

// Test de navigation vers VideoScreen
async function testVideoNavigation() {
    console.log('\n🎯 Test de navigation vers VideoScreen...');
    
    // Simuler les données d'exercice qui seraient passées à VideoScreen
    const exerciseData = {
        id: 570,
        titre: 'تمرين في الرياضيات',
        video: 'https://videos-abajim-1.s3.de.io.cloud.ovh.net/videos/test.mp4',
        thumbnail: '/thumbnails/test.jpg',
        teacher_name: 'الأستاذ محمد',
        manuel_id: 13,
        page: 8,
        vues: 150,
        likes: 25
    };
    
    console.log('📱 Données d\'exercice pour VideoScreen:');
    console.log(JSON.stringify(exerciseData, null, 2));
    
    // Vérifier que toutes les propriétés nécessaires sont présentes
    const requiredFields = ['id', 'titre', 'video'];
    const missingFields = requiredFields.filter(field => !exerciseData[field]);
    
    if (missingFields.length === 0) {
        console.log('✅ Toutes les propriétés requises sont présentes');
    } else {
        console.log('❌ Propriétés manquantes:', missingFields);
    }
}

// Exécuter les tests
async function runTests() {
    console.log('🚀 Démarrage des tests de navigation d\'exercices...\n');
    
    await testExerciseRecommendation();
    await testVideoNavigation();
    
    console.log('\n✅ Tests terminés!');
}

runTests();
