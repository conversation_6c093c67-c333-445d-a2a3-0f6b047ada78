# Test de la fonctionnalité audio du chatbot

## Corrections apportées

### 1. Composant VoiceMessage.js
- ✅ **Suppression de la restriction pour les messages utilisateur** : Les messages vocaux utilisateur peuvent maintenant être lus s'ils ont un chemin audio valide du serveur
- ✅ **Correction de la logique de détection des fichiers lisibles** : Seuls les fichiers avec des chemins `file://` (temporaires) sont bloqués
- ✅ **Correction de l'affichage du bouton** : Le bouton affiche maintenant `play/pause` au lieu de `mic` pour tous les messages avec audio valide
- ✅ **Ajout de logs de débogage** : Pour tracer les tentatives de lecture audio

### 2. Composant ChatOverlayContentPersistent.js
- ✅ **Ajout de logs pour l'enregistrement** : Pour tracer le processus d'enregistrement
- ✅ **Ajout de logs pour l'ajout de messages vocaux** : Pour vérifier que les données audio sont correctement transmises

## Tests à effectuer

### Test 1: Enregistrement audio
1. Ouvrir l'application mobile
2. Aller dans le chatbot
3. Appuyer sur le bouton microphone (quand le champ de texte est vide)
4. Parler pendant quelques secondes
5. Appuyer sur le bouton stop
6. Vérifier que :
   - Le message vocal apparaît avec une interface d'onde audio
   - Le bouton de lecture est visible (icône play)
   - Le bouton n'est pas désactivé

### Test 2: Lecture audio des messages utilisateur
1. Après avoir envoyé un message vocal (Test 1)
2. Appuyer sur le bouton de lecture du message vocal utilisateur
3. Vérifier que :
   - L'audio se lit correctement
   - Le bouton change en pause pendant la lecture
   - La barre de progression fonctionne

### Test 3: Lecture audio des réponses du bot
1. Après avoir reçu une réponse du chatbot avec audio
2. Appuyer sur le bouton de lecture de la réponse
3. Vérifier que :
   - L'audio TTS se lit correctement
   - L'interface fonctionne normalement

## Logs à surveiller

Dans la console du développeur, rechercher :
- `🎤 Démarrage de l'enregistrement audio...`
- `✅ Enregistrement démarré avec succès`
- `🎵 Ajout du message vocal à l'interface:`
- `🎵 Tentative de lecture audio pour:`
- `🎵 Lecture audio:` (avec l'URL du fichier)

## Problèmes potentiels

1. **Permissions audio** : Vérifier que l'application a les permissions microphone
2. **Chemins audio** : S'assurer que les chemins retournés par le serveur sont accessibles
3. **Format audio** : Vérifier que les fichiers .m4a sont supportés
4. **Réseau** : S'assurer que l'URL du serveur est correcte dans la configuration

## Structure des données audio

Les messages vocaux doivent avoir cette structure :
```javascript
{
  sender: 'user',
  text: 'transcription du message',
  message_type: 'audio',
  audio_path: '/uploads/audio_1234567890.m4a', // Chemin serveur
  audio_duration: 5000, // en millisecondes
  audio_size: 12345, // en bytes
  timestamp: 1234567890
}
```
