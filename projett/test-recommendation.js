// Test script for recommendation service
// The models will load the environment variables
const db = require('./backend/public/src/models');
const RecommendationService = require('./backend/public/src/services/RecommendationService');

async function testRecommendations() {
  try {
    console.log('Testing database connection...');
    await db.sequelize.authenticate();
    console.log('Database connection successful!');

    console.log('\nTesting Like model...');
    const likes = await db.Like.findAll({ limit: 3 });
    console.log(`Found ${likes.length} likes`);

    console.log('\nTesting Video model...');
    const videos = await db.Video.findAll({ limit: 3 });
    console.log(`Found ${videos.length} videos`);

    console.log('\nTesting getCollaborativeScore...');
    // Use a teacher ID that exists in your database
    const teacherId = 1954; // Replace with an actual teacher ID
    const score = await RecommendationService.getCollaborativeScore(teacherId);
    console.log(`Collaborative score for teacher ${teacherId}: ${score}`);

    console.log('\nTesting recommendProf...');
    // Use a child ID and matiere that exist in your database
    const childId = 3707; // Replace with an actual child ID
    const matiereName = 'رياضيات'; // Replace with an actual matiere name
    const teachers = await RecommendationService.recommendProf(childId, matiereName, 3);
    console.log(`Found ${teachers.length} recommended teachers for ${matiereName}`);
    console.log(JSON.stringify(teachers, null, 2));

    console.log('\nAll tests completed successfully!');
  } catch (error) {
    console.error('Error during testing:', error);
  } finally {
    // Close the database connection
    await db.sequelize.close();
  }
}

testRecommendations();
