/**
 * Script de test pour la navigation vers le profil du professeur
 * Ce script teste l'intent "recommander_professeur" et vérifie que les données nécessaires
 * pour la navigation sont bien retournées par le backend.
 */

const axios = require('axios');

// Configuration
const BASE_URL = 'http://localhost:5001/api';
const TEST_USER_ID = 3707; // ID d'un utilisateur de test
const TEST_MESSAGE = 'أريد أستاذ رياضيات'; // "Je veux un professeur de mathématiques"

async function testTeacherRecommendationNavigation() {
  console.log('🧪 Test de la navigation vers le profil du professeur');
  console.log('=' .repeat(60));

  try {
    // 1. Tester l'endpoint du chatbot pour une recommandation de professeur
    console.log('📤 Envoi de la demande de recommandation de professeur...');
    console.log(`Message: "${TEST_MESSAGE}"`);
    console.log(`User ID: ${TEST_USER_ID}`);

    const response = await axios.post(`${BASE_URL}/chatbot/ask`, {
      userId: TEST_USER_ID,
      message: TEST_MESSAGE,
      messageType: 'text'
    });

    console.log('\n📥 Réponse reçue du backend:');
    console.log('Status:', response.status);
    
    const data = response.data;
    console.log('\n🔍 Analyse des données de réponse:');
    console.log('- Reply:', data.reply ? data.reply.substring(0, 100) + '...' : 'Aucune réponse');
    console.log('- Audio:', data.audio ? 'Présent' : 'Absent');
    console.log('- Recommendation Type:', data.recommendationType || 'Non défini');
    console.log('- Teacher ID:', data.teacherId || 'Non défini');

    // 2. Vérifier les données de navigation
    if (data.teacherData) {
      console.log('\n✅ Données du professeur pour la navigation:');
      console.log('- ID:', data.teacherData.id);
      console.log('- Nom:', data.teacherData.name);
      console.log('- Bio:', data.teacherData.bio ? data.teacherData.bio.substring(0, 50) + '...' : 'Aucune bio');
      console.log('- Avatar:', data.teacherData.avatar || 'Aucun avatar');
      console.log('- Nombre de cours:', data.teacherData.webinarCount);
      console.log('- Score:', data.teacherData.score + '%');
      console.log('- Expertise:', data.teacherData.expertise);
      console.log('- Matière:', data.teacherData.matiere);

      // 3. Vérifier que toutes les données nécessaires sont présentes
      const requiredFields = ['id', 'name'];
      const missingFields = requiredFields.filter(field => !data.teacherData[field]);
      
      if (missingFields.length === 0) {
        console.log('\n🎯 ✅ SUCCÈS: Toutes les données nécessaires pour la navigation sont présentes!');
        console.log('🚀 La navigation vers le profil du professeur devrait fonctionner.');
      } else {
        console.log('\n❌ ERREUR: Champs manquants pour la navigation:', missingFields);
      }
    } else {
      console.log('\n❌ ERREUR: Aucune donnée de professeur trouvée dans la réponse');
    }

    // 4. Afficher la structure complète pour debug
    console.log('\n📋 Structure complète de la réponse:');
    console.log(JSON.stringify(data, null, 2));

  } catch (error) {
    console.error('\n❌ Erreur lors du test:', error.message);
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Data:', error.response.data);
    }
  }
}

// Fonction pour tester la navigation directe vers un professeur
async function testDirectTeacherNavigation() {
  console.log('\n\n🧪 Test de navigation directe vers un professeur');
  console.log('=' .repeat(60));

  try {
    // Simuler les données qu'on recevrait du chatbot
    const mockTeacherData = {
      id: 123,
      name: 'أستاذ محمد الأحمد',
      bio: 'أستاذ رياضيات متخصص في التعليم الابتدائي',
      avatar: '/uploads/teachers/teacher_123.jpg',
      webinarCount: 15,
      score: 85,
      expertise: 'متقدم',
      matiere: 'رياضيات'
    };

    console.log('📱 Simulation de la navigation avec les données:');
    console.log(JSON.stringify(mockTeacherData, null, 2));

    console.log('\n🎯 Navigation simulée vers TeacherScreen avec:');
    console.log('- Route: Teacher');
    console.log('- Params: { teacherId:', mockTeacherData.id, ', teacher:', mockTeacherData, '}');

    console.log('\n✅ La navigation devrait fonctionner avec ces données!');

  } catch (error) {
    console.error('\n❌ Erreur lors du test de navigation:', error.message);
  }
}

// Exécuter les tests
async function runAllTests() {
  await testTeacherRecommendationNavigation();
  await testDirectTeacherNavigation();
  
  console.log('\n' + '='.repeat(60));
  console.log('🏁 Tests terminés');
}

// Lancer les tests si ce script est exécuté directement
if (require.main === module) {
  runAllTests();
}

module.exports = {
  testTeacherRecommendationNavigation,
  testDirectTeacherNavigation,
  runAllTests
};
