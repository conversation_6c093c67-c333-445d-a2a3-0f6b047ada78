#!/usr/bin/env python3
"""
Test script to verify the API request with Arabic characters.
"""

import requests
import json
import sys

def test_api_request(matiere_name, level_id):
    """
    Test API request with Arabic characters.
    
    Args:
        matiere_name (str): Subject name
        level_id (int): Level ID
    """
    url = f"http://localhost:8000/api/recommendations/teachers"
    params = {
        'matiere_name': matiere_name,
        'level_id': level_id
    }
    
    print(f"Testing API request with matiere_name='{matiere_name}', level_id={level_id}")
    
    try:
        response = requests.get(url, params=params)
        
        print(f"Response status code: {response.status_code}")
        print(f"Response content: {response.text}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"Normalized matiere_name: {data['matiere']}")
            print(f"Number of recommendations: {len(data['recommendations'])}")
            
            if data['recommendations']:
                print("Recommendations:")
                for i, teacher in enumerate(data['recommendations']):
                    print(f"  {i+1}. {teacher['full_name']} (ID: {teacher['id']}, Score: {teacher['score']})")
            else:
                print("No recommendations found.")
        else:
            print(f"Error: {response.text}")
    
    except Exception as e:
        print(f"Error: {str(e)}")

if __name__ == "__main__":
    # Test with different matiere names
    test_cases = [
        ('فرنسية', 9),  # Français niveau 9
        ('الفرنسية', 9),  # Français avec article niveau 9
        ('رياضيات', 9),  # Mathématiques niveau 9
        ('عربية', 9),  # Arabe niveau 9
    ]
    
    for matiere_name, level_id in test_cases:
        test_api_request(matiere_name, level_id)
        print("-" * 50)
