#!/usr/bin/env node

/**
 * Test complet du système de navigation intelligente
 * Vérifie backend + frontend + intégration
 */

const axios = require('axios');
const fs = require('fs');
const path = require('path');

async function testBackendNavigation() {
    console.log('🔧 TEST BACKEND - Navigation intelligente');
    console.log('='.repeat(50));
    
    const baseURL = 'http://localhost:5001';
    const testMessage = 'هل يوجد درس اسمه الضرب في رقمين؟';
    const userId = 3712;
    
    try {
        const response = await axios.post(`${baseURL}/api/chatbot/ask`, {
            message: testMessage,
            userId: userId,
            messageType: 'text'
        }, {
            headers: { 'Content-Type': 'application/json' },
            timeout: 30000
        });
        
        // Vérifications backend
        const checks = {
            status: response.status === 200,
            reply: !!response.data.reply,
            navigation: !!response.data.navigation,
            courseListData: !!response.data.courseListData,
            correctScreen: response.data.navigation?.screen === 'CourseList',
            hasCourses: response.data.courseListData?.courses?.length > 0,
            coursesHaveIds: response.data.courseListData?.courses?.every(c => !!c.id),
            coursesHaveTitles: response.data.courseListData?.courses?.every(c => !!c.title),
            coursesHaveTeachers: response.data.courseListData?.courses?.every(c => !!c.teacher)
        };
        
        console.log('📋 VÉRIFICATIONS BACKEND:');
        Object.entries(checks).forEach(([key, value]) => {
            console.log(`${value ? '✅' : '❌'} ${key}: ${value ? 'OK' : 'ÉCHEC'}`);
        });
        
        const backendSuccess = Object.values(checks).every(v => v);
        console.log(`\n🎯 Backend: ${backendSuccess ? '✅ RÉUSSI' : '❌ ÉCHEC'}`);
        
        return {
            success: backendSuccess,
            data: response.data,
            courses: response.data.courseListData?.courses || []
        };
        
    } catch (error) {
        console.error('❌ Erreur backend:', error.message);
        return { success: false, data: null, courses: [] };
    }
}

function testFrontendComponents() {
    console.log('\n🎨 TEST FRONTEND - Composants');
    console.log('='.repeat(50));
    
    const frontendPath = path.join(__dirname, 'Frontend');
    
    // Vérifier CourseListCard
    const courseListCardPath = path.join(frontendPath, 'src/components/CourseListCard.js');
    const courseListCardExists = fs.existsSync(courseListCardPath);
    console.log(`${courseListCardExists ? '✅' : '❌'} CourseListCard.js: ${courseListCardExists ? 'EXISTE' : 'MANQUANT'}`);
    
    let courseListCardValid = false;
    if (courseListCardExists) {
        const content = fs.readFileSync(courseListCardPath, 'utf8');
        const requiredElements = [
            'import { logChildActivity }',
            'from \'../services/activityLogger\'',
            'navigation.navigate("WebinarDetail"',
            'webinarId: course.id'
        ];
        
        courseListCardValid = requiredElements.every(element => content.includes(element));
        console.log(`${courseListCardValid ? '✅' : '❌'} CourseListCard contenu: ${courseListCardValid ? 'VALIDE' : 'INVALIDE'}`);
    }
    
    // Vérifier ChatMessage
    const chatMessagePath = path.join(frontendPath, 'src/components/ChatMessage.js');
    const chatMessageExists = fs.existsSync(chatMessagePath);
    console.log(`${chatMessageExists ? '✅' : '❌'} ChatMessage.js: ${chatMessageExists ? 'EXISTE' : 'MANQUANT'}`);
    
    let chatMessageValid = false;
    if (chatMessageExists) {
        const content = fs.readFileSync(chatMessagePath, 'utf8');
        const requiredElements = [
            'import CourseListCard',
            'isCourseList',
            'message.courseListData',
            '<CourseListCard'
        ];
        
        chatMessageValid = requiredElements.every(element => content.includes(element));
        console.log(`${chatMessageValid ? '✅' : '❌'} ChatMessage intégration: ${chatMessageValid ? 'VALIDE' : 'INVALIDE'}`);
    }
    
    // Vérifier activityLogger
    const activityLoggerPath = path.join(frontendPath, 'src/services/activityLogger.js');
    const activityLoggerExists = fs.existsSync(activityLoggerPath);
    console.log(`${activityLoggerExists ? '✅' : '❌'} activityLogger.js: ${activityLoggerExists ? 'EXISTE' : 'MANQUANT'}`);
    
    // Vérifier navigation
    const navigatorPath = path.join(frontendPath, 'src/navigation/AppNavigator.js');
    const navigatorExists = fs.existsSync(navigatorPath);
    console.log(`${navigatorExists ? '✅' : '❌'} AppNavigator.js: ${navigatorExists ? 'EXISTE' : 'MANQUANT'}`);
    
    let navigationValid = false;
    if (navigatorExists) {
        const content = fs.readFileSync(navigatorPath, 'utf8');
        navigationValid = content.includes('name="WebinarDetail"') && content.includes('component={WebinarDetail}');
        console.log(`${navigationValid ? '✅' : '❌'} Navigation WebinarDetail: ${navigationValid ? 'CONFIGURÉE' : 'MANQUANTE'}`);
    }
    
    const frontendSuccess = courseListCardExists && courseListCardValid && 
                           chatMessageExists && chatMessageValid && 
                           activityLoggerExists && navigationValid;
    
    console.log(`\n🎯 Frontend: ${frontendSuccess ? '✅ RÉUSSI' : '❌ ÉCHEC'}`);
    
    return frontendSuccess;
}

function testDataFlow(backendData) {
    console.log('\n🔄 TEST FLUX DE DONNÉES');
    console.log('='.repeat(50));
    
    if (!backendData.success) {
        console.log('❌ Impossible de tester le flux - backend en échec');
        return false;
    }
    
    const { data, courses } = backendData;
    
    // Simuler le flux de données
    console.log('📤 Données envoyées par le backend:');
    console.log(`   Navigation screen: ${data.navigation?.screen}`);
    console.log(`   Nombre de cours: ${courses.length}`);
    console.log(`   Titre: ${data.courseListData?.title}`);
    
    // Simuler la réception frontend
    console.log('\n📥 Traitement frontend simulé:');
    
    // Simulation de la détection dans ChatMessage
    const isCourseList = data.courseListData && data.courseListData.courses && data.courseListData.courses.length > 0;
    console.log(`${isCourseList ? '✅' : '❌'} isCourseList détecté: ${isCourseList}`);
    
    // Simulation des props pour CourseListCard
    if (isCourseList) {
        const props = {
            courses: data.courseListData.courses,
            title: data.courseListData.title,
            onCoursePress: '(course) => navigation.navigate("WebinarDetail", { webinarId: course.id })'
        };
        
        console.log('✅ Props CourseListCard générées:');
        console.log(`   courses: ${props.courses.length} éléments`);
        console.log(`   title: "${props.title}"`);
        console.log(`   onCoursePress: fonction définie`);
        
        // Vérifier que chaque cours a les données nécessaires
        const coursesValid = props.courses.every(course => 
            course.id && course.title && course.teacher
        );
        console.log(`${coursesValid ? '✅' : '❌'} Données cours valides: ${coursesValid}`);
        
        return coursesValid;
    }
    
    return false;
}

async function runCompleteTest() {
    console.log('🧪 TEST COMPLET - SYSTÈME DE NAVIGATION INTELLIGENTE');
    console.log('='.repeat(70));
    console.log('🎯 Objectif: Vérifier que le chatbot peut afficher une liste de cours');
    console.log('📋 Scénario: Recherche "هل يوجد درس اسمه الضرب في رقمين؟"');
    console.log('');
    
    // Test backend
    const backendResult = await testBackendNavigation();
    
    // Test frontend
    const frontendResult = testFrontendComponents();
    
    // Test flux de données
    const dataFlowResult = testDataFlow(backendResult);
    
    // Résultat final
    console.log('\n' + '='.repeat(70));
    console.log('📊 RÉSULTATS FINAUX:');
    console.log(`${backendResult.success ? '✅' : '❌'} Backend (API + Navigation): ${backendResult.success ? 'RÉUSSI' : 'ÉCHEC'}`);
    console.log(`${frontendResult ? '✅' : '❌'} Frontend (Composants): ${frontendResult ? 'RÉUSSI' : 'ÉCHEC'}`);
    console.log(`${dataFlowResult ? '✅' : '❌'} Flux de données: ${dataFlowResult ? 'RÉUSSI' : 'ÉCHEC'}`);
    
    const overallSuccess = backendResult.success && frontendResult && dataFlowResult;
    
    console.log('\n🏆 RÉSULTAT GLOBAL:');
    if (overallSuccess) {
        console.log('🎉 TOUS LES TESTS RÉUSSIS !');
        console.log('✅ Le système de navigation intelligente est opérationnel');
        console.log('✅ Les utilisateurs peuvent maintenant:');
        console.log('   • Demander des cours spécifiques au chatbot');
        console.log('   • Voir une liste de cours correspondants');
        console.log('   • Cliquer pour naviguer vers les détails');
        console.log('   • Accéder directement au contenu des cours');
    } else {
        console.log('❌ CERTAINS TESTS ONT ÉCHOUÉ');
        console.log('⚠️ Le système nécessite des corrections avant utilisation');
        
        if (!backendResult.success) {
            console.log('🔧 Vérifier: Serveur backend, base de données, API');
        }
        if (!frontendResult) {
            console.log('🔧 Vérifier: Composants React Native, imports, navigation');
        }
        if (!dataFlowResult) {
            console.log('🔧 Vérifier: Structure des données, props, intégration');
        }
    }
    
    return overallSuccess;
}

// Exécuter si appelé directement
if (require.main === module) {
    runCompleteTest().catch(console.error);
}

module.exports = { runCompleteTest };
