#!/usr/bin/env node

/**
 * Test pour vérifier l'affichage des données de cours dans CourseListCard
 */

const fs = require('fs');
const path = require('path');

function testCourseDisplay() {
    console.log('🎯 TEST D\'AFFICHAGE DES COURS');
    console.log('='.repeat(50));
    
    // Données de test simulant la structure réelle
    const testCourseData = {
        courseListData: {
            title: "2 دروس متطابقة",
            searchQuery: "الضرب في رقمين",
            courses: [
                {
                    id: 1,
                    title: "الضرب في رقمين",
                    teacher: "Hatem Slama",
                    score: 1.0,
                    image: null,
                    webinar_id: 123
                },
                {
                    id: 2,
                    title: "الضرب في رقمين - الجزء الثاني",
                    teacher: "Hatem Slama", 
                    score: 0.95,
                    image: null,
                    webinar_id: 124
                }
            ]
        }
    };
    
    console.log('📋 DONNÉES DE TEST:');
    console.log(`Titre de la carte: "${testCourseData.courseListData.title}"`);
    console.log(`Nombre de cours: ${testCourseData.courseListData.courses.length}`);
    
    console.log('\n📚 DÉTAILS DES COURS:');
    testCourseData.courseListData.courses.forEach((course, index) => {
        console.log(`\nCours ${index + 1}:`);
        console.log(`  ID: ${course.id}`);
        console.log(`  Titre: "${course.title}"`);
        console.log(`  Enseignant: "${course.teacher}"`);
        console.log(`  Score: ${course.score}`);
        console.log(`  Image: ${course.image || 'null'}`);
    });
    
    // Vérifier le composant CourseListCard
    const cardPath = path.join(__dirname, 'Frontend/src/components/CourseListCard.js');
    
    if (!fs.existsSync(cardPath)) {
        console.log('\n❌ Fichier CourseListCard.js non trouvé');
        return false;
    }
    
    const content = fs.readFileSync(cardPath, 'utf8');
    
    console.log('\n🔍 VÉRIFICATION DU COMPOSANT:');
    
    // Vérifier les propriétés utilisées
    const propertyChecks = [
        {
            name: 'course.title',
            check: 'course.title',
            description: 'Accès au titre du cours'
        },
        {
            name: 'course.teacher',
            check: 'course.teacher',
            description: 'Accès au nom de l\'enseignant'
        },
        {
            name: 'course.score',
            check: 'course.score',
            description: 'Accès au score de correspondance'
        },
        {
            name: 'Fallback pour titre',
            check: 'course.title || course.name || course.webinar_title',
            description: 'Fallback pour différents noms de propriétés'
        },
        {
            name: 'Fallback pour enseignant',
            check: 'course.teacher || course.teacher_name',
            description: 'Fallback pour nom d\'enseignant'
        },
        {
            name: 'numberOfLines pour titre',
            check: 'numberOfLines={2}',
            description: 'Limitation du nombre de lignes pour le titre'
        }
    ];
    
    let passedChecks = 0;
    
    propertyChecks.forEach(check => {
        const isPresent = content.includes(check.check);
        const status = isPresent ? '✅' : '❌';
        console.log(`${status} ${check.name}: ${isPresent ? 'PRÉSENT' : 'MANQUANT'}`);
        console.log(`   ${check.description}`);
        if (isPresent) passedChecks++;
    });
    
    // Vérifier les styles de texte
    console.log('\n🎨 VÉRIFICATION DES STYLES DE TEXTE:');
    
    const styleChecks = [
        {
            name: 'courseTitle fontSize',
            check: 'fontSize: 16',
            description: 'Taille de police pour le titre'
        },
        {
            name: 'courseTitle color',
            check: 'color: \'#1F3B64\'',
            description: 'Couleur du titre'
        },
        {
            name: 'courseTitle textAlign',
            check: 'textAlign: \'right\'',
            description: 'Alignement RTL pour l\'arabe'
        },
        {
            name: 'courseTitle fontWeight',
            check: 'fontWeight: \'700\'',
            description: 'Poids de la police'
        }
    ];
    
    let passedStyles = 0;
    
    styleChecks.forEach(check => {
        const isPresent = content.includes(check.check);
        const status = isPresent ? '✅' : '❌';
        console.log(`${status} ${check.name}: ${isPresent ? 'PRÉSENT' : 'MANQUANT'}`);
        console.log(`   ${check.description}`);
        if (isPresent) passedStyles++;
    });
    
    // Calcul des scores
    const propertyScore = (passedChecks / propertyChecks.length) * 100;
    const styleScore = (passedStyles / styleChecks.length) * 100;
    const overallScore = (propertyScore + styleScore) / 2;
    
    console.log('\n📊 SCORES:');
    console.log(`🔧 Propriétés: ${propertyScore.toFixed(1)}% (${passedChecks}/${propertyChecks.length})`);
    console.log(`🎨 Styles: ${styleScore.toFixed(1)}% (${passedStyles}/${styleChecks.length})`);
    console.log(`🏆 Score global: ${overallScore.toFixed(1)}%`);
    
    // Diagnostic des problèmes potentiels
    console.log('\n🔧 DIAGNOSTIC DES PROBLÈMES:');
    
    if (!content.includes('course.title || course.name || course.webinar_title')) {
        console.log('⚠️ Pas de fallback pour le titre - peut causer des titres vides');
    }
    
    if (!content.includes('numberOfLines={2}')) {
        console.log('⚠️ Pas de limitation de lignes - peut causer des débordements');
    }
    
    if (!content.includes('textAlign: \'right\'')) {
        console.log('⚠️ Pas d\'alignement RTL - problème pour l\'arabe');
    }
    
    // Recommandations
    console.log('\n💡 RECOMMANDATIONS:');
    
    if (overallScore >= 90) {
        console.log('✅ Composant bien configuré pour l\'affichage');
        console.log('✅ Les titres devraient s\'afficher correctement');
    } else if (overallScore >= 75) {
        console.log('⚠️ Composant partiellement configuré');
        console.log('🔧 Quelques améliorations nécessaires');
    } else {
        console.log('❌ Problèmes de configuration détectés');
        console.log('🔧 Révision du composant nécessaire');
    }
    
    // Test de simulation d'affichage
    console.log('\n🎭 SIMULATION D\'AFFICHAGE:');
    console.log('Si les données suivantes étaient passées au composant:');
    
    testCourseData.courseListData.courses.forEach((course, index) => {
        console.log(`\n📱 Affichage cours ${index + 1}:`);
        console.log(`  Titre affiché: "${course.title || course.name || course.webinar_title || 'عنوان الدرس'}"`);
        console.log(`  Enseignant affiché: "${course.teacher || course.teacher_name || 'غير محدد'}"`);
        console.log(`  Score affiché: "${course.score ? Math.round(course.score * 100) + '% مطابق' : 'غير محدد'}"`);
    });
    
    return overallScore >= 75;
}

// Exécuter le test
if (require.main === module) {
    const result = testCourseDisplay();
    
    console.log('\n' + '='.repeat(50));
    console.log('🎯 RÉSULTAT FINAL:');
    console.log(`${result ? '✅' : '❌'} Test d'affichage: ${result ? 'RÉUSSI' : 'ÉCHOUÉ'}`);
    
    if (result) {
        console.log('\n🎉 LE COMPOSANT DEVRAIT AFFICHER LES TITRES CORRECTEMENT !');
        console.log('✅ Vérifiez que les données arrivent bien du backend');
    } else {
        console.log('\n⚠️ PROBLÈMES D\'AFFICHAGE DÉTECTÉS');
        console.log('🔧 Corrections nécessaires dans le composant');
    }
}

module.exports = { testCourseDisplay };
