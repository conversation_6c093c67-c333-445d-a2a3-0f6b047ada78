#!/usr/bin/env node

/**
 * Test spécifique pour l'affichage du nom du professeur
 */

const fs = require('fs');
const path = require('path');

function testTeacherDisplay() {
    console.log('👨‍🏫 TEST D\'AFFICHAGE DU NOM DU PROFESSEUR');
    console.log('='.repeat(50));
    
    const cardPath = path.join(__dirname, 'Frontend/src/components/CourseListCard.js');
    
    if (!fs.existsSync(cardPath)) {
        console.log('❌ Fichier CourseListCard.js non trouvé');
        return false;
    }
    
    const content = fs.readFileSync(cardPath, 'utf8');
    
    // Vérifications spécifiques au nom du professeur
    const teacherChecks = [
        {
            name: 'Affichage du nom du professeur',
            check: 'course.teacher || course.teacher_name',
            description: 'Fallback pour différentes propriétés',
            critical: true
        },
        {
            name: '<PERSON>lle de police appropriée',
            check: 'fontSize: 15',
            description: 'Lisibilité du nom (15px)',
            critical: true
        },
        {
            name: 'Couleur contrastée',
            check: 'color: \'#1F3B64\'',
            description: 'Couleur sombre pour la lisibilité',
            critical: true
        },
        {
            name: 'Alignement RTL',
            check: 'textAlign: \'right\'',
            description: 'Alignement pour l\'arabe',
            critical: true
        },
        {
            name: 'Limitation à 1 ligne',
            check: 'numberOfLines={1}',
            description: 'Évite le débordement',
            critical: true
        },
        {
            name: 'Espacement avec avatar',
            check: 'marginLeft: 8',
            description: 'Espace entre avatar et nom',
            critical: false
        },
        {
            name: 'Poids de police',
            check: 'fontWeight: \'600\'',
            description: 'Mise en valeur du nom',
            critical: false
        },
        {
            name: 'Hauteur de ligne',
            check: 'lineHeight: 20',
            description: 'Espacement vertical',
            critical: false
        },
        {
            name: 'Flexibilité',
            check: 'flex: 1',
            description: 'Utilise l\'espace disponible',
            critical: true
        },
        {
            name: 'Avatar du professeur',
            check: 'teacherAvatar',
            description: 'Icône visuelle pour le professeur',
            critical: false
        }
    ];
    
    console.log('📋 VÉRIFICATIONS DU NOM DU PROFESSEUR:');
    let passedChecks = 0;
    let criticalPassed = 0;
    let totalCritical = 0;
    
    teacherChecks.forEach(check => {
        const isPresent = content.includes(check.check);
        const status = isPresent ? '✅' : '❌';
        const priority = check.critical ? '🔴 CRITIQUE' : '🟡 OPTIONNEL';
        
        console.log(`${status} ${check.name}: ${isPresent ? 'PRÉSENT' : 'MANQUANT'}`);
        console.log(`   ${check.description} (${priority})`);
        
        if (isPresent) passedChecks++;
        if (check.critical) {
            totalCritical++;
            if (isPresent) criticalPassed++;
        }
    });
    
    // Scores
    const overallScore = (passedChecks / teacherChecks.length) * 100;
    const criticalScore = (criticalPassed / totalCritical) * 100;
    
    console.log('\n📊 SCORES:');
    console.log(`🏆 Score global: ${overallScore.toFixed(1)}% (${passedChecks}/${teacherChecks.length})`);
    console.log(`🔴 Score critique: ${criticalScore.toFixed(1)}% (${criticalPassed}/${totalCritical})`);
    
    // Simulation d'affichage
    console.log('\n🎭 SIMULATION D\'AFFICHAGE DU PROFESSEUR:');
    
    const testTeachers = [
        'Hatem Slama',
        'أحمد محمد',
        'Dr. Sarah Johnson',
        'محمد عبد الله الطويل الاسم'
    ];
    
    testTeachers.forEach((teacher, index) => {
        console.log(`\n👨‍🏫 Professeur ${index + 1}: "${teacher}"`);
        console.log(`   Initiales: "${getInitials(teacher)}"`);
        console.log(`   Affichage: [${getInitials(teacher)}] ${teacher}`);
        console.log(`   Longueur: ${teacher.length} caractères`);
        
        if (teacher.length > 20) {
            console.log(`   ⚠️ Nom long - sera tronqué avec numberOfLines={1}`);
        }
    });
    
    // Diagnostic des problèmes
    console.log('\n🔧 DIAGNOSTIC DES PROBLÈMES:');
    
    if (!content.includes('numberOfLines={1}')) {
        console.log('❌ PROBLÈME: Pas de limitation de lignes');
        console.log('   IMPACT: Le nom peut déborder sur plusieurs lignes');
        console.log('   SOLUTION: Ajouter numberOfLines={1} au Text du nom');
    }
    
    if (!content.includes('fontSize: 15')) {
        console.log('❌ PROBLÈME: Taille de police trop petite');
        console.log('   IMPACT: Nom difficile à lire');
        console.log('   SOLUTION: Utiliser fontSize: 15 minimum');
    }
    
    if (!content.includes('flex: 1')) {
        console.log('❌ PROBLÈME: Pas de flexibilité');
        console.log('   IMPACT: Le nom peut être coupé');
        console.log('   SOLUTION: Ajouter flex: 1 au style');
    }
    
    // Recommandations
    console.log('\n💡 RECOMMANDATIONS:');
    
    if (criticalScore >= 90) {
        console.log('✅ Affichage du nom optimisé');
        console.log('✅ Le nom du professeur devrait être clairement visible');
    } else if (criticalScore >= 70) {
        console.log('⚠️ Affichage partiellement optimisé');
        console.log('🔧 Quelques ajustements nécessaires');
    } else {
        console.log('❌ Problèmes d\'affichage détectés');
        console.log('🔧 Révision nécessaire');
    }
    
    return criticalScore >= 80;
}

function getInitials(name) {
    if (!name) return '??';
    
    const words = name.trim().split(' ');
    if (words.length === 1) {
        return words[0].substring(0, 2).toUpperCase();
    }
    
    return words.slice(0, 2).map(word => word.charAt(0)).join('').toUpperCase();
}

function generateTeacherDisplayGuide() {
    console.log('\n📋 GUIDE D\'AFFICHAGE DU PROFESSEUR');
    console.log('='.repeat(50));
    
    console.log('🎨 STRUCTURE VISUELLE ATTENDUE:');
    console.log('');
    console.log('┌─────────────────────────────────────┐');
    console.log('│ [IMG] الضرب في رقمين              →│');
    console.log('│       الجزء الأول                  │');
    console.log('│       ┌─────────────────────────┐   │');
    console.log('│       │ [HS] Hatem Slama        │   │ ← Nom visible');
    console.log('│       └─────────────────────────┘   │');
    console.log('│       100% مطابق                    │');
    console.log('└─────────────────────────────────────┘');
    
    console.log('\n🔧 ÉLÉMENTS CLÉS:');
    console.log('• [HS] = Avatar avec initiales');
    console.log('• "Hatem Slama" = Nom complet visible');
    console.log('• Fond coloré pour mise en valeur');
    console.log('• Alignement RTL pour l\'arabe');
    
    console.log('\n✅ VÉRIFICATIONS FINALES:');
    console.log('1. Le nom du professeur est-il visible ?');
    console.log('2. Les initiales sont-elles correctes ?');
    console.log('3. L\'alignement est-il correct (RTL) ?');
    console.log('4. La taille de police est-elle lisible ?');
    console.log('5. Y a-t-il assez d\'espace pour le nom ?');
}

// Exécuter les tests
if (require.main === module) {
    const result = testTeacherDisplay();
    generateTeacherDisplayGuide();
    
    console.log('\n' + '='.repeat(50));
    console.log('👨‍🏫 RÉSULTAT FINAL:');
    console.log(`${result ? '✅' : '❌'} Affichage du professeur: ${result ? 'OPTIMISÉ' : 'À AMÉLIORER'}`);
    
    if (result) {
        console.log('\n🎉 LE NOM DU PROFESSEUR EST MAINTENANT BIEN VISIBLE !');
        console.log('✅ Taille de police appropriée (15px)');
        console.log('✅ Couleur contrastée pour la lisibilité');
        console.log('✅ Espacement optimisé avec l\'avatar');
        console.log('✅ Support RTL pour l\'arabe');
        console.log('');
        console.log('🔍 TESTEZ MAINTENANT DANS L\'APPLICATION !');
    } else {
        console.log('\n⚠️ AMÉLIORATIONS NÉCESSAIRES');
        console.log('🔧 Vérifiez les éléments critiques manquants');
    }
}

module.exports = { testTeacherDisplay };
