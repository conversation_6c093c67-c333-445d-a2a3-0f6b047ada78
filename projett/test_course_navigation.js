#!/usr/bin/env node

/**
 * Script de test pour vérifier le circuit complet de navigation des cours recommandés
 * Du système de recommandation jusqu'à la navigation frontend
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:5001';
const RECOMMENDATION_URL = 'http://localhost:8000';

// Couleurs pour les logs
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

async function testCourseRecommendationSystem() {
  log('🧪 Test du système complet de recommandation de cours', 'blue');
  log('=' * 60, 'blue');

  try {
    // Test 1: Vérifier que le serveur de recommandation fonctionne
    log('\n📡 Test 1: Serveur de recommandation', 'yellow');
    try {
      const recResponse = await axios.get(`${RECOMMENDATION_URL}/api/recommendations/courses?student_id=3707&matiere_name=math&level_id=9&top_n=3`);
      log(`✅ Serveur de recommandation: ${recResponse.data.recommendations.length} cours trouvés`, 'green');
      
      const firstCourse = recResponse.data.recommendations[0];
      log(`   Premier cours: ${firstCourse.title} (ID: ${firstCourse.id})`, 'green');
    } catch (error) {
      log(`❌ Erreur serveur de recommandation: ${error.message}`, 'red');
      return false;
    }

    // Test 2: Vérifier que le backend intègre les recommandations
    log('\n🔗 Test 2: Intégration backend', 'yellow');
    try {
      const backendResponse = await axios.get(`${BASE_URL}/api/recommendations/courses?userId=3707&matiereName=math&levelId=9&topN=3`);
      log(`✅ Backend: ${backendResponse.data.recommendations.length} cours formatés`, 'green');
      
      const firstCourse = backendResponse.data.recommendations[0];
      log(`   Premier cours: ${firstCourse.title} (ID: ${firstCourse.id})`, 'green');
      log(`   Données complètes: ${firstCourse.chapters ? 'Oui' : 'Non'}`, firstCourse.chapters ? 'green' : 'red');
    } catch (error) {
      log(`❌ Erreur backend: ${error.message}`, 'red');
      return false;
    }

    // Test 3: Tester le chatbot pour recommandation de cours
    log('\n🤖 Test 3: Chatbot - Recommandation de cours', 'yellow');
    try {
      const chatResponse = await axios.post(`${BASE_URL}/api/chatbot/ask`, {
        message: 'اقترح لي دورة في الرياضيات',
        userId: 3707,
        childId: 3707
      });
      
      if (chatResponse.data.courseData) {
        log(`✅ Chatbot: Cours recommandé "${chatResponse.data.courseData.title}"`, 'green');
        log(`   Type de recommandation: ${chatResponse.data.recommendationType}`, 'green');
        log(`   Index: ${chatResponse.data.index}/${chatResponse.data.total}`, 'green');
        log(`   Session ID: ${chatResponse.data.sessionId}`, 'green');
        
        // Stocker les données pour le test suivant
        global.testCourseId = chatResponse.data.courseData.id;
        global.testSessionId = chatResponse.data.sessionId;
      } else {
        log(`❌ Chatbot: Aucune donnée de cours dans la réponse`, 'red');
        return false;
      }
    } catch (error) {
      log(`❌ Erreur chatbot: ${error.message}`, 'red');
      return false;
    }

    // Test 4: Tester la navigation progressive (cours suivant)
    log('\n➡️ Test 4: Navigation progressive', 'yellow');
    try {
      const nextResponse = await axios.post(`${BASE_URL}/api/chatbot/ask`, {
        message: 'التالي',
        userId: 3707,
        childId: 3707
      });
      
      if (nextResponse.data.courseData) {
        log(`✅ Navigation: Cours suivant "${nextResponse.data.courseData.title}"`, 'green');
        log(`   Index: ${nextResponse.data.index}/${nextResponse.data.total}`, 'green');
        
        if (nextResponse.data.courseData.id !== global.testCourseId) {
          log(`✅ Navigation: Cours différent du précédent`, 'green');
        } else {
          log(`⚠️ Navigation: Même cours que le précédent`, 'yellow');
        }
      } else {
        log(`❌ Navigation: Aucune donnée de cours dans la réponse`, 'red');
        return false;
      }
    } catch (error) {
      log(`❌ Erreur navigation: ${error.message}`, 'red');
      return false;
    }

    // Test 5: Vérifier l'API webinar pour la navigation
    log('\n🔍 Test 5: API Webinar pour navigation', 'yellow');
    try {
      const webinarResponse = await axios.get(`${BASE_URL}/api/webinars/${global.testCourseId}`);
      
      if (webinarResponse.data.id) {
        log(`✅ API Webinar: Cours trouvé "${webinarResponse.data.translations[0]?.title}"`, 'green');
        log(`   Chapitres: ${webinarResponse.data.chapters?.length || 0}`, 'green');
        log(`   Enseignant: ${webinarResponse.data.teacher?.full_name}`, 'green');
        log(`   Accessible: ${webinarResponse.data.isAccessible ? 'Oui' : 'Non'}`, webinarResponse.data.isAccessible ? 'green' : 'red');
      } else {
        log(`❌ API Webinar: Cours non trouvé`, 'red');
        return false;
      }
    } catch (error) {
      log(`❌ Erreur API Webinar: ${error.message}`, 'red');
      return false;
    }

    // Test 6: Vérifier la structure des données pour le frontend
    log('\n📱 Test 6: Structure des données pour frontend', 'yellow');
    try {
      const chatResponse = await axios.post(`${BASE_URL}/api/chatbot/ask`, {
        message: 'اقترح لي دورة أخرى في الرياضيات',
        userId: 3707,
        childId: 3707
      });
      
      const courseData = chatResponse.data.courseData;
      if (courseData) {
        const requiredFields = ['id', 'title', 'slug', 'teacher', 'chapters'];
        const missingFields = requiredFields.filter(field => !courseData[field]);
        
        if (missingFields.length === 0) {
          log(`✅ Frontend: Tous les champs requis présents`, 'green');
          log(`   ID: ${courseData.id}`, 'green');
          log(`   Titre: ${courseData.title}`, 'green');
          log(`   Slug: ${courseData.slug}`, 'green');
          log(`   Enseignant: ${courseData.teacher?.name || courseData.teacher?.full_name}`, 'green');
          log(`   Chapitres: ${courseData.chapters?.length || 0}`, 'green');
        } else {
          log(`❌ Frontend: Champs manquants: ${missingFields.join(', ')}`, 'red');
          return false;
        }
      } else {
        log(`❌ Frontend: Aucune donnée de cours`, 'red');
        return false;
      }
    } catch (error) {
      log(`❌ Erreur structure frontend: ${error.message}`, 'red');
      return false;
    }

    log('\n🎉 Tous les tests sont passés avec succès !', 'green');
    log('✅ Le système de recommandation de cours est entièrement fonctionnel', 'green');
    log('✅ La navigation progressive fonctionne', 'green');
    log('✅ Les données sont complètes pour le frontend', 'green');
    log('✅ L\'API webinar est accessible pour la navigation', 'green');
    
    return true;

  } catch (error) {
    log(`❌ Erreur générale: ${error.message}`, 'red');
    return false;
  }
}

// Exécuter les tests
if (require.main === module) {
  testCourseRecommendationSystem()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      log(`❌ Erreur fatale: ${error.message}`, 'red');
      process.exit(1);
    });
}

module.exports = { testCourseRecommendationSystem };
